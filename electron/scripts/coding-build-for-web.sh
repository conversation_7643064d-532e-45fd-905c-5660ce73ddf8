# Coding流水线配置的本地测试版本
# 目的是把一些能移动到web的文件移动到web下通过cdn来更新，避免发客户端包
# 这个脚本等合入master后再改流水线，没合入前要把下面脚本路径修改后直接配置到流水线里

#!/bin/bash

cd $QCI_WORKSPACE/electron
npm cache clean -f
npm install --registr=http://r.tnpm.oa.com
npm run build

# 获取脚本所在的目录
script_dir=$(cd "$(dirname "${BASH_SOURCE[0]}")" &> /dev/null && pwd)

# 构建相对于脚本所在目录的文件路径
board_html_path="$script_dir/../dist/board.html"
board_js_path="$script_dir/../dist/boardRenderer.bundle.js"

cp $board_html_path $script_dir/../../web/ui/dist/
cp $board_js_path $script_dir/../../web/ui/dist/
