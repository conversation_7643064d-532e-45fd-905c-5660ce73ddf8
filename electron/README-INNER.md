此文件不在npm官方上对外展示,

# todo
webpack文件copy不生效,需要处理,暂时先手动拷贝dist目录的view文件
npx webpack watch --config webpack.dev.js

electron 10.3 这两个接口被干掉了，所以用的是特殊版本
addIncludedShareWindow
removeAllIncludedShareWindow

如果要升级请跟新我们自己的方法


<!-- 1.7.4 之后版本 -- 流水线mac 环境还有问题
"trtc-electron-sdk": "10.9.405"
"electron": "11.5.0"
"electron-download": "4.1.1" -->




如果electron安装失败，可以通过配置镜像来安装三选一
npm config set ELECTRON_MIRROR="https://mirrors.tencent.com/npm_mirrors/electron/"
npm config set ELECTRON_MIRROR="https://npmmirror.com/mirrors/electron/"
npm config set ELECTRON_MIRROR="http://npm.taobao.org/mirrors/electron/"


新桌面端美颜插件:

不管mac还是win,ia32或x64,都统一封装为了内部npm包 xmagic-plugin-x

如需使用只需要pnpm i xmagic-plugin




旧:

## electron美颜插件说明（2023/08/16）
### 总目录 electron/plugin/XMagic
内部再分mac win目录对应不同系统

#### mac下资源结构 todo

#### win下资源结构
ia32: 32位dll
x64:  64位dll
res:  插件资源目录，这里实际包含很多目录， 为了节省目前删到只剩下3个（虚拟背景用到）； 其中cache目录不算，是动态产生的，可以删；

### 静态资源目录 electron/plugin_public
目前是虚拟背景图片使用

### 主要代码文件
1. electron/src/beautyUtil.ts
封装了beautyPlugin的主要方法，包括初始化，停止，应用等
2. electron/src/constants/*.js
封装了插件的基础配置信息，包括路径，参数等
