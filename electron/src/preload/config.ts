import { fetchPkgMeta, manualUpdates, setting } from '@/isomorphic/dep';
import './preload';

import { app, ipcRenderer, shell } from 'electron';


const callMainWithRet = (method: string, ...args: unknown[]) => {
  const payload = {
    method,
    args,
    retId: '',
  };
  const pass = JSON.parse(JSON.stringify(payload));
  // console.log('%c [ pass ]-16', 'font-size:13px; background:pink; color:#bf2c9f;', pass)
  return ipcRenderer.invoke('main-method-invoke', pass);
};

type Api = typeof setting & {
  manualUpdates: typeof manualUpdates;
  fetchPkgMeta: typeof fetchPkgMeta;
};

const configApi: any = {
  // restartApp: true,
  // fetcher api
  // manualUpdates: true,
  // fetchPkgMeta: true,
  // setting api
  ...setting,
  manualUpdates,
  fetchPkgMeta,
  ...Object.fromEntries(
    ['restartApp'].map(key => [
      key,
      (...args: any[]) => callMainWithRet(key, ...args),
    ])
  ),
  // fetchFinalConfig: true,
  // getApiConfig: true,
  // getLocalTestConfig: true,
  // getFinalConfig: true,
  // getMockResponse: true,
  // setMockResponse: true,
  // getApiResponse: true,
  // getLastSchoolId: true,
  // getLastUserId: true,
  // getResolvedPkg: true,
  // getWebVersion: true,
  // getWebBasePath: true,
  // setWebBasePath: true,
  // fetchWebUINpmIfNotEqual: true,
  // getWebEnv: true,
  // setWebEnv: true,
  // setHomeUrl: true,
  // getHomeUrl: true,
  // setLocalTestConfig: true,
  // getWebEnvPrefix: true,
  // getAllSettings: true,
};

// for (const key of Object.keys(configApi)) {
//   configApi[key] = ;
// }
// eslint-disable-next-line arrow-body-style
configApi.getAppVersion = () => {
  // process.env.APP_VERSION
  return app.getVersion();
};

configApi.openPath = (pathName = '') => {
  shell.openPath(pathName || process.env.APPDATA_DIR!);
};

(window as any).configApi = configApi;
