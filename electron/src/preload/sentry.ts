// import '@sentry/electron/preload';
import * as Sentry from '@sentry/electron/renderer';
import { commonOption } from '../isomorphic/sentryConfig';
import { ipcRenderer } from 'electron';
// import { EventToMain } from '@sentry/electron/renderer/integrations';

import {
  // defaultIntegrations,
  TryCatch,
  Breadcrumbs,
  GlobalHandlers,
  Dedupe,
} from '@sentry/browser';

import {
  Integrations as CoreIntegrations,
} from '@sentry/core';

const defaultIntegrations = [
  // new CoreIntegrations.InboundFilters(),
  new CoreIntegrations.FunctionToString(),
  new TryCatch(),
  new Breadcrumbs(),
  new GlobalHandlers(),
  // new LinkedErrors(),
  new Dedupe(),
  // new HttpContext(),
];

let inited = false;

const initSentry = async () => {
  _initSentry();
};

const _initSentry = async () => {
  // return;
  if (inited) {
    return;
  }
  const opts: Parameters<typeof Sentry.init>[0] = {
    ...commonOption,

    // not work.
    // initialScope: {
    //   tags: {
    //     'page': 'mainView',
    //   },
    // },
    // integrations: [new Sentry.BrowserTracing()],
    defaultIntegrations: false,
    integrations: [
      ...defaultIntegrations,
    ],
    // TODO: 检测页面是否有vue
    // https://docs.sentry.io/platforms/javascript/guides/vue/features/component-tracking/?original_referrer=https%3A%2F%2Fdocs.sentry.io%2Fproduct%2Fperformance%2F
    // 生产环境0.5, 性能采样本身也耗性能..
    // tracesSampleRate: process.env.NODE_ENV === 'production' ? 0 : 1.0,

    // Disable client reports for renderer as the sdk should only send
    // events using the main process.
    // sendClientReports: true,
  };
  Sentry.init(opts);
  inited = true;

  // const user = await ipcRenderer.invoke('getUserInfo');
  // Sentry.setUser({
  //   id: user.id,
  //   mac_address: user.mac_address,
  // });
  // Sentry.captureMessage('renderer-userid-updated.', 'info');
};

initSentry();

// (window as any).sentry = {
//   init: _initSentry,
//   // init() {
//   //   console.log('%c [  ]-81', 'font-size:13px; background:pink; color:#bf2c9f;',);
//   // }
// };
