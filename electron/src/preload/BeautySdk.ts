import { setting } from '@/isomorphic/dep';

import type * as TTRTC from 'trtc-electron-sdk';

// @ts-ignore
const nodeRequire: NodeRequire = typeof __webpack_require__ === 'function' ? __non_webpack_require__ : require;
const {
  TRTCVideoPixelFormat,
  TRTCVideoBufferType,
} = nodeRequire('trtc-electron-sdk') as typeof TTRTC;

// import { setting } from '@tencent/lcic-electron-hotupdater';

// import {
//   TUIBeautyCategory,
//   pluginInfo,
//   TUISegmentationConstant,
//   pluginName,
// } from 'xmagic-plugin-x';

interface PluginPkg {
  dlibPath: string;
  resBasePath: string;
}

interface ModuleXmagicPluginX {
  TUIBeautyCategory: {
    Beauty: number;
    BodyBeauty: number;
    Lut: number;
    Motion: number;
    Segmentation: number;
    Makeup: number;
  };
  pluginName: string;
  pluginInfo: PluginPkg;
  TUISegmentationConstant: Record<
  string,
  {
    effKey: string;
    resPath: string;
    imgPath: string;
    bgPath: string;
  }
  >;
  getTUISegmentationKeys(): string[];
}

function requireXmagicPlugin(): ModuleXmagicPluginX | null {
  try {
    const useXMagic = process.env.UseXMagic || 'YES';
    if (useXMagic === 'YES') {
      const mod = nodeRequire('xmagic-plugin-x');
      console.log(
        '%c [ 美颜插件 ]-6',
        'font-size:13px; background:pink; color:#bf2c9f;',
        mod
      );
      return mod;
    }
    return null;
  } catch (error) {
    return null;
    // if (
    //   (error instanceof Error && (error as any).code === 'MODULE_NOT_FOUND')
    // ) {
    // }
  }
}

// 获取美颜初始化参数：认证信息和资源目录文件
function getBeautyInitParam() {
  const mod = requireXmagicPlugin();
  if (!mod) {
    throw new Error('XmagicPlugin not load');
  }
  const hotParam = setting.getFinalConfig()?.BeautyParam || {};
  console.log(
    '%c [ hotParam ]-19',
    'font-size:13px; background:pink; color:#bf2c9f;',
    hotParam
  );
  const initParam = {
    // licenseURL: '', // 美颜证书需要构建【美颜特效SDK】https://cloud.tencent.com/product/x-magic
    licenseURL:
      process.env.XMagicLicenseURL || 'https://license.vod2.myqcloud.com/license/v2/1304753050_1/v_cube.license', // 美颜证书需要构建【美颜特效SDK】https://cloud.tencent.com/product/x-magic
    licenseKey: process.env.XMagicLicenseKey || '3c5e057c1b427229c639dc20c85b0451',
    // licenseURL: 'https://license.vod2.myqcloud.com/license/v2/1304753050_1/v_cube.license', // 美颜证书需要构建【美颜特效SDK】https://cloud.tencent.com/product/x-magic
    // licenseKey: '3c5e057c1b427229c639dc20c85b0451',
    resPath: mod.pluginInfo.resBasePath,
    ...hotParam,
  };
  return initParam;
}

async function delay(timeout: number): Promise<void> {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve();
    }, timeout);
  });
}

export class BeautySdk {
  private isPluginManagerOpen = false;
  private beautyPlugin: TTRTC.PluginInfo | null;
  private trtcCloud: TTRTC.default | null;

  setTrtc(trtc: TTRTC.default) {
    this.trtcCloud = trtc;
  }

  hasPlugin() {
    return !!requireXmagicPlugin();
  }

  // 开启插件管理器
  openPluginManager() {
    const mod = requireXmagicPlugin();
    if (!mod) {
      throw new Error('XmagicPlugin not support');
    }

    if (!this.trtcCloud) {
      throw new Error('need setTrtc first');
    }
    if (this.isPluginManagerOpen) {
      return;
    }
    console.log(
      '%c [ initPluginManager:]-117',
      'font-size:13px; background:pink; color:#bf2c9f;',
      {
        pixelFormat: TRTCVideoPixelFormat.TRTCVideoPixelFormat_BGRA32,
        bufferType: TRTCVideoBufferType.TRTCVideoBufferType_Buffer,
      }
    );
    this.trtcCloud.initPluginManager({
      pixelFormat: TRTCVideoPixelFormat.TRTCVideoPixelFormat_BGRA32,
      bufferType: TRTCVideoBufferType.TRTCVideoBufferType_Buffer,
    });
    this.trtcCloud.setPluginCallback((pluginId, errorCode, msg) => {
      console.log(
        `plugin here plugin: ${pluginId}, errorCode: ${errorCode}, msg: ${msg}`
      );
    });
    this.isPluginManagerOpen = true;
    return mod;
  }

  closePluginManager() {
    console.log(
      '%c [ closePluginManager ]-59',
      'font-size:13px; background:pink; color:#bf2c9f;',
      this.isPluginManagerOpen
    );
    if (this.isPluginManagerOpen) {
      this.trtcCloud?.destroyPluginManager();
    }
  }

  async enableBeautyPlugin() {
    if (this.beautyPlugin) {
      return;
    }
    const mod = this.openPluginManager();
    // this.trtcCloud.setExternalRenderEnabled(true);

    console.log(
      '%c [ pluginInfo.dlibPath ]-94',
      'font-size:13px; background:pink; color:#bf2c9f;',
      mod.pluginInfo.dlibPath
    );
    const beautyPlugin = this.trtcCloud!.addPlugin({
      id: mod.pluginName, // ID 可以随意设置，只要唯一、不重复就行
      path: mod.pluginInfo.dlibPath,
      type: 1,
    });

    const initParam = getBeautyInitParam();
    console.log(
      '%c [ initParam ]-98',
      'font-size:13px; background:pink; color:#bf2c9f;',
      initParam
    );
    beautyPlugin.setParameter(JSON.stringify(initParam));
    this.beautyPlugin = beautyPlugin;
    console.log(
      '%c [ beautyPlugin.enable() ]-93',
      'font-size:13px; background:pink; color:#bf2c9f;',
      beautyPlugin.enable()
    );
    await delay(3000);
  }

  async disableBeautyPlugin() {
    const mod = requireXmagicPlugin();
    if (!mod) {
      return;
    }
    if (this.beautyPlugin) {
      this.beautyPlugin.disable();
      this.trtcCloud?.removePlugin(this.beautyPlugin.id);
      this.beautyPlugin = null;
    }
    this.closePluginManager();
    this.isPluginManagerOpen = false;
  }

  /**
   * sceneKeyToLocalImgUrl
   */
  sceneKeyToLocalImgUrl(sceneKey: string): string {
    if (!sceneKey) {
      return '';
    }
    const mod = requireXmagicPlugin();
    if (!mod) {
      return '';
    }
    const bgPath = mod.TUISegmentationConstant[sceneKey]?.bgPath;
    if (!bgPath) {
      return '';
    }
    return encodeURI(`lcichs://lcicfile/${bgPath}`);
  }

  /**
   * getSceneKeys
   */
  getSceneKeys(): string[] {
    const mod = requireXmagicPlugin();
    if (!mod) {
      return [];
    }
    return mod.getTUISegmentationKeys();
  }

  async setVirtualImg(enable: boolean, url: string, sceneKey: string) {
    try {
      const mod = requireXmagicPlugin();
      if (!mod) {
        throw new Error('XmagicPlugin not support');
      }
      if (!enable) {
        await this.disableBeautyPlugin();
        return;
      }
      await this.enableBeautyPlugin();

      if (!(sceneKey && mod.TUISegmentationConstant[sceneKey])) {
        sceneKey = 'empty';
      }

      const { effKey, resPath, bgPath } = mod.TUISegmentationConstant[sceneKey];
      const param = JSON.stringify({
        beautySetting: [
          {
            category: mod.TUIBeautyCategory.Segmentation,
            effKey,
            resPath,
            bgPath,
          },
        ],
      });

      console.log(
        '%c [ param ]-114',
        'font-size:13px; background:pink; color:#bf2c9f;',
        param
      );
      this.beautyPlugin!.setParameter(param);
      this.beautyPlugin!.enable();
    } catch (error) {
      // 避免直接崩溃,先catch,再throw
      console.log(
        '%c [ error ]-138',
        'font-size:13px; background:pink; color:#bf2c9f;',
        error
      );
      throw error;
    }
  }
}
