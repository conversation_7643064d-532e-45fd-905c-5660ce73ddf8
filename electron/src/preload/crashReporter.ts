import electron, { crashReporter } from 'electron';

export const startCrashReporter = () => {
  // 渲染进程没有crashReporter.start方法
  // {
  // submitURL: '',
  // uploadToServer: false,
  // compress: false,
  // extra: {
  //   processType: 'renderer',
  // },
  // globalExtra: {
  //   // sdkversion: tcicSdkVersion,
  // },
  // const crashReports = {
  // last: crashReporter.getLastCrashReport(),
  // uploaded: crashReporter.getUploadedReports(),
  // };
  // if (!globalThis.dbg) {
  //   globalThis.dbg = {};
  // }
  // const { dbg } = globalThis;
  // dbg.crashReports = crashReports;
  // dbg.electron = electron;
};
