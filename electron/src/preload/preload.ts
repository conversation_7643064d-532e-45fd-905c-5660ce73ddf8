/* eslint-disable @typescript-eslint/no-require-imports */

// 渲染进程加载hotupdater alias.

const { ipc<PERSON><PERSON><PERSON> } = require('electron');
// @ts-ignore
const nodeRequire: NodeRequire =
  // @ts-ignore
  typeof __webpack_require__ === 'function' ? __non_webpack_require__ : require;

const env = process.env;
if (!env.APP_ASAR || !env.TCIC_HOTUPDATER_ALIAS) {
  const env = ipcRenderer.sendSync('get-env');
  console.log(
    '%c [ get main env ]-8',
    'font-size:13px; background:pink; color:#bf2c9f;',
    env,
    process.env
  );
  process.env = {
    ...env,
    ...process.env,
  };
}

console.log(
  '%c [ process.env.TCIC_HOTUPDATER_ALIAS ]-4',
  'font-size:13px; background:pink; color:#bf2c9f;',
  process.env.TCIC_HOTUPDATER_ALIAS
);
if (process.env.TCIC_HOTUPDATER_ALIAS) {
  nodeRequire(process.env.TCIC_HOTUPDATER_ALIAS).alias();
} else {
  // 不应使用硬编码路径.
  // nodeRequire(
  //   `${process.env.APP_ASAR}/node_modules/@tencent/lcic-electron-hotupdater/dist/alias`
  // ).alias();
  console.error('missing hotupdater alias:', process.env.TCIC_HOTUPDATER_ALIAS);
}
