// updater alias, 必须放在最前, 后续import @tencent包,可能实际为外网unscoped包

// @ts-ignore
const nodeRequire: NodeRequire =
  // @ts-ignore
  typeof __webpack_require__ === 'function' ? __non_webpack_require__ : require;

let updater = null;
try {
  updater = nodeRequire('@tencent/lcic-electron-hotupdater');
} catch (error) {
  if (!(error instanceof Error && (error as any).code === 'MODULE_NOT_FOUND')) {
    console.error(
      '%c [ error ]-12',
      'font-size:13px; background:pink; color:#bf2c9f;',
      error
    );
  }
  try {
    updater = nodeRequire('lcic-electron-hotupdater');
  } catch (error) {
    console.error('%c [ error ]-22', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    updater = nodeRequire('lcic-hotupdater');
  }
}

try {
  updater.initUpdate(false);
} catch (error) {
  console.error(
    '%c [ initUpdate error ]-24',
    'font-size:13px; background:pink; color:#bf2c9f;',
    error
  );
}
