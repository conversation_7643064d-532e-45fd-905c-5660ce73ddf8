import { checkForUpdates<PERSON><PERSON>, fetchPkgMeta, manualUpdates, setting } from '@/isomorphic/dep';
import { globalShortcut, ipcMain, BrowserWindow, app } from 'electron';
import path from 'path';

let configWin: BrowserWindow | null = null;

export const store = setting.getDefaultStore();

export function openConfigWinDevTools() {
  if (configWin) {
    configWin?.webContents?.openDevTools({
      mode: 'detach',
    });
  }
}

app.whenReady().then(() => {
  registerIPC();
  registerGlobalShortcut();
});

function registerGlobalShortcut() {
  checkForUpdatesCron();
  const conf = setting.getFinalConfig();
  const shortcut = conf?.configShortcut
    || 'CmdOrCtrl+Shift+F9';
  globalShortcut.unregister(shortcut);
  globalShortcut.register(shortcut, () => {
    if (configWin) {
      openConfigWinDevTools();
      return;
    }
    configWin = new BrowserWindow({
      minWidth: 800,
      minHeight: 600,
      show: true,
      // frame: false,
      // maximizable: false,  // 禁止双击标题栏最大化窗口
      // fullscreen: false,
      skipTaskbar: false,
      // title: this.windowTitle,
      backgroundColor: '#14181D',
      webPreferences: {
        nodeIntegration: true,
        // enableRemoteModule: true,
        backgroundThrottling: false, // 关闭后台节流功能，避免窗口不活动的时候不刷新
        contextIsolation: false,
        preload: path.join(__dirname, 'configPreload.bundle.js'),
        // webSecurity: false,
      },
    });
    configWin.setMenu(null);
    configWin.on('closed', () => {
      configWin = null;
    });
    configWin.loadURL(conf?.configUrl || 'http://localhost:5173/');
    // configWin.loadURL('http://localhost:5173/');
  });
}

const mainMethods: Record<string, any> = {
  ...setting,
  manualUpdates,
  fetchPkgMeta,
};

let ipc = false;
function registerIPC() {
  if (ipc) {
    return;
  }
  ipc = true;
  ipcMain.on('get-env', (event) => {
    event.returnValue = process.env;
  });
  ipcMain.handle(
    'main-method-invoke',
    async (
      event,
      payload: {
        method: string;
        args: any[];
      },
    ) => {
      console.log('%c [ payload ]-88', 'font-size:13px; background:pink; color:#bf2c9f;', payload);
      const { method, args } = payload;
      if (!mainMethods[method]) {
        return Promise.reject('no such method in main.');
      }
      // try {
      const ret = await mainMethods[method].apply(null, args);
      // console.log('%c [ ret ]-94', 'font-size:13px; background:pink; color:#bf2c9f;', ret);
      return ret;
      // } catch (error) {
      //   console.log('%c [ error ]-95', 'font-size:13px; background:pink; color:#bf2c9f;', error);
      //   throw new Error(JSON.stringify(error));
      // }
    },
  );
}
