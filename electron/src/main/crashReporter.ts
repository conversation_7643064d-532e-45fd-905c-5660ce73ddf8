import electron, { app, crashReporter } from 'electron';
import fs from 'fs';
import path from 'path';
import { tcicResourceDir } from './shared';

// 必须在app.ready()之前调
// TODO: 检查旧的设置,如果应用本身已设置,保持不变..
export const startCrashReporter = () => {
  // if (app.isReady()) {
  //   throw new Error('startCrashReporter should be called before app.ready');
  // }
  // before sentry.init()
  // app.setPath("userData", "~/.config/my-app");
  const dir = path.join(tcicResourceDir, 'crash');
  // console.log('crashDumps dir:', dir);
  // 用electron 默认crash 路径
  // app.setPath('crashDumps', dir);
  const crashDumpsDir = app.getPath('crashDumps');
  if (!fs.existsSync(crashDumpsDir)) {
    fs.mkdirSync(crashDumpsDir, { recursive: true });
  }
  console.log('crashDumpsDir:', crashDumpsDir);
  app.whenReady().then(() => {
    crashReporter.start({
      submitURL: '',
      uploadToServer: false,
      compress: false,
      ignoreSystemCrashHandler: true,
      extra: {
        processType: 'main',
      },
      globalExtra: {
        // sdkversion: tcicSdkVersion,
      },
    });
  });
  // const crashReports = {
  //   // last: crashReporter.getLastCrashReport(),
  //   // uploaded: crashReporter.getUploadedReports(),
  // };
  if (!globalThis.dbg) {
    globalThis.dbg = {};
  }
  const { dbg } = globalThis;
  // dbg.crashReports = crashReports;
  dbg.electron = electron;
};
