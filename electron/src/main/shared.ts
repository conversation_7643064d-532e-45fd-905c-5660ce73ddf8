import fs from 'fs';
import path from 'path';

let resdir: string;

(() => {
  // 检查并创建资源目录
  if (process.platform === 'win32') {
    resdir = path.join(process.env.APPDATA_DIR || process.env.USERPROFILE!, 'TencentCloudClass');
  } else {
    resdir = path.join(process.env.APPDATA_DIR || process.env.HOME!, 'TencentCloudClass');
  }
  if (!fs.existsSync(resdir)) {
    fs.mkdirSync(resdir, { recursive: true });
  }
})();

export const tcicResourceDir = resdir;
