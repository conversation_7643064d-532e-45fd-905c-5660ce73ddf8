import './initHotUpdater';
import  './sentry';
import { TIWCacheManager } from '../tiw-cache';
import { startCrashReporter } from './crashReporter';
import * as windowsUtil from '../windowsUtil';
import { app } from 'electron';

let booted = false;
// should be called before app.isReady()
export const bootstrap = () => {
  if (booted) {
    console.log('%c [ booted ]-11', 'font-size:13px; background:pink; color:#bf2c9f;', booted);
    return ;
  }

  // 避免electron源码的中的弹窗..
  process.on('uncaughtException', (error) => {
    console.error('uncaughtException:', error);
  });

  startCrashReporter();
  TIWCacheManager.RegisterAsPrivileged();

  // 命令行开关
  if (process.platform === 'win32') {
    // 在Windows分辨率较低的触摸设备上，禁用GPU合成，提高屏幕分享使用体验
    // 在win7上部分机器透明点击失效，禁用GPU合成
    const screenSize = windowsUtil.getPrimaryDisplaySize();
    if (screenSize.width * screenSize.height <= 1920 * 1080) {
      if (windowsUtil.getMaxTouches() > 1 || windowsUtil.isWin7()) {
        app.commandLine.appendSwitch('disable-gpu-compositing');
      }
    }
    // 在win7上部分机器打开PPT灰屏，关闭同源策略
    if (windowsUtil.isWin7()) {
      app.commandLine.appendSwitch('disable-site-isolation-trials');
    }
  }
  booted = true;
};

// TBD: 不应在import时,改为主动bootstrap, init
bootstrap();
