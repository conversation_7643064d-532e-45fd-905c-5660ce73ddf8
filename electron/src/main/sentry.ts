/* eslint-disable import/first */
/* eslint-disable @typescript-eslint/member-ordering */
// const rc = console;
import * as Sentry from '@sentry/electron/main';
import { CaptureConsole as CaptureConsoleIntegration } from '@sentry/integrations';
import { tcicSdkVersion } from '../version';
import { commonOption } from '../isomorphic/sentryConfig';
import { BrowserWindow, app, session } from 'electron';
import {
  ElectronMainOptions,
  IPCMode,
  Integrations,
} from '@sentry/electron/main';

import { defaultIntegrations as defaultNodeIntegrations } from '@sentry/node';

// import { classInstance } from './../main';
// TODO: refactor.
const classInstance: any = null;

// 摘自https://github.com/getsentry/sentry-electron/blob/HEAD/src/main/sdk.ts#L4-L5
const {
  SentryMinidump,
  ElectronBreadcrumbs,
  // Net,
  MainContext,
  ChildProcess,
  OnUncaughtException,
  PreloadInjection,
  AdditionalContext,
  Screenshots,
  MainProcessSession,
} = Integrations;

const defaults: any = [
  new SentryMinidump(),
  new ElectronBreadcrumbs(),
  // new Net(),
  new MainContext(),
  new ChildProcess(),
  new OnUncaughtException(),
  new PreloadInjection(),
  new AdditionalContext(),
  new Screenshots(),
  ...defaultNodeIntegrations.filter(integration => integration.name !== 'OnUncaughtException'
      && integration.name !== 'Context'),
];

// autoSessionTracking = false; Unless autoSessionTracking is specifically disabled, we track sessions as the
// lifetime of the Electron main process
defaults.push(new MainProcessSession());

// const checkClose = () => {
//   return function () {};
// };

const closeSentry = false;
const fakespan = {
  finish() {},
};

// export const rawConsole = {
//   warn: rc.warn.bind(rc),
//   log: rc.log.bind(rc),
//   debug: rc.debug.bind(rc),
//   info: rc.info.bind(rc),
//   error: rc.error.bind(rc),
//   group: rc.group.bind(rc),
//   groupEnd: rc.groupEnd.bind(rc),
// };

class SentryWrapper {
  private static inst: SentryWrapper;
  // 获取instance时初始化
  public static get instance() {
    // TODO: 在bootstrap中初始化
    if (!SentryWrapper.inst) {
      SentryWrapper.inst = new SentryWrapper();
      SentryWrapper.inst.init();
    }
    return SentryWrapper.inst;
  }
  private inited = false;

  private appStartUpTrans: Transaction | null = null;
  private appStartUpSpans: Span[] = [];

  private init() {
    if (closeSentry) {
      return;
    }
    if (this.inited) {
      return;
    }
    const initOpts: ElectronMainOptions = {
      ...commonOption,
      // debug: true,
      initialScope: {
        tags: {
          sdk: tcicSdkVersion,
          // 兼容旧tag
          'extra.sdk': tcicSdkVersion,
          appName: app.getName(),
          appVersion: app.getVersion(),
        },
        // mac地址
        // user: { mac_address: getMAC() },
      },
      getRendererName(contents: any) {
        if (contents.__sentryName) {
          return contents.__sentryName;
        }
        if (classInstance) {
          const win = BrowserWindow.fromWebContents(contents);
          const {
            tcicMainView,
            tcicBoardView,
            tcicScreenShareVideoWindow,
            tcicDefaultEntryMainWindow,
            tcicScreenShareMainWindow,
            tcicScreenShareBoardWindow,
            tcicScreenShareCameraWindow,
          } = classInstance.wins;
          switch (win) {
            case tcicMainView:
              return 'tcicMainView';
            case tcicBoardView:
              return 'tcicBoardView';
            case tcicScreenShareVideoWindow:
              return 'tcicScreenShareVideoWindow';
            case tcicDefaultEntryMainWindow:
              return 'tcicDefaultEntryMainWindow';
            case tcicScreenShareMainWindow:
              return 'tcicScreenShareMainWindow';
            case tcicScreenShareBoardWindow:
              return 'tcicScreenShareBoardWindow';
            case tcicScreenShareCameraWindow:
              return 'tcicScreenShareCameraWindow';
            default:
              break;
          }
        }
        return contents.getTitle();
      },

      // 要注入preload和protocol的session
      // https://docs.sentry.io/platforms/javascript/guides/electron/#preload-injection
      getSessions: () => [session.defaultSession],
      // IPCMode: IPCMode.Protocol,
      autoSessionTracking: false,
      defaultIntegrations: false,
      integrations: [...defaults],
      tracesSampleRate: 1.0,
      // beforeSend: () => {},
      // beforeSendTransaction(event, hint) {
      // },
    };
    if (process.env.NODE_ENV === 'production' || app.isPackaged) {
      if (Array.isArray(initOpts.integrations)) {
        initOpts.integrations.push(new CaptureConsoleIntegration({
          // array of methods that should be captured
          // defaults to ['log', 'info', 'warn', 'error', 'debug', 'assert']
          levels: ['error', 'warn', 'assert'],
        }) as any);
      }
    }
    Sentry.init(initOpts);
    const trans = Sentry.startTransaction({
      op: 'app-start',
      name: 'appStartUp',
      description: 'app启动时序',
      trimEnd: true,
    });
    this.appStartUpTrans = trans;
    const span = trans.startChild({
      op: 'app.ready',
      description: '从第一行代码到app.ready耗时',
    });
    // 统计app.ready时间, 获取单例锁时间, relunch时间(填timestamp), 创建窗口到 dom ready 时间
    app.whenReady().then(() => {
      span.finish();
    });

    Sentry.captureMessage('Sentry-inited.', 'info');
    this.inited = true;
  }

  public startUpAppChildSpan(spanCtx: SpanCtx) {
    if (closeSentry) {
      return fakespan;
    }
    const span = this.appStartUpTrans!.startChild(spanCtx);
    const rawFinish = span.finish.bind(span);
    span.finish = (...args) => {
      rawFinish(...args);
      this.finishAppStartUpTrans();
    };
    return span;
  }

  public finishAppStartUpTrans(endTimestamp?: number) {
    if (!this.appStartUpTrans) {
      return;
    }
    // TODO: ..
    // if (endTimestamp > Date.now()) {
    //   this.appStartUpTrans.finish(endTimestamp);
    //   return ;
    // }
    for (const span of this.appStartUpSpans) {
      if (!span.endTimestamp) {
        return;
      }
    }
    this.appStartUpTrans.finish(endTimestamp);
  }

  public startTransWithSpans(
    startTransArgs: StartTransArgs,
    ...spanCtxs: SpanCtx[]
  ): { trans: Transaction; spans: any[] } {
    if (closeSentry) {
      return {
        trans: null,
        spans: spanCtxs.map(() => fakespan),
      } as any;
    }
    const trans = Sentry.startTransaction(...startTransArgs);
    const spans: Span[] = [];
    for (const spanCtx of spanCtxs) {
      const span = trans.startChild(spanCtx);
      const rawFinish = span.finish.bind(span);
      span.finish = (...args) => {
        rawFinish(...args);
        for (const span of spans) {
          if (!span.endTimestamp) {
            return;
          }
        }
        trans.finish();
      };
      spans.push(span);
    }
    return {
      trans,
      spans,
    };
  }

  public setUserId(userId: string) {
    Sentry.setUser({ id: userId });
    Sentry.captureMessage('UserId-updated.', 'info');
  }

  public addClue(breadcrumb: Sentry.Breadcrumb) {
    Sentry.addBreadcrumb(breadcrumb);
  }

  public startTransaction(...args: StartTransArgs): Transaction {
    if (closeSentry) {
      return fakespan as any;
    }
    const trans = Sentry.startTransaction(...args);
    // TODO: wrap startChild
    return trans;
  }

  public captureMsg = Sentry.captureMessage;

  // @checkClose('')
  public captureError: typeof Sentry.captureException = function (
    this: SentryWrapper,
    err,
    ctx?,
  ) {
    if (!this.inited) {
      return '';
    }
    return Sentry.captureException(err, ctx);
  };

  // @checkClose(false)
  public closeAndFlush = async () => {
    if (!this.inited) {
      return true;
    }
    await Sentry.close(2000);
    this.inited = false;
    return true;
  };
}

// TODO: 上报cpu.https://docs.sentry.io/platforms/javascript/guides/electron/performance/instrumentation/performance-metrics/

// TODO: 关闭程序前, 等待所有事件flush.
// app.on('before-quit', async (event) => {
//   event.preventDefault();
//   await Sentry.close(2000);
//   app.quit();
// });

export const sentry = SentryWrapper.instance;

type StartTransArgs = Parameters<typeof Sentry.startTransaction>;
type Transaction = ReturnType<Sentry.Hub['startTransaction']>;

type StartSpan = ReturnType<Sentry.Hub['startTransaction']>['startChild'];
type SpanCtx = Parameters<StartSpan>[0];
type Span = ReturnType<StartSpan>;
