/* eslint-disable */
/**
 * 主动更新逻辑
 */
/**
 * 只允许注册一次更新地址
 * webpack打包还有问题，没有拷贝需要的文件到dist目录,view目录需要被拷贝到dist
 */
import { autoUpdater } from "electron-updater";
import * as path from "path";
import * as fs from "fs";
let FEED_URL: string | null = null;
type UpdateEventName =
  | "error"
  | "checking-for-update"
  | "update-available"
  | "update-not-available"
  | "download-progress"
  | "update-downloaded";

type EventHandler = {
  name: UpdateEventName;
  handler: any;
};
/** **
 *
 * 更新提醒 - Update Alert
立即更新 - Update Now
稍后再说 - Later
已更新至最新版本，点击重新启动使用最新版 - Updated to the latest version, click to restart
正在下载安装包 - Downloading Installer
重新启动 - Restart
 *
 *
 */

/**
 * 加载视图文件
 * @param {} path4view
 * @returns
 */
function loadView(path4view: string) {
  const content = fs.readFileSync(path.resolve(__dirname, "view", path4view));
  return content.toString();
}

/**
 * 获取模板
 */
const tplStr = JSON.stringify({ tpl: loadView("update.html") });
const updatingConfig = {
  isUpdating: false,
};

/**
 * 浏览器界面注入脚本
 * "#replace#"作为传参更新
 */
export function insertUpdateDom() {
  const wraper = document.createElement("DIV");
  const loadTextObj: any = "#replace#";
  const loadText = loadTextObj.tpl;
  /**
   * todo: 需要更严谨的匹配符，如何代码使用异或操作会引起问题
   * 暂时先用异或作为判断。
   */
  const getJs = /<script>([^\^]+)<\/script>/gi.exec(loadText);
  wraper.innerHTML = loadText;
  document.body.appendChild(wraper);
  if (getJs) {
    eval(getJs[1]);
  }
}

/**
 * 获取web端执行的脚本字符串
 * @param fn
 * @returns
 */
export function getWebjs(fn: any, arg: string) {
  let result = `${fn.toString().replace('"#replace#"', arg)};${fn.name}()`;
  return result;
}
/**
 * 设置更新地址，并绑定事件
 * @param param
 * @returns
 */
export function setUpdateUrl(param: {
  url: string;
  notifiView?: Electron.BrowserView["webContents"];
  cb?: (eventName: UpdateEventName, arg?: any) => any;
}) {
  /**
   * 渲染视图可以刷新
   * 通知的渲染视图执行js
   * 非课堂进入需要,课堂进入会使用外部
   * @param fnName
   */
  const notifiViewAction = (function () {
    let isInit = false;
    return (fnName: any) => {
      if (!isInit) {
        isInit = true;
        if (param.notifiView) {
          return param.notifiView.executeJavaScript(getWebjs(fnName, tplStr));
        }
      }
      return new Promise((resolve) => {
        resolve(false);
      });
    };
  })();
  /**
   * 渲染层消息发送
   * @param eventName
   * @param param
   */
  const classInstanceSendMsg = function (eventName: string, data?: any) {
    // console.log('param.notifiView:',param.notifiView);
    param.notifiView && param.notifiView.send("message", eventName, data);
  };
  FEED_URL = param.url;

  /**
   * 注入更新界面
   */
  autoUpdater.setFeedURL(FEED_URL);
  autoUpdater.autoDownload = false; // 禁止自动下载
  autoUpdater.autoInstallOnAppQuit = false; // 禁止静默安装
  const eventLists: EventHandler[] = [
    {
      name: "error",
      handler(error: any) {
        console.log("-->checking-for-update =>", error);
        notifiViewAction(insertUpdateDom).then(() => {
          classInstanceSendMsg("error", error);
        });
        console.log("will call cb", param.cb);
        param.cb && param.cb("error", error);
      },
    },
    {
      name: "checking-for-update",
      handler(info: any) {
        notifiViewAction(insertUpdateDom)
          .then(() => {
            console.log(
              "-->checking-for-update checking-for-updatingConfig.isUpdating",
              updatingConfig.isUpdating
            );
            classInstanceSendMsg("checking-for-update", info);
          })
          .catch((err) => {
            console.log("checking-for-update:", err);
          });
        param.cb && param.cb("checking-for-update", info);
      },
    },
    {
      name: "update-available",
      handler(info: any) {
        notifiViewAction(insertUpdateDom)
          .then(async () => {
            classInstanceSendMsg("update-available", info);
            console.log("-->checking-for-update ::update-available=>", info, param.cb);
            if (param.cb) {
              const confirmResult = await param.cb("update-available", info);
              console.log("-->checking-for-update ::confirmResult=>", confirmResult);
              if (confirmResult) {
                console.log(
                  "-->checking-for-update ::confirmResult=> isUpdating",
                  updatingConfig.isUpdating
                );
                autoUpdater.downloadUpdate();
              }
            }
          })
          .catch((err) => {
            console.log("-->checking-for-update ------->notifiViewAction err ", err);
            console.error(err);
          });
      },
    },
    {
      name: "update-not-available",
      handler(info: any) {
        notifiViewAction(insertUpdateDom).then(async () => {
          classInstanceSendMsg("update-not-available", info);
          console.log("-->checking-for-update update-not-available ---->>> ", info);
          param.cb && param.cb("update-not-available", info);
        });
      },
    },
    {
      name: "download-progress",
      handler(progressObj: any) {
        console.log("download-progress::----", progressObj);
        classInstanceSendMsg("download-progress", progressObj);
        param.cb && param.cb("download-progress", progressObj);
      },
    },
    {
      name: "update-downloaded",
      async handler(
        event: any,
        releaseNotes: any,
        releaseName: any,
        releaseDate: any,
        updateUrl: any,
        quitAndUpdate: any
      ) {
        const opts = {
          releaseNotes,
          releaseName,
          releaseDate,
          updateUrl,
          quitAndUpdate,
        };
        if (param.cb) {
          const confirmResult = await param.cb("update-downloaded", opts);
          if (confirmResult) {
            quitAndInstall();
          }
        }
        /**
         * 下载完成允许弹窗自动关闭
         */
        updatingConfig.isUpdating = true;
        classInstanceSendMsg("update-downloaded", opts);
      },
    },
  ];
  eventLists.forEach((item) => {
    autoUpdater.on(item.name, item.handler);
  });
}

/**
 * 检查更新
 * @returns
 */
export function checkUpdate() {
  console.log("checkUpdate fn:----------->", FEED_URL);
  if (!FEED_URL) {
    console.error("Need setUpdateUrl");
    return;
  }
  autoUpdater.checkForUpdates();
}

const USER_HOME = process.env.HOME || process.env.USERPROFILE;
// 清理更新遗留cache
function clearUpdateCache() {
  try {
    let result;
    if (process.platform === "win32") {
      result = path.join(
        process.env.LOCALAPPDATA || path.join(USER_HOME, "AppData", "Local"),
        "TuritoWhiteboard-updater"
      );
    } else if (process.platform === "darwin") {
      result = path.join(
        require("os").homedir(),
        "Library",
        "Application Support",
        "Caches",
        "TuritoWhiteboard-updater"
      );
    }
    if (result && fs.existsSync(result)) {
      console.info("MAIN::clearUpdateCache do...", result);
      fs.rmdir(result, { recursive: true }, () => {});
    }
  } catch (e) {
    console.error("MAIN::clearUpdateCache error", e);
  }
}

/**
 * 唤起下载
 */
export function downloadUpdate() {
  console.log("downloadUpdate----> call");
  autoUpdater.downloadUpdate();
}

/**
 * 判断是否正在更新
 * @returns boolean
 */
export function getUpdateStatus() {
  return updatingConfig.isUpdating;
}
/**
 * 退出并且下载
 */
export function quitAndInstall() {
  console.log("quitAndInstall----> call");
  /**
   * Restarts the app and installs the update after it has been downloaded.
   * It should only be called after update-downloaded has been emitted.
   * Note: autoUpdater.quitAndInstall() will close all application windows first
   * and only emit before-quit event on app after that.
   * This is different from the normal quit event sequence.
   * @param isSilent — windows-only Runs the installer in silent mode. Defaults to false.
   *  @param isForceRunAfter — Run the app after finish even on silent install.
   * Not applicable for macOS. Ignored if isSilent is set to false.
   */
  autoUpdater.quitAndInstall();
}
