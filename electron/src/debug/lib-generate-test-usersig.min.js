/*eslint-disable*/
!function(e,t){"object"==typeof exports&&"undefined"!=typeof module?module.exports=t():"function"==typeof define&&define.amd?define(t):(e=e||self).LibGenerateTestUserSig=t()}(this,function(){"use strict";var e="undefined"!=typeof global?global:"undefined"!=typeof self?self:"undefined"!=typeof window?window:{},t=[],r=[],n="undefined"!=typeof Uint8Array?Uint8Array:Array,i=!1;function o(){i=!0;for(var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",n=0,o=e.length;n<o;++n)t[n]=e[n],r[e.charCodeAt(n)]=n;r["-".charCodeAt(0)]=62,r["_".charCodeAt(0)]=63}function a(e,r,n){for(var i,o,a=[],s=r;s<n;s+=3)i=(e[s]<<16)+(e[s+1]<<8)+e[s+2],a.push(t[(o=i)>>18&63]+t[o>>12&63]+t[o>>6&63]+t[63&o]);return a.join("")}function s(e){var r;i||o();for(var n=e.length,s=n%3,h="",f=[],l=0,c=n-s;l<c;l+=16383)f.push(a(e,l,l+16383>c?c:l+16383));return 1===s?(r=e[n-1],h+=t[r>>2],h+=t[r<<4&63],h+="=="):2===s&&(r=(e[n-2]<<8)+e[n-1],h+=t[r>>10],h+=t[r>>4&63],h+=t[r<<2&63],h+="="),f.push(h),f.join("")}function h(e,t,r,n,i){var o,a,s=8*i-n-1,h=(1<<s)-1,f=h>>1,l=-7,c=r?i-1:0,u=r?-1:1,d=e[t+c];for(c+=u,o=d&(1<<-l)-1,d>>=-l,l+=s;l>0;o=256*o+e[t+c],c+=u,l-=8);for(a=o&(1<<-l)-1,o>>=-l,l+=n;l>0;a=256*a+e[t+c],c+=u,l-=8);if(0===o)o=1-f;else{if(o===h)return a?NaN:1/0*(d?-1:1);a+=Math.pow(2,n),o-=f}return(d?-1:1)*a*Math.pow(2,o-n)}function f(e,t,r,n,i,o){var a,s,h,f=8*o-i-1,l=(1<<f)-1,c=l>>1,u=23===i?Math.pow(2,-24)-Math.pow(2,-77):0,d=n?0:o-1,p=n?1:-1,_=t<0||0===t&&1/t<0?1:0;for(t=Math.abs(t),isNaN(t)||t===1/0?(s=isNaN(t)?1:0,a=l):(a=Math.floor(Math.log(t)/Math.LN2),t*(h=Math.pow(2,-a))<1&&(a--,h*=2),(t+=a+c>=1?u/h:u*Math.pow(2,1-c))*h>=2&&(a++,h/=2),a+c>=l?(s=0,a=l):a+c>=1?(s=(t*h-1)*Math.pow(2,i),a+=c):(s=t*Math.pow(2,c-1)*Math.pow(2,i),a=0));i>=8;e[r+d]=255&s,d+=p,s/=256,i-=8);for(a=a<<i|s,f+=i;f>0;e[r+d]=255&a,d+=p,a/=256,f-=8);e[r+d-p]|=128*_}var l={}.toString,c=Array.isArray||function(e){return"[object Array]"==l.call(e)};function u(){return p.TYPED_ARRAY_SUPPORT?2147483647:1073741823}function d(e,t){if(u()<t)throw new RangeError("Invalid typed array length");return p.TYPED_ARRAY_SUPPORT?(e=new Uint8Array(t)).__proto__=p.prototype:(null===e&&(e=new p(t)),e.length=t),e}function p(e,t,r){if(!(p.TYPED_ARRAY_SUPPORT||this instanceof p))return new p(e,t,r);if("number"==typeof e){if("string"==typeof t)throw new Error("If encoding is specified then the first argument must be a string");return v(this,e)}return _(this,e,t,r)}function _(e,t,r,n){if("number"==typeof t)throw new TypeError('"value" argument must not be a number');return"undefined"!=typeof ArrayBuffer&&t instanceof ArrayBuffer?function(e,t,r,n){if(t.byteLength,r<0||t.byteLength<r)throw new RangeError("'offset' is out of bounds");if(t.byteLength<r+(n||0))throw new RangeError("'length' is out of bounds");t=void 0===r&&void 0===n?new Uint8Array(t):void 0===n?new Uint8Array(t,r):new Uint8Array(t,r,n);p.TYPED_ARRAY_SUPPORT?(e=t).__proto__=p.prototype:e=w(e,t);return e}(e,t,r,n):"string"==typeof t?function(e,t,r){"string"==typeof r&&""!==r||(r="utf8");if(!p.isEncoding(r))throw new TypeError('"encoding" must be a valid string encoding');var n=0|m(t,r),i=(e=d(e,n)).write(t,r);i!==n&&(e=e.slice(0,i));return e}(e,t,r):function(e,t){if(y(t)){var r=0|b(t.length);return 0===(e=d(e,r)).length?e:(t.copy(e,0,0,r),e)}if(t){if("undefined"!=typeof ArrayBuffer&&t.buffer instanceof ArrayBuffer||"length"in t)return"number"!=typeof t.length||(n=t.length)!=n?d(e,0):w(e,t);if("Buffer"===t.type&&c(t.data))return w(e,t.data)}var n;throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}(e,t)}function g(e){if("number"!=typeof e)throw new TypeError('"size" argument must be a number');if(e<0)throw new RangeError('"size" argument must not be negative')}function v(e,t){if(g(t),e=d(e,t<0?0:0|b(t)),!p.TYPED_ARRAY_SUPPORT)for(var r=0;r<t;++r)e[r]=0;return e}function w(e,t){var r=t.length<0?0:0|b(t.length);e=d(e,r);for(var n=0;n<r;n+=1)e[n]=255&t[n];return e}function b(e){if(e>=u())throw new RangeError("Attempt to allocate Buffer larger than maximum size: 0x"+u().toString(16)+" bytes");return 0|e}function y(e){return!(null==e||!e._isBuffer)}function m(e,t){if(y(e))return e.length;if("undefined"!=typeof ArrayBuffer&&"function"==typeof ArrayBuffer.isView&&(ArrayBuffer.isView(e)||e instanceof ArrayBuffer))return e.byteLength;"string"!=typeof e&&(e=""+e);var r=e.length;if(0===r)return 0;for(var n=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":case void 0:return q(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return V(e).length;default:if(n)return q(e).length;t=(""+t).toLowerCase(),n=!0}}function k(e,t,r){var n=!1;if((void 0===t||t<0)&&(t=0),t>this.length)return"";if((void 0===r||r>this.length)&&(r=this.length),r<=0)return"";if((r>>>=0)<=(t>>>=0))return"";for(e||(e="utf8");;)switch(e){case"hex":return O(this,t,r);case"utf8":case"utf-8":return C(this,t,r);case"ascii":return I(this,t,r);case"latin1":case"binary":return P(this,t,r);case"base64":return M(this,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return U(this,t,r);default:if(n)throw new TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),n=!0}}function E(e,t,r){var n=e[t];e[t]=e[r],e[r]=n}function S(e,t,r,n,i){if(0===e.length)return-1;if("string"==typeof r?(n=r,r=0):r>2147483647?r=2147483647:r<-2147483648&&(r=-2147483648),r=+r,isNaN(r)&&(r=i?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(i)return-1;r=e.length-1}else if(r<0){if(!i)return-1;r=0}if("string"==typeof t&&(t=p.from(t,n)),y(t))return 0===t.length?-1:x(e,t,r,n,i);if("number"==typeof t)return t&=255,p.TYPED_ARRAY_SUPPORT&&"function"==typeof Uint8Array.prototype.indexOf?i?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):x(e,[t],r,n,i);throw new TypeError("val must be string, number or Buffer")}function x(e,t,r,n,i){var o,a=1,s=e.length,h=t.length;if(void 0!==n&&("ucs2"===(n=String(n).toLowerCase())||"ucs-2"===n||"utf16le"===n||"utf-16le"===n)){if(e.length<2||t.length<2)return-1;a=2,s/=2,h/=2,r/=2}function f(e,t){return 1===a?e[t]:e.readUInt16BE(t*a)}if(i){var l=-1;for(o=r;o<s;o++)if(f(e,o)===f(t,-1===l?0:o-l)){if(-1===l&&(l=o),o-l+1===h)return l*a}else-1!==l&&(o-=o-l),l=-1}else for(r+h>s&&(r=s-h),o=r;o>=0;o--){for(var c=!0,u=0;u<h;u++)if(f(e,o+u)!==f(t,u)){c=!1;break}if(c)return o}return-1}function R(e,t,r,n){r=Number(r)||0;var i=e.length-r;n?(n=Number(n))>i&&(n=i):n=i;var o=t.length;if(o%2!=0)throw new TypeError("Invalid hex string");n>o/2&&(n=o/2);for(var a=0;a<n;++a){var s=parseInt(t.substr(2*a,2),16);if(isNaN(s))return a;e[r+a]=s}return a}function A(e,t,r,n){return G(q(t,e.length-r),e,r,n)}function B(e,t,r,n){return G(function(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}(t),e,r,n)}function z(e,t,r,n){return B(e,t,r,n)}function L(e,t,r,n){return G(V(t),e,r,n)}function T(e,t,r,n){return G(function(e,t){for(var r,n,i,o=[],a=0;a<e.length&&!((t-=2)<0);++a)r=e.charCodeAt(a),n=r>>8,i=r%256,o.push(i),o.push(n);return o}(t,e.length-r),e,r,n)}function M(e,t,r){return 0===t&&r===e.length?s(e):s(e.slice(t,r))}function C(e,t,r){r=Math.min(e.length,r);for(var n=[],i=t;i<r;){var o,a,s,h,f=e[i],l=null,c=f>239?4:f>223?3:f>191?2:1;if(i+c<=r)switch(c){case 1:f<128&&(l=f);break;case 2:128==(192&(o=e[i+1]))&&(h=(31&f)<<6|63&o)>127&&(l=h);break;case 3:o=e[i+1],a=e[i+2],128==(192&o)&&128==(192&a)&&(h=(15&f)<<12|(63&o)<<6|63&a)>2047&&(h<55296||h>57343)&&(l=h);break;case 4:o=e[i+1],a=e[i+2],s=e[i+3],128==(192&o)&&128==(192&a)&&128==(192&s)&&(h=(15&f)<<18|(63&o)<<12|(63&a)<<6|63&s)>65535&&h<1114112&&(l=h)}null===l?(l=65533,c=1):l>65535&&(l-=65536,n.push(l>>>10&1023|55296),l=56320|1023&l),n.push(l),i+=c}return function(e){var t=e.length;if(t<=D)return String.fromCharCode.apply(String,e);var r="",n=0;for(;n<t;)r+=String.fromCharCode.apply(String,e.slice(n,n+=D));return r}(n)}p.TYPED_ARRAY_SUPPORT=void 0===e.TYPED_ARRAY_SUPPORT||e.TYPED_ARRAY_SUPPORT,p.poolSize=8192,p._augment=function(e){return e.__proto__=p.prototype,e},p.from=function(e,t,r){return _(null,e,t,r)},p.TYPED_ARRAY_SUPPORT&&(p.prototype.__proto__=Uint8Array.prototype,p.__proto__=Uint8Array),p.alloc=function(e,t,r){return function(e,t,r,n){return g(t),t<=0?d(e,t):void 0!==r?"string"==typeof n?d(e,t).fill(r,n):d(e,t).fill(r):d(e,t)}(null,e,t,r)},p.allocUnsafe=function(e){return v(null,e)},p.allocUnsafeSlow=function(e){return v(null,e)},p.isBuffer=$,p.compare=function(e,t){if(!y(e)||!y(t))throw new TypeError("Arguments must be Buffers");if(e===t)return 0;for(var r=e.length,n=t.length,i=0,o=Math.min(r,n);i<o;++i)if(e[i]!==t[i]){r=e[i],n=t[i];break}return r<n?-1:n<r?1:0},p.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},p.concat=function(e,t){if(!c(e))throw new TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return p.alloc(0);var r;if(void 0===t)for(t=0,r=0;r<e.length;++r)t+=e[r].length;var n=p.allocUnsafe(t),i=0;for(r=0;r<e.length;++r){var o=e[r];if(!y(o))throw new TypeError('"list" argument must be an Array of Buffers');o.copy(n,i),i+=o.length}return n},p.byteLength=m,p.prototype._isBuffer=!0,p.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw new RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)E(this,t,t+1);return this},p.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw new RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)E(this,t,t+3),E(this,t+1,t+2);return this},p.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw new RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)E(this,t,t+7),E(this,t+1,t+6),E(this,t+2,t+5),E(this,t+3,t+4);return this},p.prototype.toString=function(){var e=0|this.length;return 0===e?"":0===arguments.length?C(this,0,e):k.apply(this,arguments)},p.prototype.equals=function(e){if(!y(e))throw new TypeError("Argument must be a Buffer");return this===e||0===p.compare(this,e)},p.prototype.inspect=function(){var e="";return this.length>0&&(e=this.toString("hex",0,50).match(/.{2}/g).join(" "),this.length>50&&(e+=" ... ")),"<Buffer "+e+">"},p.prototype.compare=function(e,t,r,n,i){if(!y(e))throw new TypeError("Argument must be a Buffer");if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===n&&(n=0),void 0===i&&(i=this.length),t<0||r>e.length||n<0||i>this.length)throw new RangeError("out of range index");if(n>=i&&t>=r)return 0;if(n>=i)return-1;if(t>=r)return 1;if(this===e)return 0;for(var o=(i>>>=0)-(n>>>=0),a=(r>>>=0)-(t>>>=0),s=Math.min(o,a),h=this.slice(n,i),f=e.slice(t,r),l=0;l<s;++l)if(h[l]!==f[l]){o=h[l],a=f[l];break}return o<a?-1:a<o?1:0},p.prototype.includes=function(e,t,r){return-1!==this.indexOf(e,t,r)},p.prototype.indexOf=function(e,t,r){return S(this,e,t,r,!0)},p.prototype.lastIndexOf=function(e,t,r){return S(this,e,t,r,!1)},p.prototype.write=function(e,t,r,n){if(void 0===t)n="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)n=t,r=this.length,t=0;else{if(!isFinite(t))throw new Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");t|=0,isFinite(r)?(r|=0,void 0===n&&(n="utf8")):(n=r,r=void 0)}var i=this.length-t;if((void 0===r||r>i)&&(r=i),e.length>0&&(r<0||t<0)||t>this.length)throw new RangeError("Attempt to write outside buffer bounds");n||(n="utf8");for(var o=!1;;)switch(n){case"hex":return R(this,e,t,r);case"utf8":case"utf-8":return A(this,e,t,r);case"ascii":return B(this,e,t,r);case"latin1":case"binary":return z(this,e,t,r);case"base64":return L(this,e,t,r);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return T(this,e,t,r);default:if(o)throw new TypeError("Unknown encoding: "+n);n=(""+n).toLowerCase(),o=!0}},p.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}};var D=4096;function I(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(127&e[i]);return n}function P(e,t,r){var n="";r=Math.min(e.length,r);for(var i=t;i<r;++i)n+=String.fromCharCode(e[i]);return n}function O(e,t,r){var n=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>n)&&(r=n);for(var i="",o=t;o<r;++o)i+=X(e[o]);return i}function U(e,t,r){for(var n=e.slice(t,r),i="",o=0;o<n.length;o+=2)i+=String.fromCharCode(n[o]+256*n[o+1]);return i}function H(e,t,r){if(e%1!=0||e<0)throw new RangeError("offset is not uint");if(e+t>r)throw new RangeError("Trying to access beyond buffer length")}function F(e,t,r,n,i,o){if(!y(e))throw new TypeError('"buffer" argument must be a Buffer instance');if(t>i||t<o)throw new RangeError('"value" argument is out of bounds');if(r+n>e.length)throw new RangeError("Index out of range")}function N(e,t,r,n){t<0&&(t=65535+t+1);for(var i=0,o=Math.min(e.length-r,2);i<o;++i)e[r+i]=(t&255<<8*(n?i:1-i))>>>8*(n?i:1-i)}function Z(e,t,r,n){t<0&&(t=4294967295+t+1);for(var i=0,o=Math.min(e.length-r,4);i<o;++i)e[r+i]=t>>>8*(n?i:3-i)&255}function j(e,t,r,n,i,o){if(r+n>e.length)throw new RangeError("Index out of range");if(r<0)throw new RangeError("Index out of range")}function W(e,t,r,n,i){return i||j(e,0,r,4),f(e,t,r,n,23,4),r+4}function Y(e,t,r,n,i){return i||j(e,0,r,8),f(e,t,r,n,52,8),r+8}p.prototype.slice=function(e,t){var r,n=this.length;if((e=~~e)<0?(e+=n)<0&&(e=0):e>n&&(e=n),(t=void 0===t?n:~~t)<0?(t+=n)<0&&(t=0):t>n&&(t=n),t<e&&(t=e),p.TYPED_ARRAY_SUPPORT)(r=this.subarray(e,t)).__proto__=p.prototype;else{var i=t-e;r=new p(i,void 0);for(var o=0;o<i;++o)r[o]=this[o+e]}return r},p.prototype.readUIntLE=function(e,t,r){e|=0,t|=0,r||H(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n},p.prototype.readUIntBE=function(e,t,r){e|=0,t|=0,r||H(e,t,this.length);for(var n=this[e+--t],i=1;t>0&&(i*=256);)n+=this[e+--t]*i;return n},p.prototype.readUInt8=function(e,t){return t||H(e,1,this.length),this[e]},p.prototype.readUInt16LE=function(e,t){return t||H(e,2,this.length),this[e]|this[e+1]<<8},p.prototype.readUInt16BE=function(e,t){return t||H(e,2,this.length),this[e]<<8|this[e+1]},p.prototype.readUInt32LE=function(e,t){return t||H(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},p.prototype.readUInt32BE=function(e,t){return t||H(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},p.prototype.readIntLE=function(e,t,r){e|=0,t|=0,r||H(e,t,this.length);for(var n=this[e],i=1,o=0;++o<t&&(i*=256);)n+=this[e+o]*i;return n>=(i*=128)&&(n-=Math.pow(2,8*t)),n},p.prototype.readIntBE=function(e,t,r){e|=0,t|=0,r||H(e,t,this.length);for(var n=t,i=1,o=this[e+--n];n>0&&(i*=256);)o+=this[e+--n]*i;return o>=(i*=128)&&(o-=Math.pow(2,8*t)),o},p.prototype.readInt8=function(e,t){return t||H(e,1,this.length),128&this[e]?-1*(255-this[e]+1):this[e]},p.prototype.readInt16LE=function(e,t){t||H(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},p.prototype.readInt16BE=function(e,t){t||H(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},p.prototype.readInt32LE=function(e,t){return t||H(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},p.prototype.readInt32BE=function(e,t){return t||H(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},p.prototype.readFloatLE=function(e,t){return t||H(e,4,this.length),h(this,e,!0,23,4)},p.prototype.readFloatBE=function(e,t){return t||H(e,4,this.length),h(this,e,!1,23,4)},p.prototype.readDoubleLE=function(e,t){return t||H(e,8,this.length),h(this,e,!0,52,8)},p.prototype.readDoubleBE=function(e,t){return t||H(e,8,this.length),h(this,e,!1,52,8)},p.prototype.writeUIntLE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||F(this,e,t,r,Math.pow(2,8*r)-1,0);var i=1,o=0;for(this[t]=255&e;++o<r&&(i*=256);)this[t+o]=e/i&255;return t+r},p.prototype.writeUIntBE=function(e,t,r,n){(e=+e,t|=0,r|=0,n)||F(this,e,t,r,Math.pow(2,8*r)-1,0);var i=r-1,o=1;for(this[t+i]=255&e;--i>=0&&(o*=256);)this[t+i]=e/o&255;return t+r},p.prototype.writeUInt8=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,1,255,0),p.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),this[t]=255&e,t+1},p.prototype.writeUInt16LE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,2,65535,0),p.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):N(this,e,t,!0),t+2},p.prototype.writeUInt16BE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,2,65535,0),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):N(this,e,t,!1),t+2},p.prototype.writeUInt32LE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,4,4294967295,0),p.TYPED_ARRAY_SUPPORT?(this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e):Z(this,e,t,!0),t+4},p.prototype.writeUInt32BE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,4,4294967295,0),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):Z(this,e,t,!1),t+4},p.prototype.writeIntLE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);F(this,e,t,r,i-1,-i)}var o=0,a=1,s=0;for(this[t]=255&e;++o<r&&(a*=256);)e<0&&0===s&&0!==this[t+o-1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},p.prototype.writeIntBE=function(e,t,r,n){if(e=+e,t|=0,!n){var i=Math.pow(2,8*r-1);F(this,e,t,r,i-1,-i)}var o=r-1,a=1,s=0;for(this[t+o]=255&e;--o>=0&&(a*=256);)e<0&&0===s&&0!==this[t+o+1]&&(s=1),this[t+o]=(e/a>>0)-s&255;return t+r},p.prototype.writeInt8=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,1,127,-128),p.TYPED_ARRAY_SUPPORT||(e=Math.floor(e)),e<0&&(e=255+e+1),this[t]=255&e,t+1},p.prototype.writeInt16LE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,2,32767,-32768),p.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8):N(this,e,t,!0),t+2},p.prototype.writeInt16BE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,2,32767,-32768),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>8,this[t+1]=255&e):N(this,e,t,!1),t+2},p.prototype.writeInt32LE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,4,2147483647,-2147483648),p.TYPED_ARRAY_SUPPORT?(this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24):Z(this,e,t,!0),t+4},p.prototype.writeInt32BE=function(e,t,r){return e=+e,t|=0,r||F(this,e,t,4,2147483647,-2147483648),e<0&&(e=4294967295+e+1),p.TYPED_ARRAY_SUPPORT?(this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e):Z(this,e,t,!1),t+4},p.prototype.writeFloatLE=function(e,t,r){return W(this,e,t,!0,r)},p.prototype.writeFloatBE=function(e,t,r){return W(this,e,t,!1,r)},p.prototype.writeDoubleLE=function(e,t,r){return Y(this,e,t,!0,r)},p.prototype.writeDoubleBE=function(e,t,r){return Y(this,e,t,!1,r)},p.prototype.copy=function(e,t,r,n){if(r||(r=0),n||0===n||(n=this.length),t>=e.length&&(t=e.length),t||(t=0),n>0&&n<r&&(n=r),n===r)return 0;if(0===e.length||0===this.length)return 0;if(t<0)throw new RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw new RangeError("sourceStart out of bounds");if(n<0)throw new RangeError("sourceEnd out of bounds");n>this.length&&(n=this.length),e.length-t<n-r&&(n=e.length-t+r);var i,o=n-r;if(this===e&&r<t&&t<n)for(i=o-1;i>=0;--i)e[i+t]=this[i+r];else if(o<1e3||!p.TYPED_ARRAY_SUPPORT)for(i=0;i<o;++i)e[i+t]=this[i+r];else Uint8Array.prototype.set.call(e,this.subarray(r,r+o),t);return o},p.prototype.fill=function(e,t,r,n){if("string"==typeof e){if("string"==typeof t?(n=t,t=0,r=this.length):"string"==typeof r&&(n=r,r=this.length),1===e.length){var i=e.charCodeAt(0);i<256&&(e=i)}if(void 0!==n&&"string"!=typeof n)throw new TypeError("encoding must be a string");if("string"==typeof n&&!p.isEncoding(n))throw new TypeError("Unknown encoding: "+n)}else"number"==typeof e&&(e&=255);if(t<0||this.length<t||this.length<r)throw new RangeError("Out of range index");if(r<=t)return this;var o;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(o=t;o<r;++o)this[o]=e;else{var a=y(e)?e:q(new p(e,n).toString()),s=a.length;for(o=0;o<r-t;++o)this[o+t]=a[o%s]}return this};var K=/[^+\/0-9A-Za-z-_]/g;function X(e){return e<16?"0"+e.toString(16):e.toString(16)}function q(e,t){var r;t=t||1/0;for(var n=e.length,i=null,o=[],a=0;a<n;++a){if((r=e.charCodeAt(a))>55295&&r<57344){if(!i){if(r>56319){(t-=3)>-1&&o.push(239,191,189);continue}if(a+1===n){(t-=3)>-1&&o.push(239,191,189);continue}i=r;continue}if(r<56320){(t-=3)>-1&&o.push(239,191,189),i=r;continue}r=65536+(i-55296<<10|r-56320)}else i&&(t-=3)>-1&&o.push(239,191,189);if(i=null,r<128){if((t-=1)<0)break;o.push(r)}else if(r<2048){if((t-=2)<0)break;o.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;o.push(r>>12|224,r>>6&63|128,63&r|128)}else{if(!(r<1114112))throw new Error("Invalid code point");if((t-=4)<0)break;o.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}}return o}function V(e){return function(e){var t,a,s,h,f,l;i||o();var c=e.length;if(c%4>0)throw new Error("Invalid string. Length must be a multiple of 4");f="="===e[c-2]?2:"="===e[c-1]?1:0,l=new n(3*c/4-f),s=f>0?c-4:c;var u=0;for(t=0,a=0;t<s;t+=4,a+=3)h=r[e.charCodeAt(t)]<<18|r[e.charCodeAt(t+1)]<<12|r[e.charCodeAt(t+2)]<<6|r[e.charCodeAt(t+3)],l[u++]=h>>16&255,l[u++]=h>>8&255,l[u++]=255&h;return 2===f?(h=r[e.charCodeAt(t)]<<2|r[e.charCodeAt(t+1)]>>4,l[u++]=255&h):1===f&&(h=r[e.charCodeAt(t)]<<10|r[e.charCodeAt(t+1)]<<4|r[e.charCodeAt(t+2)]>>2,l[u++]=h>>8&255,l[u++]=255&h),l}(function(e){if((e=function(e){return e.trim?e.trim():e.replace(/^\s+|\s+$/g,"")}(e).replace(K,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function G(e,t,r,n){for(var i=0;i<n&&!(i+r>=t.length||i>=e.length);++i)t[i+r]=e[i];return i}function $(e){return null!=e&&(!!e._isBuffer||J(e)||function(e){return"function"==typeof e.readFloatLE&&"function"==typeof e.slice&&J(e.slice(0,0))}(e))}function J(e){return!!e.constructor&&"function"==typeof e.constructor.isBuffer&&e.constructor.isBuffer(e)}"undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self&&self;function Q(e,t){return e(t={exports:{}},t.exports),t.exports}var ee=Q(function(e,t){var r;e.exports=(r=r||function(e,t){var r=Object.create||function(){function e(){}return function(t){var r;return e.prototype=t,r=new e,e.prototype=null,r}}(),n={},i=n.lib={},o=i.Base={extend:function(e){var t=r(this);return e&&t.mixIn(e),t.hasOwnProperty("init")&&this.init!==t.init||(t.init=function(){t.$super.init.apply(this,arguments)}),t.init.prototype=t,t.$super=this,t},create:function(){var e=this.extend();return e.init.apply(e,arguments),e},init:function(){},mixIn:function(e){for(var t in e)e.hasOwnProperty(t)&&(this[t]=e[t]);e.hasOwnProperty("toString")&&(this.toString=e.toString)},clone:function(){return this.init.prototype.extend(this)}},a=i.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:4*e.length},toString:function(e){return(e||h).stringify(this)},concat:function(e){var t=this.words,r=e.words,n=this.sigBytes,i=e.sigBytes;if(this.clamp(),n%4)for(var o=0;o<i;o++){var a=r[o>>>2]>>>24-o%4*8&255;t[n+o>>>2]|=a<<24-(n+o)%4*8}else for(var o=0;o<i;o+=4)t[n+o>>>2]=r[o>>>2];return this.sigBytes+=i,this},clamp:function(){var t=this.words,r=this.sigBytes;t[r>>>2]&=4294967295<<32-r%4*8,t.length=e.ceil(r/4)},clone:function(){var e=o.clone.call(this);return e.words=this.words.slice(0),e},random:function(t){for(var r,n=[],i=function(t){var t=t,r=987654321,n=4294967295;return function(){var i=((r=36969*(65535&r)+(r>>16)&n)<<16)+(t=18e3*(65535&t)+(t>>16)&n)&n;return i/=4294967296,(i+=.5)*(e.random()>.5?1:-1)}},o=0;o<t;o+=4){var s=i(4294967296*(r||e.random()));r=987654071*s(),n.push(4294967296*s()|0)}return new a.init(n,t)}}),s=n.enc={},h=s.Hex={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push((o>>>4).toString(16)),n.push((15&o).toString(16))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n+=2)r[n>>>3]|=parseInt(e.substr(n,2),16)<<24-n%8*4;return new a.init(r,t/2)}},f=s.Latin1={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i++){var o=t[i>>>2]>>>24-i%4*8&255;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var t=e.length,r=[],n=0;n<t;n++)r[n>>>2]|=(255&e.charCodeAt(n))<<24-n%4*8;return new a.init(r,t)}},l=s.Utf8={stringify:function(e){try{return decodeURIComponent(escape(f.stringify(e)))}catch(e){throw new Error("Malformed UTF-8 data")}},parse:function(e){return f.parse(unescape(encodeURIComponent(e)))}},c=i.BufferedBlockAlgorithm=o.extend({reset:function(){this._data=new a.init,this._nDataBytes=0},_append:function(e){"string"==typeof e&&(e=l.parse(e)),this._data.concat(e),this._nDataBytes+=e.sigBytes},_process:function(t){var r=this._data,n=r.words,i=r.sigBytes,o=this.blockSize,s=4*o,h=i/s,f=(h=t?e.ceil(h):e.max((0|h)-this._minBufferSize,0))*o,l=e.min(4*f,i);if(f){for(var c=0;c<f;c+=o)this._doProcessBlock(n,c);var u=n.splice(0,f);r.sigBytes-=l}return new a.init(u,l)},clone:function(){var e=o.clone.call(this);return e._data=this._data.clone(),e},_minBufferSize:0}),u=(i.Hasher=c.extend({cfg:o.extend(),init:function(e){this.cfg=this.cfg.extend(e),this.reset()},reset:function(){c.reset.call(this),this._doReset()},update:function(e){return this._append(e),this._process(),this},finalize:function(e){e&&this._append(e);var t=this._doFinalize();return t},blockSize:16,_createHelper:function(e){return function(t,r){return new e.init(r).finalize(t)}},_createHmacHelper:function(e){return function(t,r){return new u.HMAC.init(e,r).finalize(t)}}}),n.algo={});return n}(Math),r)}),te=(Q(function(e,t){var r,n,i,o,a,s;e.exports=(i=(n=r=ee).lib,o=i.Base,a=i.WordArray,(s=n.x64={}).Word=o.extend({init:function(e,t){this.high=e,this.low=t}}),s.WordArray=o.extend({init:function(e,t){e=this.words=e||[],this.sigBytes=null!=t?t:8*e.length},toX32:function(){for(var e=this.words,t=e.length,r=[],n=0;n<t;n++){var i=e[n];r.push(i.high),r.push(i.low)}return a.create(r,this.sigBytes)},clone:function(){for(var e=o.clone.call(this),t=e.words=this.words.slice(0),r=t.length,n=0;n<r;n++)t[n]=t[n].clone();return e}}),r)}),Q(function(e,t){var r;e.exports=(r=ee,function(){if("function"==typeof ArrayBuffer){var e=r.lib.WordArray,t=e.init;(e.init=function(e){if(e instanceof ArrayBuffer&&(e=new Uint8Array(e)),(e instanceof Int8Array||"undefined"!=typeof Uint8ClampedArray&&e instanceof Uint8ClampedArray||e instanceof Int16Array||e instanceof Uint16Array||e instanceof Int32Array||e instanceof Uint32Array||e instanceof Float32Array||e instanceof Float64Array)&&(e=new Uint8Array(e.buffer,e.byteOffset,e.byteLength)),e instanceof Uint8Array){for(var r=e.byteLength,n=[],i=0;i<r;i++)n[i>>>2]|=e[i]<<24-i%4*8;t.call(this,n,r)}else t.apply(this,arguments)}).prototype=e}}(),r.lib.WordArray)}),Q(function(e,t){var r;e.exports=(r=ee,function(){var e=r,t=e.lib.WordArray,n=e.enc;function i(e){return e<<8&4278255360|e>>>8&16711935}n.Utf16=n.Utf16BE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],i=0;i<r;i+=2){var o=t[i>>>2]>>>16-i%4*8&65535;n.push(String.fromCharCode(o))}return n.join("")},parse:function(e){for(var r=e.length,n=[],i=0;i<r;i++)n[i>>>1]|=e.charCodeAt(i)<<16-i%2*16;return t.create(n,2*r)}},n.Utf16LE={stringify:function(e){for(var t=e.words,r=e.sigBytes,n=[],o=0;o<r;o+=2){var a=i(t[o>>>2]>>>16-o%4*8&65535);n.push(String.fromCharCode(a))}return n.join("")},parse:function(e){for(var r=e.length,n=[],o=0;o<r;o++)n[o>>>1]|=i(e.charCodeAt(o)<<16-o%2*16);return t.create(n,2*r)}}}(),r.enc.Utf16)}),Q(function(e,t){var r,n,i;e.exports=(i=(n=r=ee).lib.WordArray,n.enc.Base64={stringify:function(e){var t=e.words,r=e.sigBytes,n=this._map;e.clamp();for(var i=[],o=0;o<r;o+=3)for(var a=(t[o>>>2]>>>24-o%4*8&255)<<16|(t[o+1>>>2]>>>24-(o+1)%4*8&255)<<8|t[o+2>>>2]>>>24-(o+2)%4*8&255,s=0;s<4&&o+.75*s<r;s++)i.push(n.charAt(a>>>6*(3-s)&63));var h=n.charAt(64);if(h)for(;i.length%4;)i.push(h);return i.join("")},parse:function(e){var t=e.length,r=this._map,n=this._reverseMap;if(!n){n=this._reverseMap=[];for(var o=0;o<r.length;o++)n[r.charCodeAt(o)]=o}var a=r.charAt(64);if(a){var s=e.indexOf(a);-1!==s&&(t=s)}return function(e,t,r){for(var n=[],o=0,a=0;a<t;a++)if(a%4){var s=r[e.charCodeAt(a-1)]<<a%4*2,h=r[e.charCodeAt(a)]>>>6-a%4*2;n[o>>>2]|=(s|h)<<24-o%4*8,o++}return i.create(n,o)}(e,t,n)},_map:"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/="},r.enc.Base64)}),Q(function(e,t){var r;e.exports=(r=ee,function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.algo,s=[];!function(){for(var t=0;t<64;t++)s[t]=4294967296*e.abs(e.sin(t+1))|0}();var h=a.MD5=o.extend({_doReset:function(){this._hash=new i.init([1732584193,4023233417,2562383102,271733878])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o=this._hash.words,a=e[t+0],h=e[t+1],d=e[t+2],p=e[t+3],_=e[t+4],g=e[t+5],v=e[t+6],w=e[t+7],b=e[t+8],y=e[t+9],m=e[t+10],k=e[t+11],E=e[t+12],S=e[t+13],x=e[t+14],R=e[t+15],A=o[0],B=o[1],z=o[2],L=o[3];A=f(A,B,z,L,a,7,s[0]),L=f(L,A,B,z,h,12,s[1]),z=f(z,L,A,B,d,17,s[2]),B=f(B,z,L,A,p,22,s[3]),A=f(A,B,z,L,_,7,s[4]),L=f(L,A,B,z,g,12,s[5]),z=f(z,L,A,B,v,17,s[6]),B=f(B,z,L,A,w,22,s[7]),A=f(A,B,z,L,b,7,s[8]),L=f(L,A,B,z,y,12,s[9]),z=f(z,L,A,B,m,17,s[10]),B=f(B,z,L,A,k,22,s[11]),A=f(A,B,z,L,E,7,s[12]),L=f(L,A,B,z,S,12,s[13]),z=f(z,L,A,B,x,17,s[14]),A=l(A,B=f(B,z,L,A,R,22,s[15]),z,L,h,5,s[16]),L=l(L,A,B,z,v,9,s[17]),z=l(z,L,A,B,k,14,s[18]),B=l(B,z,L,A,a,20,s[19]),A=l(A,B,z,L,g,5,s[20]),L=l(L,A,B,z,m,9,s[21]),z=l(z,L,A,B,R,14,s[22]),B=l(B,z,L,A,_,20,s[23]),A=l(A,B,z,L,y,5,s[24]),L=l(L,A,B,z,x,9,s[25]),z=l(z,L,A,B,p,14,s[26]),B=l(B,z,L,A,b,20,s[27]),A=l(A,B,z,L,S,5,s[28]),L=l(L,A,B,z,d,9,s[29]),z=l(z,L,A,B,w,14,s[30]),A=c(A,B=l(B,z,L,A,E,20,s[31]),z,L,g,4,s[32]),L=c(L,A,B,z,b,11,s[33]),z=c(z,L,A,B,k,16,s[34]),B=c(B,z,L,A,x,23,s[35]),A=c(A,B,z,L,h,4,s[36]),L=c(L,A,B,z,_,11,s[37]),z=c(z,L,A,B,w,16,s[38]),B=c(B,z,L,A,m,23,s[39]),A=c(A,B,z,L,S,4,s[40]),L=c(L,A,B,z,a,11,s[41]),z=c(z,L,A,B,p,16,s[42]),B=c(B,z,L,A,v,23,s[43]),A=c(A,B,z,L,y,4,s[44]),L=c(L,A,B,z,E,11,s[45]),z=c(z,L,A,B,R,16,s[46]),A=u(A,B=c(B,z,L,A,d,23,s[47]),z,L,a,6,s[48]),L=u(L,A,B,z,w,10,s[49]),z=u(z,L,A,B,x,15,s[50]),B=u(B,z,L,A,g,21,s[51]),A=u(A,B,z,L,E,6,s[52]),L=u(L,A,B,z,p,10,s[53]),z=u(z,L,A,B,m,15,s[54]),B=u(B,z,L,A,h,21,s[55]),A=u(A,B,z,L,b,6,s[56]),L=u(L,A,B,z,R,10,s[57]),z=u(z,L,A,B,v,15,s[58]),B=u(B,z,L,A,S,21,s[59]),A=u(A,B,z,L,_,6,s[60]),L=u(L,A,B,z,k,10,s[61]),z=u(z,L,A,B,d,15,s[62]),B=u(B,z,L,A,y,21,s[63]),o[0]=o[0]+A|0,o[1]=o[1]+B|0,o[2]=o[2]+z|0,o[3]=o[3]+L|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;r[i>>>5]|=128<<24-i%32;var o=e.floor(n/4294967296),a=n;r[15+(i+64>>>9<<4)]=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),r[14+(i+64>>>9<<4)]=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),t.sigBytes=4*(r.length+1),this._process();for(var s=this._hash,h=s.words,f=0;f<4;f++){var l=h[f];h[f]=16711935&(l<<8|l>>>24)|4278255360&(l<<24|l>>>8)}return s},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function f(e,t,r,n,i,o,a){var s=e+(t&r|~t&n)+i+a;return(s<<o|s>>>32-o)+t}function l(e,t,r,n,i,o,a){var s=e+(t&n|r&~n)+i+a;return(s<<o|s>>>32-o)+t}function c(e,t,r,n,i,o,a){var s=e+(t^r^n)+i+a;return(s<<o|s>>>32-o)+t}function u(e,t,r,n,i,o,a){var s=e+(r^(t|~n))+i+a;return(s<<o|s>>>32-o)+t}t.MD5=o._createHelper(h),t.HmacMD5=o._createHmacHelper(h)}(Math),r.MD5)}),Q(function(e,t){var r,n,i,o,a,s,h,f;e.exports=(i=(n=r=ee).lib,o=i.WordArray,a=i.Hasher,s=n.algo,h=[],f=s.SHA1=a.extend({_doReset:function(){this._hash=new o.init([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],s=r[4],f=0;f<80;f++){if(f<16)h[f]=0|e[t+f];else{var l=h[f-3]^h[f-8]^h[f-14]^h[f-16];h[f]=l<<1|l>>>31}var c=(n<<5|n>>>27)+s+h[f];c+=f<20?1518500249+(i&o|~i&a):f<40?1859775393+(i^o^a):f<60?(i&o|i&a|o&a)-1894007588:(i^o^a)-899497514,s=a,a=o,o=i<<30|i>>>2,i=n,n=c}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=Math.floor(r/4294967296),t[15+(n+64>>>9<<4)]=r,e.sigBytes=4*t.length,this._process(),this._hash},clone:function(){var e=a.clone.call(this);return e._hash=this._hash.clone(),e}}),n.SHA1=a._createHelper(f),n.HmacSHA1=a._createHmacHelper(f),r.SHA1)}),Q(function(e,t){var r;e.exports=(r=ee,function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.algo,s=[],h=[];!function(){function t(t){for(var r=e.sqrt(t),n=2;n<=r;n++)if(!(t%n))return!1;return!0}function r(e){return 4294967296*(e-(0|e))|0}for(var n=2,i=0;i<64;)t(n)&&(i<8&&(s[i]=r(e.pow(n,.5))),h[i]=r(e.pow(n,1/3)),i++),n++}();var f=[],l=a.SHA256=o.extend({_doReset:function(){this._hash=new i.init(s.slice(0))},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],s=r[4],l=r[5],c=r[6],u=r[7],d=0;d<64;d++){if(d<16)f[d]=0|e[t+d];else{var p=f[d-15],_=(p<<25|p>>>7)^(p<<14|p>>>18)^p>>>3,g=f[d-2],v=(g<<15|g>>>17)^(g<<13|g>>>19)^g>>>10;f[d]=_+f[d-7]+v+f[d-16]}var w=n&i^n&o^i&o,b=(n<<30|n>>>2)^(n<<19|n>>>13)^(n<<10|n>>>22),y=u+((s<<26|s>>>6)^(s<<21|s>>>11)^(s<<7|s>>>25))+(s&l^~s&c)+h[d]+f[d];u=c,c=l,l=s,s=a+y|0,a=o,o=i,i=n,n=y+(b+w)|0}r[0]=r[0]+n|0,r[1]=r[1]+i|0,r[2]=r[2]+o|0,r[3]=r[3]+a|0,r[4]=r[4]+s|0,r[5]=r[5]+l|0,r[6]=r[6]+c|0,r[7]=r[7]+u|0},_doFinalize:function(){var t=this._data,r=t.words,n=8*this._nDataBytes,i=8*t.sigBytes;return r[i>>>5]|=128<<24-i%32,r[14+(i+64>>>9<<4)]=e.floor(n/4294967296),r[15+(i+64>>>9<<4)]=n,t.sigBytes=4*r.length,this._process(),this._hash},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});t.SHA256=o._createHelper(l),t.HmacSHA256=o._createHmacHelper(l)}(Math),r.SHA256)}),Q(function(e,t){var r,n,i,o,a,s;e.exports=(i=(n=r=ee).lib.WordArray,o=n.algo,a=o.SHA256,s=o.SHA224=a.extend({_doReset:function(){this._hash=new i.init([3238371032,914150663,812702999,4144912697,4290775857,1750603025,1694076839,3204075428])},_doFinalize:function(){var e=a._doFinalize.call(this);return e.sigBytes-=4,e}}),n.SHA224=a._createHelper(s),n.HmacSHA224=a._createHmacHelper(s),r.SHA224)}),Q(function(e,t){var r;e.exports=(r=ee,function(){var e=r,t=e.lib.Hasher,n=e.x64,i=n.Word,o=n.WordArray,a=e.algo;function s(){return i.create.apply(i,arguments)}var h=[s(1116352408,3609767458),s(1899447441,602891725),s(3049323471,3964484399),s(3921009573,2173295548),s(961987163,4081628472),s(1508970993,3053834265),s(2453635748,2937671579),s(2870763221,3664609560),s(3624381080,2734883394),s(310598401,1164996542),s(607225278,1323610764),s(1426881987,3590304994),s(1925078388,4068182383),s(2162078206,991336113),s(2614888103,633803317),s(3248222580,3479774868),s(3835390401,2666613458),s(4022224774,944711139),s(264347078,2341262773),s(604807628,2007800933),s(770255983,1495990901),s(1249150122,1856431235),s(1555081692,3175218132),s(1996064986,2198950837),s(2554220882,3999719339),s(2821834349,766784016),s(2952996808,2566594879),s(3210313671,3203337956),s(3336571891,1034457026),s(3584528711,2466948901),s(113926993,3758326383),s(338241895,168717936),s(666307205,1188179964),s(773529912,1546045734),s(1294757372,1522805485),s(1396182291,2643833823),s(1695183700,2343527390),s(1986661051,1014477480),s(2177026350,1206759142),s(2456956037,344077627),s(2730485921,1290863460),s(2820302411,3158454273),s(3259730800,3505952657),s(3345764771,106217008),s(3516065817,3606008344),s(3600352804,1432725776),s(4094571909,1467031594),s(275423344,851169720),s(430227734,3100823752),s(506948616,1363258195),s(659060556,3750685593),s(883997877,3785050280),s(958139571,3318307427),s(1322822218,3812723403),s(1537002063,2003034995),s(1747873779,3602036899),s(1955562222,1575990012),s(2024104815,1125592928),s(2227730452,2716904306),s(2361852424,442776044),s(2428436474,593698344),s(2756734187,3733110249),s(3204031479,2999351573),s(3329325298,3815920427),s(3391569614,3928383900),s(3515267271,566280711),s(3940187606,3454069534),s(4118630271,4000239992),s(116418474,1914138554),s(174292421,2731055270),s(289380356,3203993006),s(460393269,320620315),s(685471733,587496836),s(852142971,1086792851),s(1017036298,365543100),s(1126000580,2618297676),s(1288033470,3409855158),s(1501505948,4234509866),s(1607167915,987167468),s(1816402316,1246189591)],f=[];!function(){for(var e=0;e<80;e++)f[e]=s()}();var l=a.SHA512=t.extend({_doReset:function(){this._hash=new o.init([new i.init(1779033703,4089235720),new i.init(3144134277,2227873595),new i.init(1013904242,4271175723),new i.init(2773480762,1595750129),new i.init(1359893119,2917565137),new i.init(2600822924,725511199),new i.init(528734635,4215389547),new i.init(1541459225,327033209)])},_doProcessBlock:function(e,t){for(var r=this._hash.words,n=r[0],i=r[1],o=r[2],a=r[3],s=r[4],l=r[5],c=r[6],u=r[7],d=n.high,p=n.low,_=i.high,g=i.low,v=o.high,w=o.low,b=a.high,y=a.low,m=s.high,k=s.low,E=l.high,S=l.low,x=c.high,R=c.low,A=u.high,B=u.low,z=d,L=p,T=_,M=g,C=v,D=w,I=b,P=y,O=m,U=k,H=E,F=S,N=x,Z=R,j=A,W=B,Y=0;Y<80;Y++){var K=f[Y];if(Y<16)var X=K.high=0|e[t+2*Y],q=K.low=0|e[t+2*Y+1];else{var V=f[Y-15],G=V.high,$=V.low,J=(G>>>1|$<<31)^(G>>>8|$<<24)^G>>>7,Q=($>>>1|G<<31)^($>>>8|G<<24)^($>>>7|G<<25),ee=f[Y-2],te=ee.high,re=ee.low,ne=(te>>>19|re<<13)^(te<<3|re>>>29)^te>>>6,ie=(re>>>19|te<<13)^(re<<3|te>>>29)^(re>>>6|te<<26),oe=f[Y-7],ae=oe.high,se=oe.low,he=f[Y-16],fe=he.high,le=he.low;X=(X=(X=J+ae+((q=Q+se)>>>0<Q>>>0?1:0))+ne+((q+=ie)>>>0<ie>>>0?1:0))+fe+((q+=le)>>>0<le>>>0?1:0),K.high=X,K.low=q}var ce,ue=O&H^~O&N,de=U&F^~U&Z,pe=z&T^z&C^T&C,_e=L&M^L&D^M&D,ge=(z>>>28|L<<4)^(z<<30|L>>>2)^(z<<25|L>>>7),ve=(L>>>28|z<<4)^(L<<30|z>>>2)^(L<<25|z>>>7),we=(O>>>14|U<<18)^(O>>>18|U<<14)^(O<<23|U>>>9),be=(U>>>14|O<<18)^(U>>>18|O<<14)^(U<<23|O>>>9),ye=h[Y],me=ye.high,ke=ye.low,Ee=j+we+((ce=W+be)>>>0<W>>>0?1:0),Se=ve+_e;j=N,W=Z,N=H,Z=F,H=O,F=U,O=I+(Ee=(Ee=(Ee=Ee+ue+((ce+=de)>>>0<de>>>0?1:0))+me+((ce+=ke)>>>0<ke>>>0?1:0))+X+((ce+=q)>>>0<q>>>0?1:0))+((U=P+ce|0)>>>0<P>>>0?1:0)|0,I=C,P=D,C=T,D=M,T=z,M=L,z=Ee+(ge+pe+(Se>>>0<ve>>>0?1:0))+((L=ce+Se|0)>>>0<ce>>>0?1:0)|0}p=n.low=p+L,n.high=d+z+(p>>>0<L>>>0?1:0),g=i.low=g+M,i.high=_+T+(g>>>0<M>>>0?1:0),w=o.low=w+D,o.high=v+C+(w>>>0<D>>>0?1:0),y=a.low=y+P,a.high=b+I+(y>>>0<P>>>0?1:0),k=s.low=k+U,s.high=m+O+(k>>>0<U>>>0?1:0),S=l.low=S+F,l.high=E+H+(S>>>0<F>>>0?1:0),R=c.low=R+Z,c.high=x+N+(R>>>0<Z>>>0?1:0),B=u.low=B+W,u.high=A+j+(B>>>0<W>>>0?1:0)},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;return t[n>>>5]|=128<<24-n%32,t[30+(n+128>>>10<<5)]=Math.floor(r/4294967296),t[31+(n+128>>>10<<5)]=r,e.sigBytes=4*t.length,this._process(),this._hash.toX32()},clone:function(){var e=t.clone.call(this);return e._hash=this._hash.clone(),e},blockSize:32});e.SHA512=t._createHelper(l),e.HmacSHA512=t._createHmacHelper(l)}(),r.SHA512)}),Q(function(e,t){var r,n,i,o,a,s,h,f;e.exports=(i=(n=r=ee).x64,o=i.Word,a=i.WordArray,s=n.algo,h=s.SHA512,f=s.SHA384=h.extend({_doReset:function(){this._hash=new a.init([new o.init(3418070365,3238371032),new o.init(1654270250,914150663),new o.init(2438529370,812702999),new o.init(355462360,4144912697),new o.init(1731405415,4290775857),new o.init(2394180231,1750603025),new o.init(3675008525,1694076839),new o.init(1203062813,3204075428)])},_doFinalize:function(){var e=h._doFinalize.call(this);return e.sigBytes-=16,e}}),n.SHA384=h._createHelper(f),n.HmacSHA384=h._createHmacHelper(f),r.SHA384)}),Q(function(e,t){var r;e.exports=(r=ee,function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.x64.Word,s=t.algo,h=[],f=[],l=[];!function(){for(var e=1,t=0,r=0;r<24;r++){h[e+5*t]=(r+1)*(r+2)/2%64;var n=(2*e+3*t)%5;e=t%5,t=n}for(e=0;e<5;e++)for(t=0;t<5;t++)f[e+5*t]=t+(2*e+3*t)%5*5;for(var i=1,o=0;o<24;o++){for(var s=0,c=0,u=0;u<7;u++){if(1&i){var d=(1<<u)-1;d<32?c^=1<<d:s^=1<<d-32}128&i?i=i<<1^113:i<<=1}l[o]=a.create(s,c)}}();var c=[];!function(){for(var e=0;e<25;e++)c[e]=a.create()}();var u=s.SHA3=o.extend({cfg:o.cfg.extend({outputLength:512}),_doReset:function(){for(var e=this._state=[],t=0;t<25;t++)e[t]=new a.init;this.blockSize=(1600-2*this.cfg.outputLength)/32},_doProcessBlock:function(e,t){for(var r=this._state,n=this.blockSize/2,i=0;i<n;i++){var o=e[t+2*i],a=e[t+2*i+1];o=16711935&(o<<8|o>>>24)|4278255360&(o<<24|o>>>8),a=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),(B=r[i]).high^=a,B.low^=o}for(var s=0;s<24;s++){for(var u=0;u<5;u++){for(var d=0,p=0,_=0;_<5;_++)d^=(B=r[u+5*_]).high,p^=B.low;var g=c[u];g.high=d,g.low=p}for(u=0;u<5;u++){var v=c[(u+4)%5],w=c[(u+1)%5],b=w.high,y=w.low;for(d=v.high^(b<<1|y>>>31),p=v.low^(y<<1|b>>>31),_=0;_<5;_++)(B=r[u+5*_]).high^=d,B.low^=p}for(var m=1;m<25;m++){var k=(B=r[m]).high,E=B.low,S=h[m];S<32?(d=k<<S|E>>>32-S,p=E<<S|k>>>32-S):(d=E<<S-32|k>>>64-S,p=k<<S-32|E>>>64-S);var x=c[f[m]];x.high=d,x.low=p}var R=c[0],A=r[0];for(R.high=A.high,R.low=A.low,u=0;u<5;u++)for(_=0;_<5;_++){var B=r[m=u+5*_],z=c[m],L=c[(u+1)%5+5*_],T=c[(u+2)%5+5*_];B.high=z.high^~L.high&T.high,B.low=z.low^~L.low&T.low}B=r[0];var M=l[s];B.high^=M.high,B.low^=M.low}},_doFinalize:function(){var t=this._data,r=t.words,n=(this._nDataBytes,8*t.sigBytes),o=32*this.blockSize;r[n>>>5]|=1<<24-n%32,r[(e.ceil((n+1)/o)*o>>>5)-1]|=128,t.sigBytes=4*r.length,this._process();for(var a=this._state,s=this.cfg.outputLength/8,h=s/8,f=[],l=0;l<h;l++){var c=a[l],u=c.high,d=c.low;u=16711935&(u<<8|u>>>24)|4278255360&(u<<24|u>>>8),d=16711935&(d<<8|d>>>24)|4278255360&(d<<24|d>>>8),f.push(d),f.push(u)}return new i.init(f,s)},clone:function(){for(var e=o.clone.call(this),t=e._state=this._state.slice(0),r=0;r<25;r++)t[r]=t[r].clone();return e}});t.SHA3=o._createHelper(u),t.HmacSHA3=o._createHmacHelper(u)}(Math),r.SHA3)}),Q(function(e,t){var r;e.exports=(r=ee,function(e){var t=r,n=t.lib,i=n.WordArray,o=n.Hasher,a=t.algo,s=i.create([0,1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,7,4,13,1,10,6,15,3,12,0,9,5,2,14,11,8,3,10,14,4,9,15,8,1,2,7,0,6,13,11,5,12,1,9,11,10,0,8,12,4,13,3,7,15,14,5,6,2,4,0,5,9,7,12,2,10,14,1,3,8,11,6,15,13]),h=i.create([5,14,7,0,9,2,11,4,13,6,15,8,1,10,3,12,6,11,3,7,0,13,5,10,14,15,8,12,4,9,1,2,15,5,1,3,7,14,6,9,11,8,12,2,10,0,4,13,8,6,4,1,3,11,15,0,5,12,2,13,9,7,10,14,12,15,10,4,1,5,8,7,6,2,13,14,0,3,9,11]),f=i.create([11,14,15,12,5,8,7,9,11,13,14,15,6,7,9,8,7,6,8,13,11,9,7,15,7,12,15,9,11,7,13,12,11,13,6,7,14,9,13,15,14,8,13,6,5,12,7,5,11,12,14,15,14,15,9,8,9,14,5,6,8,6,5,12,9,15,5,11,6,8,13,12,5,12,13,14,11,8,5,6]),l=i.create([8,9,9,11,13,15,15,5,7,7,8,11,14,14,12,6,9,13,15,7,12,8,9,11,7,7,12,7,6,15,13,11,9,7,15,11,8,6,6,14,12,13,5,14,13,13,7,5,15,5,8,11,14,14,6,14,6,9,12,9,12,5,15,8,8,5,12,9,12,5,14,6,8,13,6,5,15,13,11,11]),c=i.create([0,1518500249,1859775393,2400959708,2840853838]),u=i.create([1352829926,1548603684,1836072691,2053994217,0]),d=a.RIPEMD160=o.extend({_doReset:function(){this._hash=i.create([1732584193,4023233417,2562383102,271733878,3285377520])},_doProcessBlock:function(e,t){for(var r=0;r<16;r++){var n=t+r,i=e[n];e[n]=16711935&(i<<8|i>>>24)|4278255360&(i<<24|i>>>8)}var o,a,d,y,m,k,E,S,x,R,A,B=this._hash.words,z=c.words,L=u.words,T=s.words,M=h.words,C=f.words,D=l.words;for(k=o=B[0],E=a=B[1],S=d=B[2],x=y=B[3],R=m=B[4],r=0;r<80;r+=1)A=o+e[t+T[r]]|0,A+=r<16?p(a,d,y)+z[0]:r<32?_(a,d,y)+z[1]:r<48?g(a,d,y)+z[2]:r<64?v(a,d,y)+z[3]:w(a,d,y)+z[4],A=(A=b(A|=0,C[r]))+m|0,o=m,m=y,y=b(d,10),d=a,a=A,A=k+e[t+M[r]]|0,A+=r<16?w(E,S,x)+L[0]:r<32?v(E,S,x)+L[1]:r<48?g(E,S,x)+L[2]:r<64?_(E,S,x)+L[3]:p(E,S,x)+L[4],A=(A=b(A|=0,D[r]))+R|0,k=R,R=x,x=b(S,10),S=E,E=A;A=B[1]+d+x|0,B[1]=B[2]+y+R|0,B[2]=B[3]+m+k|0,B[3]=B[4]+o+E|0,B[4]=B[0]+a+S|0,B[0]=A},_doFinalize:function(){var e=this._data,t=e.words,r=8*this._nDataBytes,n=8*e.sigBytes;t[n>>>5]|=128<<24-n%32,t[14+(n+64>>>9<<4)]=16711935&(r<<8|r>>>24)|4278255360&(r<<24|r>>>8),e.sigBytes=4*(t.length+1),this._process();for(var i=this._hash,o=i.words,a=0;a<5;a++){var s=o[a];o[a]=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8)}return i},clone:function(){var e=o.clone.call(this);return e._hash=this._hash.clone(),e}});function p(e,t,r){return e^t^r}function _(e,t,r){return e&t|~e&r}function g(e,t,r){return(e|~t)^r}function v(e,t,r){return e&r|t&~r}function w(e,t,r){return e^(t|~r)}function b(e,t){return e<<t|e>>>32-t}t.RIPEMD160=o._createHelper(d),t.HmacRIPEMD160=o._createHmacHelper(d)}(),r.RIPEMD160)}),Q(function(e,t){var r,n,i,o,a,s;e.exports=(n=(r=ee).lib,i=n.Base,o=r.enc,a=o.Utf8,s=r.algo,void(s.HMAC=i.extend({init:function(e,t){e=this._hasher=new e.init,"string"==typeof t&&(t=a.parse(t));var r=e.blockSize,n=4*r;t.sigBytes>n&&(t=e.finalize(t)),t.clamp();for(var i=this._oKey=t.clone(),o=this._iKey=t.clone(),s=i.words,h=o.words,f=0;f<r;f++)s[f]^=1549556828,h[f]^=909522486;i.sigBytes=o.sigBytes=n,this.reset()},reset:function(){var e=this._hasher;e.reset(),e.update(this._iKey)},update:function(e){return this._hasher.update(e),this},finalize:function(e){var t=this._hasher,r=t.finalize(e);t.reset();var n=t.finalize(this._oKey.clone().concat(r));return n}})))}),Q(function(e,t){var r,n,i,o,a,s,h,f,l;e.exports=(i=(n=r=ee).lib,o=i.Base,a=i.WordArray,s=n.algo,h=s.SHA1,f=s.HMAC,l=s.PBKDF2=o.extend({cfg:o.extend({keySize:4,hasher:h,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,n=f.create(r.hasher,e),i=a.create(),o=a.create([1]),s=i.words,h=o.words,l=r.keySize,c=r.iterations;s.length<l;){var u=n.update(t).finalize(o);n.reset();for(var d=u.words,p=d.length,_=u,g=1;g<c;g++){_=n.finalize(_),n.reset();for(var v=_.words,w=0;w<p;w++)d[w]^=v[w]}i.concat(u),h[0]++}return i.sigBytes=4*l,i}}),n.PBKDF2=function(e,t,r){return l.create(r).compute(e,t)},r.PBKDF2)}),Q(function(e,t){var r,n,i,o,a,s,h,f;e.exports=(i=(n=r=ee).lib,o=i.Base,a=i.WordArray,s=n.algo,h=s.MD5,f=s.EvpKDF=o.extend({cfg:o.extend({keySize:4,hasher:h,iterations:1}),init:function(e){this.cfg=this.cfg.extend(e)},compute:function(e,t){for(var r=this.cfg,n=r.hasher.create(),i=a.create(),o=i.words,s=r.keySize,h=r.iterations;o.length<s;){f&&n.update(f);var f=n.update(e).finalize(t);n.reset();for(var l=1;l<h;l++)f=n.finalize(f),n.reset();i.concat(f)}return i.sigBytes=4*s,i}}),n.EvpKDF=function(e,t,r){return f.create(r).compute(e,t)},r.EvpKDF)}),Q(function(e,t){var r,n,i,o,a,s,h,f,l,c,u,d,p,_,g,v,w,b,y,m,k,E,S,x;e.exports=void((r=ee).lib.Cipher||(i=r,o=i.lib,a=o.Base,s=o.WordArray,h=o.BufferedBlockAlgorithm,f=i.enc,f.Utf8,l=f.Base64,c=i.algo,u=c.EvpKDF,d=o.Cipher=h.extend({cfg:a.extend(),createEncryptor:function(e,t){return this.create(this._ENC_XFORM_MODE,e,t)},createDecryptor:function(e,t){return this.create(this._DEC_XFORM_MODE,e,t)},init:function(e,t,r){this.cfg=this.cfg.extend(r),this._xformMode=e,this._key=t,this.reset()},reset:function(){h.reset.call(this),this._doReset()},process:function(e){return this._append(e),this._process()},finalize:function(e){e&&this._append(e);var t=this._doFinalize();return t},keySize:4,ivSize:4,_ENC_XFORM_MODE:1,_DEC_XFORM_MODE:2,_createHelper:function(){function e(e){return"string"==typeof e?x:k}return function(t){return{encrypt:function(r,n,i){return e(n).encrypt(t,r,n,i)},decrypt:function(r,n,i){return e(n).decrypt(t,r,n,i)}}}}()}),o.StreamCipher=d.extend({_doFinalize:function(){var e=this._process(!0);return e},blockSize:1}),p=i.mode={},_=o.BlockCipherMode=a.extend({createEncryptor:function(e,t){return this.Encryptor.create(e,t)},createDecryptor:function(e,t){return this.Decryptor.create(e,t)},init:function(e,t){this._cipher=e,this._iv=t}}),g=p.CBC=function(){var e=_.extend();function t(e,t,r){var i=this._iv;if(i){var o=i;this._iv=n}else var o=this._prevBlock;for(var a=0;a<r;a++)e[t+a]^=o[a]}return e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize;t.call(this,e,r,i),n.encryptBlock(e,r),this._prevBlock=e.slice(r,r+i)}}),e.Decryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,o=e.slice(r,r+i);n.decryptBlock(e,r),t.call(this,e,r,i),this._prevBlock=o}}),e}(),v=i.pad={},w=v.Pkcs7={pad:function(e,t){for(var r=4*t,n=r-e.sigBytes%r,i=n<<24|n<<16|n<<8|n,o=[],a=0;a<n;a+=4)o.push(i);var h=s.create(o,n);e.concat(h)},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},o.BlockCipher=d.extend({cfg:d.cfg.extend({mode:g,padding:w}),reset:function(){d.reset.call(this);var e=this.cfg,t=e.iv,r=e.mode;if(this._xformMode==this._ENC_XFORM_MODE)var n=r.createEncryptor;else{var n=r.createDecryptor;this._minBufferSize=1}this._mode&&this._mode.__creator==n?this._mode.init(this,t&&t.words):(this._mode=n.call(r,this,t&&t.words),this._mode.__creator=n)},_doProcessBlock:function(e,t){this._mode.processBlock(e,t)},_doFinalize:function(){var e=this.cfg.padding;if(this._xformMode==this._ENC_XFORM_MODE){e.pad(this._data,this.blockSize);var t=this._process(!0)}else{var t=this._process(!0);e.unpad(t)}return t},blockSize:4}),b=o.CipherParams=a.extend({init:function(e){this.mixIn(e)},toString:function(e){return(e||this.formatter).stringify(this)}}),y=i.format={},m=y.OpenSSL={stringify:function(e){var t=e.ciphertext,r=e.salt;if(r)var n=s.create([1398893684,1701076831]).concat(r).concat(t);else var n=t;return n.toString(l)},parse:function(e){var t=l.parse(e),r=t.words;if(1398893684==r[0]&&1701076831==r[1]){var n=s.create(r.slice(2,4));r.splice(0,4),t.sigBytes-=16}return b.create({ciphertext:t,salt:n})}},k=o.SerializableCipher=a.extend({cfg:a.extend({format:m}),encrypt:function(e,t,r,n){n=this.cfg.extend(n);var i=e.createEncryptor(r,n),o=i.finalize(t),a=i.cfg;return b.create({ciphertext:o,key:r,iv:a.iv,algorithm:e,mode:a.mode,padding:a.padding,blockSize:e.blockSize,formatter:n.format})},decrypt:function(e,t,r,n){n=this.cfg.extend(n),t=this._parse(t,n.format);var i=e.createDecryptor(r,n).finalize(t.ciphertext);return i},_parse:function(e,t){return"string"==typeof e?t.parse(e,this):e}}),E=i.kdf={},S=E.OpenSSL={execute:function(e,t,r,n){n||(n=s.random(8));var i=u.create({keySize:t+r}).compute(e,n),o=s.create(i.words.slice(t),4*r);return i.sigBytes=4*t,b.create({key:i,iv:o,salt:n})}},x=o.PasswordBasedCipher=k.extend({cfg:k.cfg.extend({kdf:S}),encrypt:function(e,t,r,n){var i=(n=this.cfg.extend(n)).kdf.execute(r,e.keySize,e.ivSize);n.iv=i.iv;var o=k.encrypt.call(this,e,t,i.key,n);return o.mixIn(i),o},decrypt:function(e,t,r,n){n=this.cfg.extend(n),t=this._parse(t,n.format);var i=n.kdf.execute(r,e.keySize,e.ivSize,t.salt);n.iv=i.iv;var o=k.decrypt.call(this,e,t,i.key,n);return o}})))}),Q(function(e,t){var r;e.exports=((r=ee).mode.CFB=function(){var e=r.lib.BlockCipherMode.extend();function t(e,t,r,n){var i=this._iv;if(i){var o=i.slice(0);this._iv=void 0}else o=this._prevBlock;n.encryptBlock(o,0);for(var a=0;a<r;a++)e[t+a]^=o[a]}return e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize;t.call(this,e,r,i,n),this._prevBlock=e.slice(r,r+i)}}),e.Decryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,o=e.slice(r,r+i);t.call(this,e,r,i,n),this._prevBlock=o}}),e}(),r.mode.CFB)}),Q(function(e,t){var r,n,i;e.exports=((r=ee).mode.CTR=(n=r.lib.BlockCipherMode.extend(),i=n.Encryptor=n.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._counter;i&&(o=this._counter=i.slice(0),this._iv=void 0);var a=o.slice(0);r.encryptBlock(a,0),o[n-1]=o[n-1]+1|0;for(var s=0;s<n;s++)e[t+s]^=a[s]}}),n.Decryptor=i,n),r.mode.CTR)}),Q(function(e,t){var r;e.exports=((r=ee).mode.CTRGladman=function(){var e=r.lib.BlockCipherMode.extend();function t(e){if(255==(e>>24&255)){var t=e>>16&255,r=e>>8&255,n=255&e;255===t?(t=0,255===r?(r=0,255===n?n=0:++n):++r):++t,e=0,e+=t<<16,e+=r<<8,e+=n}else e+=1<<24;return e}var n=e.Encryptor=e.extend({processBlock:function(e,r){var n=this._cipher,i=n.blockSize,o=this._iv,a=this._counter;o&&(a=this._counter=o.slice(0),this._iv=void 0),function(e){0===(e[0]=t(e[0]))&&(e[1]=t(e[1]))}(a);var s=a.slice(0);n.encryptBlock(s,0);for(var h=0;h<i;h++)e[r+h]^=s[h]}});return e.Decryptor=n,e}(),r.mode.CTRGladman)}),Q(function(e,t){var r,n,i;e.exports=((r=ee).mode.OFB=(n=r.lib.BlockCipherMode.extend(),i=n.Encryptor=n.extend({processBlock:function(e,t){var r=this._cipher,n=r.blockSize,i=this._iv,o=this._keystream;i&&(o=this._keystream=i.slice(0),this._iv=void 0),r.encryptBlock(o,0);for(var a=0;a<n;a++)e[t+a]^=o[a]}}),n.Decryptor=i,n),r.mode.OFB)}),Q(function(e,t){var r,n;e.exports=((r=ee).mode.ECB=((n=r.lib.BlockCipherMode.extend()).Encryptor=n.extend({processBlock:function(e,t){this._cipher.encryptBlock(e,t)}}),n.Decryptor=n.extend({processBlock:function(e,t){this._cipher.decryptBlock(e,t)}}),n),r.mode.ECB)}),Q(function(e,t){var r;e.exports=((r=ee).pad.AnsiX923={pad:function(e,t){var r=e.sigBytes,n=4*t,i=n-r%n,o=r+i-1;e.clamp(),e.words[o>>>2]|=i<<24-o%4*8,e.sigBytes+=i},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Ansix923)}),Q(function(e,t){var r;e.exports=((r=ee).pad.Iso10126={pad:function(e,t){var n=4*t,i=n-e.sigBytes%n;e.concat(r.lib.WordArray.random(i-1)).concat(r.lib.WordArray.create([i<<24],1))},unpad:function(e){var t=255&e.words[e.sigBytes-1>>>2];e.sigBytes-=t}},r.pad.Iso10126)}),Q(function(e,t){var r;e.exports=((r=ee).pad.Iso97971={pad:function(e,t){e.concat(r.lib.WordArray.create([2147483648],1)),r.pad.ZeroPadding.pad(e,t)},unpad:function(e){r.pad.ZeroPadding.unpad(e),e.sigBytes--}},r.pad.Iso97971)}),Q(function(e,t){var r;e.exports=((r=ee).pad.ZeroPadding={pad:function(e,t){var r=4*t;e.clamp(),e.sigBytes+=r-(e.sigBytes%r||r)},unpad:function(e){for(var t=e.words,r=e.sigBytes-1;!(t[r>>>2]>>>24-r%4*8&255);)r--;e.sigBytes=r+1}},r.pad.ZeroPadding)}),Q(function(e,t){var r;e.exports=((r=ee).pad.NoPadding={pad:function(){},unpad:function(){}},r.pad.NoPadding)}),Q(function(e,t){var r,n,i,o;e.exports=(i=(n=r=ee).lib.CipherParams,o=n.enc.Hex,n.format.Hex={stringify:function(e){return e.ciphertext.toString(o)},parse:function(e){var t=o.parse(e);return i.create({ciphertext:t})}},r.format.Hex)}),Q(function(e,t){var r;e.exports=(r=ee,function(){var e=r,t=e.lib.BlockCipher,n=e.algo,i=[],o=[],a=[],s=[],h=[],f=[],l=[],c=[],u=[],d=[];!function(){for(var e=[],t=0;t<256;t++)e[t]=t<128?t<<1:t<<1^283;var r=0,n=0;for(t=0;t<256;t++){var p=n^n<<1^n<<2^n<<3^n<<4;p=p>>>8^255&p^99,i[r]=p,o[p]=r;var _=e[r],g=e[_],v=e[g],w=257*e[p]^16843008*p;a[r]=w<<24|w>>>8,s[r]=w<<16|w>>>16,h[r]=w<<8|w>>>24,f[r]=w,w=16843009*v^65537*g^257*_^16843008*r,l[p]=w<<24|w>>>8,c[p]=w<<16|w>>>16,u[p]=w<<8|w>>>24,d[p]=w,r?(r=_^e[e[e[v^_]]],n^=e[e[n]]):r=n=1}}();var p=[0,1,2,4,8,16,32,64,128,27,54],_=n.AES=t.extend({_doReset:function(){if(!this._nRounds||this._keyPriorReset!==this._key){for(var e=this._keyPriorReset=this._key,t=e.words,r=e.sigBytes/4,n=4*((this._nRounds=r+6)+1),o=this._keySchedule=[],a=0;a<n;a++)if(a<r)o[a]=t[a];else{var s=o[a-1];a%r?r>6&&a%r==4&&(s=i[s>>>24]<<24|i[s>>>16&255]<<16|i[s>>>8&255]<<8|i[255&s]):(s=i[(s=s<<8|s>>>24)>>>24]<<24|i[s>>>16&255]<<16|i[s>>>8&255]<<8|i[255&s],s^=p[a/r|0]<<24),o[a]=o[a-r]^s}for(var h=this._invKeySchedule=[],f=0;f<n;f++)a=n-f,s=f%4?o[a]:o[a-4],h[f]=f<4||a<=4?s:l[i[s>>>24]]^c[i[s>>>16&255]]^u[i[s>>>8&255]]^d[i[255&s]]}},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._keySchedule,a,s,h,f,i)},decryptBlock:function(e,t){var r=e[t+1];e[t+1]=e[t+3],e[t+3]=r,this._doCryptBlock(e,t,this._invKeySchedule,l,c,u,d,o),r=e[t+1],e[t+1]=e[t+3],e[t+3]=r},_doCryptBlock:function(e,t,r,n,i,o,a,s){for(var h=this._nRounds,f=e[t]^r[0],l=e[t+1]^r[1],c=e[t+2]^r[2],u=e[t+3]^r[3],d=4,p=1;p<h;p++){var _=n[f>>>24]^i[l>>>16&255]^o[c>>>8&255]^a[255&u]^r[d++],g=n[l>>>24]^i[c>>>16&255]^o[u>>>8&255]^a[255&f]^r[d++],v=n[c>>>24]^i[u>>>16&255]^o[f>>>8&255]^a[255&l]^r[d++],w=n[u>>>24]^i[f>>>16&255]^o[l>>>8&255]^a[255&c]^r[d++];f=_,l=g,c=v,u=w}_=(s[f>>>24]<<24|s[l>>>16&255]<<16|s[c>>>8&255]<<8|s[255&u])^r[d++],g=(s[l>>>24]<<24|s[c>>>16&255]<<16|s[u>>>8&255]<<8|s[255&f])^r[d++],v=(s[c>>>24]<<24|s[u>>>16&255]<<16|s[f>>>8&255]<<8|s[255&l])^r[d++],w=(s[u>>>24]<<24|s[f>>>16&255]<<16|s[l>>>8&255]<<8|s[255&c])^r[d++],e[t]=_,e[t+1]=g,e[t+2]=v,e[t+3]=w},keySize:8});e.AES=t._createHelper(_)}(),r.AES)}),Q(function(e,t){var r;e.exports=(r=ee,function(){var e=r,t=e.lib,n=t.WordArray,i=t.BlockCipher,o=e.algo,a=[57,49,41,33,25,17,9,1,58,50,42,34,26,18,10,2,59,51,43,35,27,19,11,3,60,52,44,36,63,55,47,39,31,23,15,7,62,54,46,38,30,22,14,6,61,53,45,37,29,21,13,5,28,20,12,4],s=[14,17,11,24,1,5,3,28,15,6,21,10,23,19,12,4,26,8,16,7,27,20,13,2,41,52,31,37,47,55,30,40,51,45,33,48,44,49,39,56,34,53,46,42,50,36,29,32],h=[1,2,4,6,8,10,12,14,15,17,19,21,23,25,27,28],f=[{0:8421888,268435456:32768,536870912:8421378,805306368:2,1073741824:512,1342177280:8421890,1610612736:8389122,1879048192:8388608,2147483648:514,2415919104:8389120,2684354560:33280,2952790016:8421376,3221225472:32770,3489660928:8388610,3758096384:0,4026531840:33282,134217728:0,402653184:8421890,671088640:33282,939524096:32768,1207959552:8421888,1476395008:512,1744830464:8421378,2013265920:2,2281701376:8389120,2550136832:33280,2818572288:8421376,3087007744:8389122,3355443200:8388610,3623878656:32770,3892314112:514,4160749568:8388608,1:32768,268435457:2,536870913:8421888,805306369:8388608,1073741825:8421378,1342177281:33280,1610612737:512,1879048193:8389122,2147483649:8421890,2415919105:8421376,2684354561:8388610,2952790017:33282,3221225473:514,3489660929:8389120,3758096385:32770,4026531841:0,134217729:8421890,402653185:8421376,671088641:8388608,939524097:512,1207959553:32768,1476395009:8388610,1744830465:2,2013265921:33282,2281701377:32770,2550136833:8389122,2818572289:514,3087007745:8421888,3355443201:8389120,3623878657:0,3892314113:33280,4160749569:8421378},{0:1074282512,16777216:16384,33554432:524288,50331648:1074266128,67108864:1073741840,83886080:1074282496,100663296:1073758208,117440512:16,134217728:540672,150994944:1073758224,167772160:1073741824,184549376:540688,201326592:524304,218103808:0,234881024:16400,251658240:1074266112,8388608:1073758208,25165824:540688,41943040:16,58720256:1073758224,75497472:1074282512,92274688:1073741824,109051904:524288,125829120:1074266128,142606336:524304,159383552:0,176160768:16384,192937984:1074266112,209715200:1073741840,226492416:540672,243269632:1074282496,260046848:16400,268435456:0,285212672:1074266128,301989888:1073758224,318767104:1074282496,335544320:1074266112,352321536:16,369098752:540688,385875968:16384,402653184:16400,419430400:524288,436207616:524304,452984832:1073741840,469762048:540672,486539264:1073758208,503316480:1073741824,520093696:1074282512,276824064:540688,293601280:524288,310378496:1074266112,327155712:16384,343932928:1073758208,360710144:1074282512,377487360:16,394264576:1073741824,411041792:1074282496,427819008:1073741840,444596224:1073758224,461373440:524304,478150656:0,494927872:16400,511705088:1074266128,528482304:540672},{0:260,1048576:0,2097152:67109120,3145728:65796,4194304:65540,5242880:67108868,6291456:67174660,7340032:67174400,8388608:67108864,9437184:67174656,10485760:65792,11534336:67174404,12582912:67109124,13631488:65536,14680064:4,15728640:256,524288:67174656,1572864:67174404,2621440:0,3670016:67109120,4718592:67108868,5767168:65536,6815744:65540,7864320:260,8912896:4,9961472:256,11010048:67174400,12058624:65796,13107200:65792,14155776:67109124,15204352:67174660,16252928:67108864,16777216:67174656,17825792:65540,18874368:65536,19922944:67109120,20971520:256,22020096:67174660,23068672:67108868,24117248:0,25165824:67109124,26214400:67108864,27262976:4,28311552:65792,29360128:67174400,30408704:260,31457280:65796,32505856:67174404,17301504:67108864,18350080:260,19398656:67174656,20447232:0,21495808:65540,22544384:67109120,23592960:256,24641536:67174404,25690112:65536,26738688:67174660,27787264:65796,28835840:67108868,29884416:67109124,30932992:67174400,31981568:4,33030144:65792},{0:2151682048,65536:2147487808,131072:4198464,196608:2151677952,262144:0,327680:4198400,393216:2147483712,458752:4194368,524288:2147483648,589824:4194304,655360:64,720896:2147487744,786432:2151678016,851968:4160,917504:4096,983040:2151682112,32768:2147487808,98304:64,163840:2151678016,229376:2147487744,294912:4198400,360448:2151682112,425984:0,491520:2151677952,557056:4096,622592:2151682048,688128:4194304,753664:4160,819200:2147483648,884736:4194368,950272:4198464,1015808:2147483712,1048576:4194368,1114112:4198400,1179648:2147483712,1245184:0,1310720:4160,1376256:2151678016,1441792:2151682048,1507328:2147487808,1572864:2151682112,1638400:2147483648,1703936:2151677952,1769472:4198464,1835008:2147487744,1900544:4194304,1966080:64,2031616:4096,1081344:2151677952,1146880:2151682112,1212416:0,1277952:4198400,1343488:4194368,1409024:2147483648,1474560:2147487808,1540096:64,1605632:2147483712,1671168:4096,1736704:2147487744,1802240:2151678016,1867776:4160,1933312:2151682048,1998848:4194304,2064384:4198464},{0:128,4096:17039360,8192:262144,12288:536870912,16384:537133184,20480:16777344,24576:553648256,28672:262272,32768:16777216,36864:537133056,40960:536871040,45056:553910400,49152:553910272,53248:0,57344:17039488,61440:553648128,2048:17039488,6144:553648256,10240:128,14336:17039360,18432:262144,22528:537133184,26624:553910272,30720:536870912,34816:537133056,38912:0,43008:553910400,47104:16777344,51200:536871040,55296:553648128,59392:16777216,63488:262272,65536:262144,69632:128,73728:536870912,77824:553648256,81920:16777344,86016:553910272,90112:537133184,94208:16777216,98304:553910400,102400:553648128,106496:17039360,110592:537133056,114688:262272,118784:536871040,122880:0,126976:17039488,67584:553648256,71680:16777216,75776:17039360,79872:537133184,83968:536870912,88064:17039488,92160:128,96256:553910272,100352:262272,104448:553910400,108544:0,112640:553648128,116736:16777344,120832:262144,124928:537133056,129024:536871040},{0:268435464,256:8192,512:270532608,768:270540808,1024:268443648,1280:2097152,1536:2097160,1792:268435456,2048:0,2304:268443656,2560:2105344,2816:8,3072:270532616,3328:2105352,3584:8200,3840:270540800,128:270532608,384:270540808,640:8,896:2097152,1152:2105352,1408:268435464,1664:268443648,1920:8200,2176:2097160,2432:8192,2688:268443656,2944:270532616,3200:0,3456:270540800,3712:2105344,3968:268435456,4096:268443648,4352:270532616,4608:270540808,4864:8200,5120:2097152,5376:268435456,5632:268435464,5888:2105344,6144:2105352,6400:0,6656:8,6912:270532608,7168:8192,7424:268443656,7680:270540800,7936:2097160,4224:8,4480:2105344,4736:2097152,4992:268435464,5248:268443648,5504:8200,5760:270540808,6016:270532608,6272:270540800,6528:270532616,6784:8192,7040:2105352,7296:2097160,7552:0,7808:268435456,8064:268443656},{0:1048576,16:33555457,32:1024,48:1049601,64:34604033,80:0,96:1,112:34603009,128:33555456,144:1048577,160:33554433,176:34604032,192:34603008,208:1025,224:1049600,240:33554432,8:34603009,24:0,40:33555457,56:34604032,72:1048576,88:33554433,104:33554432,120:1025,136:1049601,152:33555456,168:34603008,184:1048577,200:1024,216:34604033,232:1,248:1049600,256:33554432,272:1048576,288:33555457,304:34603009,320:1048577,336:33555456,352:34604032,368:1049601,384:1025,400:34604033,416:1049600,432:1,448:0,464:34603008,480:33554433,496:1024,264:1049600,280:33555457,296:34603009,312:1,328:33554432,344:1048576,360:1025,376:34604032,392:33554433,408:34603008,424:0,440:34604033,456:1049601,472:1024,488:33555456,504:1048577},{0:134219808,1:131072,2:134217728,3:32,4:131104,5:134350880,6:134350848,7:2048,8:134348800,9:134219776,10:133120,11:134348832,12:2080,13:0,14:134217760,15:133152,2147483648:2048,2147483649:134350880,2147483650:134219808,2147483651:134217728,2147483652:134348800,2147483653:133120,2147483654:133152,2147483655:32,2147483656:134217760,2147483657:2080,2147483658:131104,2147483659:134350848,2147483660:0,2147483661:134348832,2147483662:134219776,2147483663:131072,16:133152,17:134350848,18:32,19:2048,20:134219776,21:134217760,22:134348832,23:131072,24:0,25:131104,26:134348800,27:134219808,28:134350880,29:133120,30:2080,31:134217728,2147483664:131072,2147483665:2048,2147483666:134348832,2147483667:133152,2147483668:32,2147483669:134348800,2147483670:134217728,2147483671:134219808,2147483672:134350880,2147483673:134217760,2147483674:134219776,2147483675:0,2147483676:133120,2147483677:2080,2147483678:131104,2147483679:134350848}],l=[4160749569,528482304,33030144,2064384,129024,8064,504,2147483679],c=o.DES=i.extend({_doReset:function(){for(var e=this._key.words,t=[],r=0;r<56;r++){var n=a[r]-1;t[r]=e[n>>>5]>>>31-n%32&1}for(var i=this._subKeys=[],o=0;o<16;o++){var f=i[o]=[],l=h[o];for(r=0;r<24;r++)f[r/6|0]|=t[(s[r]-1+l)%28]<<31-r%6,f[4+(r/6|0)]|=t[28+(s[r+24]-1+l)%28]<<31-r%6;for(f[0]=f[0]<<1|f[0]>>>31,r=1;r<7;r++)f[r]=f[r]>>>4*(r-1)+3;f[7]=f[7]<<5|f[7]>>>27}var c=this._invSubKeys=[];for(r=0;r<16;r++)c[r]=i[15-r]},encryptBlock:function(e,t){this._doCryptBlock(e,t,this._subKeys)},decryptBlock:function(e,t){this._doCryptBlock(e,t,this._invSubKeys)},_doCryptBlock:function(e,t,r){this._lBlock=e[t],this._rBlock=e[t+1],u.call(this,4,252645135),u.call(this,16,65535),d.call(this,2,858993459),d.call(this,8,16711935),u.call(this,1,1431655765);for(var n=0;n<16;n++){for(var i=r[n],o=this._lBlock,a=this._rBlock,s=0,h=0;h<8;h++)s|=f[h][((a^i[h])&l[h])>>>0];this._lBlock=a,this._rBlock=o^s}var c=this._lBlock;this._lBlock=this._rBlock,this._rBlock=c,u.call(this,1,1431655765),d.call(this,8,16711935),d.call(this,2,858993459),u.call(this,16,65535),u.call(this,4,252645135),e[t]=this._lBlock,e[t+1]=this._rBlock},keySize:2,ivSize:2,blockSize:2});function u(e,t){var r=(this._lBlock>>>e^this._rBlock)&t;this._rBlock^=r,this._lBlock^=r<<e}function d(e,t){var r=(this._rBlock>>>e^this._lBlock)&t;this._lBlock^=r,this._rBlock^=r<<e}e.DES=i._createHelper(c);var p=o.TripleDES=i.extend({_doReset:function(){var e=this._key.words;this._des1=c.createEncryptor(n.create(e.slice(0,2))),this._des2=c.createEncryptor(n.create(e.slice(2,4))),this._des3=c.createEncryptor(n.create(e.slice(4,6)))},encryptBlock:function(e,t){this._des1.encryptBlock(e,t),this._des2.decryptBlock(e,t),this._des3.encryptBlock(e,t)},decryptBlock:function(e,t){this._des3.decryptBlock(e,t),this._des2.encryptBlock(e,t),this._des1.decryptBlock(e,t)},keySize:6,ivSize:2,blockSize:2});e.TripleDES=i._createHelper(p)}(),r.TripleDES)}),Q(function(e,t){var r;e.exports=(r=ee,function(){var e=r,t=e.lib.StreamCipher,n=e.algo,i=n.RC4=t.extend({_doReset:function(){for(var e=this._key,t=e.words,r=e.sigBytes,n=this._S=[],i=0;i<256;i++)n[i]=i;i=0;for(var o=0;i<256;i++){var a=i%r,s=t[a>>>2]>>>24-a%4*8&255;o=(o+n[i]+s)%256;var h=n[i];n[i]=n[o],n[o]=h}this._i=this._j=0},_doProcessBlock:function(e,t){e[t]^=o.call(this)},keySize:8,ivSize:0});function o(){for(var e=this._S,t=this._i,r=this._j,n=0,i=0;i<4;i++){r=(r+e[t=(t+1)%256])%256;var o=e[t];e[t]=e[r],e[r]=o,n|=e[(e[t]+e[r])%256]<<24-8*i}return this._i=t,this._j=r,n}e.RC4=t._createHelper(i);var a=n.RC4Drop=i.extend({cfg:i.cfg.extend({drop:192}),_doReset:function(){i._doReset.call(this);for(var e=this.cfg.drop;e>0;e--)o.call(this)}});e.RC4Drop=t._createHelper(a)}(),r.RC4)}),Q(function(e,t){var r;e.exports=(r=ee,function(){var e=r,t=e.lib.StreamCipher,n=e.algo,i=[],o=[],a=[],s=n.Rabbit=t.extend({_doReset:function(){for(var e=this._key.words,t=this.cfg.iv,r=0;r<4;r++)e[r]=16711935&(e[r]<<8|e[r]>>>24)|4278255360&(e[r]<<24|e[r]>>>8);var n=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],i=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];for(this._b=0,r=0;r<4;r++)h.call(this);for(r=0;r<8;r++)i[r]^=n[r+4&7];if(t){var o=t.words,a=o[0],s=o[1],f=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),c=f>>>16|4294901760&l,u=l<<16|65535&f;for(i[0]^=f,i[1]^=c,i[2]^=l,i[3]^=u,i[4]^=f,i[5]^=c,i[6]^=l,i[7]^=u,r=0;r<4;r++)h.call(this)}},_doProcessBlock:function(e,t){var r=this._X;h.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function h(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,s=n>>>16,h=((i*i>>>17)+i*s>>>15)+s*s,f=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=h^f}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.Rabbit=t._createHelper(s)}(),r.Rabbit)}),Q(function(e,t){var r;e.exports=(r=ee,function(){var e=r,t=e.lib.StreamCipher,n=e.algo,i=[],o=[],a=[],s=n.RabbitLegacy=t.extend({_doReset:function(){var e=this._key.words,t=this.cfg.iv,r=this._X=[e[0],e[3]<<16|e[2]>>>16,e[1],e[0]<<16|e[3]>>>16,e[2],e[1]<<16|e[0]>>>16,e[3],e[2]<<16|e[1]>>>16],n=this._C=[e[2]<<16|e[2]>>>16,4294901760&e[0]|65535&e[1],e[3]<<16|e[3]>>>16,4294901760&e[1]|65535&e[2],e[0]<<16|e[0]>>>16,4294901760&e[2]|65535&e[3],e[1]<<16|e[1]>>>16,4294901760&e[3]|65535&e[0]];this._b=0;for(var i=0;i<4;i++)h.call(this);for(i=0;i<8;i++)n[i]^=r[i+4&7];if(t){var o=t.words,a=o[0],s=o[1],f=16711935&(a<<8|a>>>24)|4278255360&(a<<24|a>>>8),l=16711935&(s<<8|s>>>24)|4278255360&(s<<24|s>>>8),c=f>>>16|4294901760&l,u=l<<16|65535&f;for(n[0]^=f,n[1]^=c,n[2]^=l,n[3]^=u,n[4]^=f,n[5]^=c,n[6]^=l,n[7]^=u,i=0;i<4;i++)h.call(this)}},_doProcessBlock:function(e,t){var r=this._X;h.call(this),i[0]=r[0]^r[5]>>>16^r[3]<<16,i[1]=r[2]^r[7]>>>16^r[5]<<16,i[2]=r[4]^r[1]>>>16^r[7]<<16,i[3]=r[6]^r[3]>>>16^r[1]<<16;for(var n=0;n<4;n++)i[n]=16711935&(i[n]<<8|i[n]>>>24)|4278255360&(i[n]<<24|i[n]>>>8),e[t+n]^=i[n]},blockSize:4,ivSize:2});function h(){for(var e=this._X,t=this._C,r=0;r<8;r++)o[r]=t[r];for(t[0]=t[0]+1295307597+this._b|0,t[1]=t[1]+3545052371+(t[0]>>>0<o[0]>>>0?1:0)|0,t[2]=t[2]+886263092+(t[1]>>>0<o[1]>>>0?1:0)|0,t[3]=t[3]+1295307597+(t[2]>>>0<o[2]>>>0?1:0)|0,t[4]=t[4]+3545052371+(t[3]>>>0<o[3]>>>0?1:0)|0,t[5]=t[5]+886263092+(t[4]>>>0<o[4]>>>0?1:0)|0,t[6]=t[6]+1295307597+(t[5]>>>0<o[5]>>>0?1:0)|0,t[7]=t[7]+3545052371+(t[6]>>>0<o[6]>>>0?1:0)|0,this._b=t[7]>>>0<o[7]>>>0?1:0,r=0;r<8;r++){var n=e[r]+t[r],i=65535&n,s=n>>>16,h=((i*i>>>17)+i*s>>>15)+s*s,f=((4294901760&n)*n|0)+((65535&n)*n|0);a[r]=h^f}e[0]=a[0]+(a[7]<<16|a[7]>>>16)+(a[6]<<16|a[6]>>>16)|0,e[1]=a[1]+(a[0]<<8|a[0]>>>24)+a[7]|0,e[2]=a[2]+(a[1]<<16|a[1]>>>16)+(a[0]<<16|a[0]>>>16)|0,e[3]=a[3]+(a[2]<<8|a[2]>>>24)+a[1]|0,e[4]=a[4]+(a[3]<<16|a[3]>>>16)+(a[2]<<16|a[2]>>>16)|0,e[5]=a[5]+(a[4]<<8|a[4]>>>24)+a[3]|0,e[6]=a[6]+(a[5]<<16|a[5]>>>16)+(a[4]<<16|a[4]>>>16)|0,e[7]=a[7]+(a[6]<<8|a[6]>>>24)+a[5]|0}e.RabbitLegacy=t._createHelper(s)}(),r.RabbitLegacy)}),Q(function(e,t){e.exports=ee}));function re(){throw new Error("setTimeout has not been defined")}function ne(){throw new Error("clearTimeout has not been defined")}var ie=re,oe=ne;function ae(e){if(ie===setTimeout)return setTimeout(e,0);if((ie===re||!ie)&&setTimeout)return ie=setTimeout,setTimeout(e,0);try{return ie(e,0)}catch(t){try{return ie.call(null,e,0)}catch(t){return ie.call(this,e,0)}}}"function"==typeof e.setTimeout&&(ie=setTimeout),"function"==typeof e.clearTimeout&&(oe=clearTimeout);var se,he=[],fe=!1,le=-1;function ce(){fe&&se&&(fe=!1,se.length?he=se.concat(he):le=-1,he.length&&ue())}function ue(){if(!fe){var e=ae(ce);fe=!0;for(var t=he.length;t;){for(se=he,he=[];++le<t;)se&&se[le].run();le=-1,t=he.length}se=null,fe=!1,function(e){if(oe===clearTimeout)return clearTimeout(e);if((oe===ne||!oe)&&clearTimeout)return oe=clearTimeout,clearTimeout(e);try{oe(e)}catch(t){try{return oe.call(null,e)}catch(t){return oe.call(this,e)}}}(e)}}function de(e){var t=new Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];he.push(new pe(e,t)),1!==he.length||fe||ae(ue)}function pe(e,t){this.fun=e,this.array=t}pe.prototype.run=function(){this.fun.apply(null,this.array)};var _e=e.performance||{};_e.now||_e.mozNow||_e.msNow||_e.oNow||_e.webkitNow;function ge(){}function ve(){ve.init.call(this)}function we(e){return void 0===e._maxListeners?ve.defaultMaxListeners:e._maxListeners}function be(e,t,r){if(t)e.call(r);else for(var n=e.length,i=Ae(e,n),o=0;o<n;++o)i[o].call(r)}function ye(e,t,r,n){if(t)e.call(r,n);else for(var i=e.length,o=Ae(e,i),a=0;a<i;++a)o[a].call(r,n)}function me(e,t,r,n,i){if(t)e.call(r,n,i);else for(var o=e.length,a=Ae(e,o),s=0;s<o;++s)a[s].call(r,n,i)}function ke(e,t,r,n,i,o){if(t)e.call(r,n,i,o);else for(var a=e.length,s=Ae(e,a),h=0;h<a;++h)s[h].call(r,n,i,o)}function Ee(e,t,r,n){if(t)e.apply(r,n);else for(var i=e.length,o=Ae(e,i),a=0;a<i;++a)o[a].apply(r,n)}function Se(e,t,r,n){var i,o,a,s;if("function"!=typeof r)throw new TypeError('"listener" argument must be a function');if((o=e._events)?(o.newListener&&(e.emit("newListener",t,r.listener?r.listener:r),o=e._events),a=o[t]):(o=e._events=new ge,e._eventsCount=0),a){if("function"==typeof a?a=o[t]=n?[r,a]:[a,r]:n?a.unshift(r):a.push(r),!a.warned&&(i=we(e))&&i>0&&a.length>i){a.warned=!0;var h=new Error("Possible EventEmitter memory leak detected. "+a.length+" "+t+" listeners added. Use emitter.setMaxListeners() to increase limit");h.name="MaxListenersExceededWarning",h.emitter=e,h.type=t,h.count=a.length,s=h,"function"==typeof console.demoWarn?console.demoWarn(s):console.log(s)}}else a=o[t]=r,++e._eventsCount;return e}function xe(e,t,r){var n=!1;function i(){e.removeListener(t,i),n||(n=!0,r.apply(e,arguments))}return i.listener=r,i}function Re(e){var t=this._events;if(t){var r=t[e];if("function"==typeof r)return 1;if(r)return r.length}return 0}function Ae(e,t){for(var r=new Array(t);t--;)r[t]=e[t];return r}ge.prototype=Object.create(null),ve.EventEmitter=ve,ve.usingDomains=!1,ve.prototype.domain=void 0,ve.prototype._events=void 0,ve.prototype._maxListeners=void 0,ve.defaultMaxListeners=10,ve.init=function(){this.domain=null,ve.usingDomains&&(void 0).active&&(void 0).Domain,this._events&&this._events!==Object.getPrototypeOf(this)._events||(this._events=new ge,this._eventsCount=0),this._maxListeners=this._maxListeners||void 0},ve.prototype.setMaxListeners=function(e){if("number"!=typeof e||e<0||isNaN(e))throw new TypeError('"n" argument must be a positive number');return this._maxListeners=e,this},ve.prototype.getMaxListeners=function(){return we(this)},ve.prototype.emit=function(e){var t,r,n,i,o,a,s,h="error"===e;if(a=this._events)h=h&&null==a.error;else if(!h)return!1;if(s=this.domain,h){if(t=arguments[1],!s){if(t instanceof Error)throw t;var f=new Error('Uncaught, unspecified "error" event. ('+t+")");throw f.context=t,f}return t||(t=new Error('Uncaught, unspecified "error" event')),t.domainEmitter=this,t.domain=s,t.domainThrown=!1,s.emit("error",t),!1}if(!(r=a[e]))return!1;var l="function"==typeof r;switch(n=arguments.length){case 1:be(r,l,this);break;case 2:ye(r,l,this,arguments[1]);break;case 3:me(r,l,this,arguments[1],arguments[2]);break;case 4:ke(r,l,this,arguments[1],arguments[2],arguments[3]);break;default:for(i=new Array(n-1),o=1;o<n;o++)i[o-1]=arguments[o];Ee(r,l,this,i)}return!0},ve.prototype.addListener=function(e,t){return Se(this,e,t,!1)},ve.prototype.on=ve.prototype.addListener,ve.prototype.prependListener=function(e,t){return Se(this,e,t,!0)},ve.prototype.once=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.on(e,xe(this,e,t)),this},ve.prototype.prependOnceListener=function(e,t){if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');return this.prependListener(e,xe(this,e,t)),this},ve.prototype.removeListener=function(e,t){var r,n,i,o,a;if("function"!=typeof t)throw new TypeError('"listener" argument must be a function');if(!(n=this._events))return this;if(!(r=n[e]))return this;if(r===t||r.listener&&r.listener===t)0==--this._eventsCount?this._events=new ge:(delete n[e],n.removeListener&&this.emit("removeListener",e,r.listener||t));else if("function"!=typeof r){for(i=-1,o=r.length;o-- >0;)if(r[o]===t||r[o].listener&&r[o].listener===t){a=r[o].listener,i=o;break}if(i<0)return this;if(1===r.length){if(r[0]=void 0,0==--this._eventsCount)return this._events=new ge,this;delete n[e]}else!function(e,t){for(var r=t,n=r+1,i=e.length;n<i;r+=1,n+=1)e[r]=e[n];e.pop()}(r,i);n.removeListener&&this.emit("removeListener",e,a||t)}return this},ve.prototype.removeAllListeners=function(e){var t,r;if(!(r=this._events))return this;if(!r.removeListener)return 0===arguments.length?(this._events=new ge,this._eventsCount=0):r[e]&&(0==--this._eventsCount?this._events=new ge:delete r[e]),this;if(0===arguments.length){for(var n,i=Object.keys(r),o=0;o<i.length;++o)"removeListener"!==(n=i[o])&&this.removeAllListeners(n);return this.removeAllListeners("removeListener"),this._events=new ge,this._eventsCount=0,this}if("function"==typeof(t=r[e]))this.removeListener(e,t);else if(t)do{this.removeListener(e,t[t.length-1])}while(t[0]);return this},ve.prototype.listeners=function(e){var t,r=this._events;return r&&(t=r[e])?"function"==typeof t?[t.listener||t]:function(e){for(var t=new Array(e.length),r=0;r<t.length;++r)t[r]=e[r].listener||e[r];return t}(t):[]},ve.listenerCount=function(e,t){return"function"==typeof e.listenerCount?e.listenerCount(t):Re.call(e,t)},ve.prototype.listenerCount=Re,ve.prototype.eventNames=function(){return this._eventsCount>0?Reflect.ownKeys(this._events):[]};var Be="function"==typeof Object.create?function(e,t){e.super_=t,e.prototype=Object.create(t.prototype,{constructor:{value:e,enumerable:!1,writable:!0,configurable:!0}})}:function(e,t){e.super_=t;var r=function(){};r.prototype=t.prototype,e.prototype=new r,e.prototype.constructor=e},ze=/%[sdj%]/g;function Le(e){if(!Ze(e)){for(var t=[],r=0;r<arguments.length;r++)t.push(De(arguments[r]));return t.join(" ")}r=1;for(var n=arguments,i=n.length,o=String(e).replace(ze,function(e){if("%%"===e)return"%";if(r>=i)return e;switch(e){case"%s":return String(n[r++]);case"%d":return Number(n[r++]);case"%j":try{return JSON.stringify(n[r++])}catch(e){return"[Circular]"}default:return e}}),a=n[r];r<i;a=n[++r])Ne(a)||!Ye(a)?o+=" "+a:o+=" "+De(a);return o}function Te(t,r){if(je(e.process))return function(){return Te(t,r).apply(this,arguments)};var n=!1;return function(){return n||(console.error(r),n=!0),t.apply(this,arguments)}}var Me,Ce={};function De(e,t){var r={seen:[],stylize:Pe};return arguments.length>=3&&(r.depth=arguments[2]),arguments.length>=4&&(r.colors=arguments[3]),Fe(t)?r.showHidden=t:t&&function(e,t){if(!t||!Ye(t))return e;var r=Object.keys(t),n=r.length;for(;n--;)e[r[n]]=t[r[n]]}(r,t),je(r.showHidden)&&(r.showHidden=!1),je(r.depth)&&(r.depth=2),je(r.colors)&&(r.colors=!1),je(r.customInspect)&&(r.customInspect=!0),r.colors&&(r.stylize=Ie),Oe(r,e,r.depth)}function Ie(e,t){var r=De.styles[t];return r?"["+De.colors[r][0]+"m"+e+"["+De.colors[r][1]+"m":e}function Pe(e,t){return e}function Oe(e,t,r){if(e.customInspect&&t&&qe(t.inspect)&&t.inspect!==De&&(!t.constructor||t.constructor.prototype!==t)){var n=t.inspect(r,e);return Ze(n)||(n=Oe(e,n,r)),n}var i=function(e,t){if(je(t))return e.stylize("undefined","undefined");if(Ze(t)){var r="'"+JSON.stringify(t).replace(/^"|"$/g,"").replace(/'/g,"\\'").replace(/\\"/g,'"')+"'";return e.stylize(r,"string")}if(n=t,"number"==typeof n)return e.stylize(""+t,"number");var n;if(Fe(t))return e.stylize(""+t,"boolean");if(Ne(t))return e.stylize("null","null")}(e,t);if(i)return i;var o=Object.keys(t),a=function(e){var t={};return e.forEach(function(e,r){t[e]=!0}),t}(o);if(e.showHidden&&(o=Object.getOwnPropertyNames(t)),Xe(t)&&(o.indexOf("message")>=0||o.indexOf("description")>=0))return Ue(t);if(0===o.length){if(qe(t)){var s=t.name?": "+t.name:"";return e.stylize("[Function"+s+"]","special")}if(We(t))return e.stylize(RegExp.prototype.toString.call(t),"regexp");if(Ke(t))return e.stylize(Date.prototype.toString.call(t),"date");if(Xe(t))return Ue(t)}var h,f,l="",c=!1,u=["{","}"];(h=t,Array.isArray(h)&&(c=!0,u=["[","]"]),qe(t))&&(l=" [Function"+(t.name?": "+t.name:"")+"]");return We(t)&&(l=" "+RegExp.prototype.toString.call(t)),Ke(t)&&(l=" "+Date.prototype.toUTCString.call(t)),Xe(t)&&(l=" "+Ue(t)),0!==o.length||c&&0!=t.length?r<0?We(t)?e.stylize(RegExp.prototype.toString.call(t),"regexp"):e.stylize("[Object]","special"):(e.seen.push(t),f=c?function(e,t,r,n,i){for(var o=[],a=0,s=t.length;a<s;++a)Ge(t,String(a))?o.push(He(e,t,r,n,String(a),!0)):o.push("");return i.forEach(function(i){i.match(/^\d+$/)||o.push(He(e,t,r,n,i,!0))}),o}(e,t,r,a,o):o.map(function(n){return He(e,t,r,a,n,c)}),e.seen.pop(),function(e,t,r){if(e.reduce(function(e,t){return t.indexOf("\n"),e+t.replace(/\u001b\[\d\d?m/g,"").length+1},0)>60)return r[0]+(""===t?"":t+"\n ")+" "+e.join(",\n  ")+" "+r[1];return r[0]+t+" "+e.join(", ")+" "+r[1]}(f,l,u)):u[0]+l+u[1]}function Ue(e){return"["+Error.prototype.toString.call(e)+"]"}function He(e,t,r,n,i,o){var a,s,h;if((h=Object.getOwnPropertyDescriptor(t,i)||{value:t[i]}).get?s=h.set?e.stylize("[Getter/Setter]","special"):e.stylize("[Getter]","special"):h.set&&(s=e.stylize("[Setter]","special")),Ge(n,i)||(a="["+i+"]"),s||(e.seen.indexOf(h.value)<0?(s=Ne(r)?Oe(e,h.value,null):Oe(e,h.value,r-1)).indexOf("\n")>-1&&(s=o?s.split("\n").map(function(e){return"  "+e}).join("\n").substr(2):"\n"+s.split("\n").map(function(e){return"   "+e}).join("\n")):s=e.stylize("[Circular]","special")),je(a)){if(o&&i.match(/^\d+$/))return s;(a=JSON.stringify(""+i)).match(/^"([a-zA-Z_][a-zA-Z_0-9]*)"$/)?(a=a.substr(1,a.length-2),a=e.stylize(a,"name")):(a=a.replace(/'/g,"\\'").replace(/\\"/g,'"').replace(/(^"|"$)/g,"'"),a=e.stylize(a,"string"))}return a+": "+s}function Fe(e){return"boolean"==typeof e}function Ne(e){return null===e}function Ze(e){return"string"==typeof e}function je(e){return void 0===e}function We(e){return Ye(e)&&"[object RegExp]"===Ve(e)}function Ye(e){return"object"==typeof e&&null!==e}function Ke(e){return Ye(e)&&"[object Date]"===Ve(e)}function Xe(e){return Ye(e)&&("[object Error]"===Ve(e)||e instanceof Error)}function qe(e){return"function"==typeof e}function Ve(e){return Object.prototype.toString.call(e)}function Ge(e,t){return Object.prototype.hasOwnProperty.call(e,t)}function $e(){this.head=null,this.tail=null,this.length=0}De.colors={bold:[1,22],italic:[3,23],underline:[4,24],inverse:[7,27],white:[37,39],grey:[90,39],black:[30,39],blue:[34,39],cyan:[36,39],green:[32,39],magenta:[35,39],red:[31,39],yellow:[33,39]},De.styles={special:"cyan",number:"yellow",boolean:"yellow",undefined:"grey",null:"bold",string:"green",date:"magenta",regexp:"red"},$e.prototype.push=function(e){var t={data:e,next:null};this.length>0?this.tail.next=t:this.head=t,this.tail=t,++this.length},$e.prototype.unshift=function(e){var t={data:e,next:this.head};0===this.length&&(this.tail=t),this.head=t,++this.length},$e.prototype.shift=function(){if(0!==this.length){var e=this.head.data;return 1===this.length?this.head=this.tail=null:this.head=this.head.next,--this.length,e}},$e.prototype.clear=function(){this.head=this.tail=null,this.length=0},$e.prototype.join=function(e){if(0===this.length)return"";for(var t=this.head,r=""+t.data;t=t.next;)r+=e+t.data;return r},$e.prototype.concat=function(e){if(0===this.length)return p.alloc(0);if(1===this.length)return this.head.data;for(var t=p.allocUnsafe(e>>>0),r=this.head,n=0;r;)r.data.copy(t,n),n+=r.data.length,r=r.next;return t};var Je=p.isEncoding||function(e){switch(e&&e.toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":case"raw":return!0;default:return!1}};function Qe(e){switch(this.encoding=(e||"utf8").toLowerCase().replace(/[-_]/,""),function(e){if(e&&!Je(e))throw new Error("Unknown encoding: "+e)}(e),this.encoding){case"utf8":this.surrogateSize=3;break;case"ucs2":case"utf16le":this.surrogateSize=2,this.detectIncompleteChar=tt;break;case"base64":this.surrogateSize=3,this.detectIncompleteChar=rt;break;default:return void(this.write=et)}this.charBuffer=new p(6),this.charReceived=0,this.charLength=0}function et(e){return e.toString(this.encoding)}function tt(e){this.charReceived=e.length%2,this.charLength=this.charReceived?2:0}function rt(e){this.charReceived=e.length%3,this.charLength=this.charReceived?3:0}Qe.prototype.write=function(e){for(var t="";this.charLength;){var r=e.length>=this.charLength-this.charReceived?this.charLength-this.charReceived:e.length;if(e.copy(this.charBuffer,this.charReceived,0,r),this.charReceived+=r,this.charReceived<this.charLength)return"";if(e=e.slice(r,e.length),!((i=(t=this.charBuffer.slice(0,this.charLength).toString(this.encoding)).charCodeAt(t.length-1))>=55296&&i<=56319)){if(this.charReceived=this.charLength=0,0===e.length)return t;break}this.charLength+=this.surrogateSize,t=""}this.detectIncompleteChar(e);var n=e.length;this.charLength&&(e.copy(this.charBuffer,0,e.length-this.charReceived,n),n-=this.charReceived);var i;n=(t+=e.toString(this.encoding,0,n)).length-1;if((i=t.charCodeAt(n))>=55296&&i<=56319){var o=this.surrogateSize;return this.charLength+=o,this.charReceived+=o,this.charBuffer.copy(this.charBuffer,o,0,o),e.copy(this.charBuffer,0,0,o),t.substring(0,n)}return t},Qe.prototype.detectIncompleteChar=function(e){for(var t=e.length>=3?3:e.length;t>0;t--){var r=e[e.length-t];if(1==t&&r>>5==6){this.charLength=2;break}if(t<=2&&r>>4==14){this.charLength=3;break}if(t<=3&&r>>3==30){this.charLength=4;break}}this.charReceived=t},Qe.prototype.end=function(e){var t="";if(e&&e.length&&(t=this.write(e)),this.charReceived){var r=this.charReceived,n=this.charBuffer,i=this.encoding;t+=n.slice(0,r).toString(i)}return t},ot.ReadableState=it;var nt=function(e){je(Me)&&(Me=""),e=e.toUpperCase(),Ce[e]||(new RegExp("\\b"+e+"\\b","i").test(Me)?Ce[e]=function(){var t=Le.apply(null,arguments);console.error("%s %d: %s",e,0,t)}:Ce[e]=function(){});return Ce[e]}("stream");function it(e,t){e=e||{},this.objectMode=!!e.objectMode,t instanceof Ct&&(this.objectMode=this.objectMode||!!e.readableObjectMode);var r=e.highWaterMark,n=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:n,this.highWaterMark=~~this.highWaterMark,this.buffer=new $e,this.length=0,this.pipes=null,this.pipesCount=0,this.flowing=null,this.ended=!1,this.endEmitted=!1,this.reading=!1,this.sync=!0,this.needReadable=!1,this.emittedReadable=!1,this.readableListening=!1,this.resumeScheduled=!1,this.defaultEncoding=e.defaultEncoding||"utf8",this.ranOut=!1,this.awaitDrain=0,this.readingMore=!1,this.decoder=null,this.encoding=null,e.encoding&&(this.decoder=new Qe(e.encoding),this.encoding=e.encoding)}function ot(e){if(!(this instanceof ot))return new ot(e);this._readableState=new it(e,this),this.readable=!0,e&&"function"==typeof e.read&&(this._read=e.read),ve.call(this)}function at(e,t,r,n,i){var o=function(e,t){var r=null;$(t)||"string"==typeof t||null==t||e.objectMode||(r=new TypeError("Invalid non-string/buffer chunk"));return r}(t,r);if(o)e.emit("error",o);else if(null===r)t.reading=!1,function(e,t){if(t.ended)return;if(t.decoder){var r=t.decoder.end();r&&r.length&&(t.buffer.push(r),t.length+=t.objectMode?1:r.length)}t.ended=!0,ft(e)}(e,t);else if(t.objectMode||r&&r.length>0)if(t.ended&&!i){var a=new Error("stream.push() after EOF");e.emit("error",a)}else if(t.endEmitted&&i){var s=new Error("stream.unshift() after end event");e.emit("error",s)}else{var h;!t.decoder||i||n||(r=t.decoder.write(r),h=!t.objectMode&&0===r.length),i||(t.reading=!1),h||(t.flowing&&0===t.length&&!t.sync?(e.emit("data",r),e.read(0)):(t.length+=t.objectMode?1:r.length,i?t.buffer.unshift(r):t.buffer.push(r),t.needReadable&&ft(e))),function(e,t){t.readingMore||(t.readingMore=!0,de(ct,e,t))}(e,t)}else i||(t.reading=!1);return function(e){return!e.ended&&(e.needReadable||e.length<e.highWaterMark||0===e.length)}(t)}Be(ot,ve),ot.prototype.push=function(e,t){var r=this._readableState;return r.objectMode||"string"!=typeof e||(t=t||r.defaultEncoding)!==r.encoding&&(e=p.from(e,t),t=""),at(this,r,e,t,!1)},ot.prototype.unshift=function(e){return at(this,this._readableState,e,"",!0)},ot.prototype.isPaused=function(){return!1===this._readableState.flowing},ot.prototype.setEncoding=function(e){return this._readableState.decoder=new Qe(e),this._readableState.encoding=e,this};var st=8388608;function ht(e,t){return e<=0||0===t.length&&t.ended?0:t.objectMode?1:e!=e?t.flowing&&t.length?t.buffer.head.data.length:t.length:(e>t.highWaterMark&&(t.highWaterMark=function(e){return e>=st?e=st:(e--,e|=e>>>1,e|=e>>>2,e|=e>>>4,e|=e>>>8,e|=e>>>16,e++),e}(e)),e<=t.length?e:t.ended?t.length:(t.needReadable=!0,0))}function ft(e){var t=e._readableState;t.needReadable=!1,t.emittedReadable||(nt("emitReadable",t.flowing),t.emittedReadable=!0,t.sync?de(lt,e):lt(e))}function lt(e){nt("emit readable"),e.emit("readable"),pt(e)}function ct(e,t){for(var r=t.length;!t.reading&&!t.flowing&&!t.ended&&t.length<t.highWaterMark&&(nt("maybeReadMore read 0"),e.read(0),r!==t.length);)r=t.length;t.readingMore=!1}function ut(e){nt("readable nexttick read 0"),e.read(0)}function dt(e,t){t.reading||(nt("resume read 0"),e.read(0)),t.resumeScheduled=!1,t.awaitDrain=0,e.emit("resume"),pt(e),t.flowing&&!t.reading&&e.read(0)}function pt(e){var t=e._readableState;for(nt("flow",t.flowing);t.flowing&&null!==e.read(););}function _t(e,t){return 0===t.length?null:(t.objectMode?r=t.buffer.shift():!e||e>=t.length?(r=t.decoder?t.buffer.join(""):1===t.buffer.length?t.buffer.head.data:t.buffer.concat(t.length),t.buffer.clear()):r=function(e,t,r){var n;e<t.head.data.length?(n=t.head.data.slice(0,e),t.head.data=t.head.data.slice(e)):n=e===t.head.data.length?t.shift():r?function(e,t){var r=t.head,n=1,i=r.data;e-=i.length;for(;r=r.next;){var o=r.data,a=e>o.length?o.length:e;if(a===o.length?i+=o:i+=o.slice(0,e),0===(e-=a)){a===o.length?(++n,r.next?t.head=r.next:t.head=t.tail=null):(t.head=r,r.data=o.slice(a));break}++n}return t.length-=n,i}(e,t):function(e,t){var r=p.allocUnsafe(e),n=t.head,i=1;n.data.copy(r),e-=n.data.length;for(;n=n.next;){var o=n.data,a=e>o.length?o.length:e;if(o.copy(r,r.length-e,0,a),0===(e-=a)){a===o.length?(++i,n.next?t.head=n.next:t.head=t.tail=null):(t.head=n,n.data=o.slice(a));break}++i}return t.length-=i,r}(e,t);return n}(e,t.buffer,t.decoder),r);var r}function gt(e){var t=e._readableState;if(t.length>0)throw new Error('"endReadable()" called on non-empty stream');t.endEmitted||(t.ended=!0,de(vt,t,e))}function vt(e,t){e.endEmitted||0!==e.length||(e.endEmitted=!0,t.readable=!1,t.emit("end"))}function wt(e,t){for(var r=0,n=e.length;r<n;r++)if(e[r]===t)return r;return-1}function bt(){}function yt(e,t,r){this.chunk=e,this.encoding=t,this.callback=r,this.next=null}function mt(e,t){Object.defineProperty(this,"buffer",{get:Te(function(){return this.getBuffer()},"_writableState.buffer is deprecated. Use _writableState.getBuffer instead.")}),e=e||{},this.objectMode=!!e.objectMode,t instanceof Ct&&(this.objectMode=this.objectMode||!!e.writableObjectMode);var r=e.highWaterMark,n=this.objectMode?16:16384;this.highWaterMark=r||0===r?r:n,this.highWaterMark=~~this.highWaterMark,this.needDrain=!1,this.ending=!1,this.ended=!1,this.finished=!1;var i=!1===e.decodeStrings;this.decodeStrings=!i,this.defaultEncoding=e.defaultEncoding||"utf8",this.length=0,this.writing=!1,this.corked=0,this.sync=!0,this.bufferProcessing=!1,this.onwrite=function(e){!function(e,t){var r=e._writableState,n=r.sync,i=r.writecb;if(function(e){e.writing=!1,e.writecb=null,e.length-=e.writelen,e.writelen=0}(r),t)!function(e,t,r,n,i){--t.pendingcb,r?de(i,n):i(n);e._writableState.errorEmitted=!0,e.emit("error",n)}(e,r,n,t,i);else{var o=Rt(r);o||r.corked||r.bufferProcessing||!r.bufferedRequest||xt(e,r),n?de(St,e,r,o,i):St(e,r,o,i)}}(t,e)},this.writecb=null,this.writelen=0,this.bufferedRequest=null,this.lastBufferedRequest=null,this.pendingcb=0,this.prefinished=!1,this.errorEmitted=!1,this.bufferedRequestCount=0,this.corkedRequestsFree=new zt(this)}function kt(e){if(!(this instanceof kt||this instanceof Ct))return new kt(e);this._writableState=new mt(e,this),this.writable=!0,e&&("function"==typeof e.write&&(this._write=e.write),"function"==typeof e.writev&&(this._writev=e.writev)),ve.call(this)}function Et(e,t,r,n,i,o,a){t.writelen=n,t.writecb=a,t.writing=!0,t.sync=!0,r?e._writev(i,t.onwrite):e._write(i,o,t.onwrite),t.sync=!1}function St(e,t,r,n){r||function(e,t){0===t.length&&t.needDrain&&(t.needDrain=!1,e.emit("drain"))}(e,t),t.pendingcb--,n(),Bt(e,t)}function xt(e,t){t.bufferProcessing=!0;var r=t.bufferedRequest;if(e._writev&&r&&r.next){var n=t.bufferedRequestCount,i=new Array(n),o=t.corkedRequestsFree;o.entry=r;for(var a=0;r;)i[a]=r,r=r.next,a+=1;Et(e,t,!0,t.length,i,"",o.finish),t.pendingcb++,t.lastBufferedRequest=null,o.next?(t.corkedRequestsFree=o.next,o.next=null):t.corkedRequestsFree=new zt(t)}else{for(;r;){var s=r.chunk,h=r.encoding,f=r.callback;if(Et(e,t,!1,t.objectMode?1:s.length,s,h,f),r=r.next,t.writing)break}null===r&&(t.lastBufferedRequest=null)}t.bufferedRequestCount=0,t.bufferedRequest=r,t.bufferProcessing=!1}function Rt(e){return e.ending&&0===e.length&&null===e.bufferedRequest&&!e.finished&&!e.writing}function At(e,t){t.prefinished||(t.prefinished=!0,e.emit("prefinish"))}function Bt(e,t){var r=Rt(t);return r&&(0===t.pendingcb?(At(e,t),t.finished=!0,e.emit("finish")):At(e,t)),r}function zt(e){var t=this;this.next=null,this.entry=null,this.finish=function(r){var n=t.entry;for(t.entry=null;n;){var i=n.callback;e.pendingcb--,i(r),n=n.next}e.corkedRequestsFree?e.corkedRequestsFree.next=t:e.corkedRequestsFree=t}}ot.prototype.read=function(e){nt("read",e),e=parseInt(e,10);var t=this._readableState,r=e;if(0!==e&&(t.emittedReadable=!1),0===e&&t.needReadable&&(t.length>=t.highWaterMark||t.ended))return nt("read: emitReadable",t.length,t.ended),0===t.length&&t.ended?gt(this):ft(this),null;if(0===(e=ht(e,t))&&t.ended)return 0===t.length&&gt(this),null;var n,i=t.needReadable;return nt("need readable",i),(0===t.length||t.length-e<t.highWaterMark)&&nt("length less than watermark",i=!0),t.ended||t.reading?nt("reading or ended",i=!1):i&&(nt("do read"),t.reading=!0,t.sync=!0,0===t.length&&(t.needReadable=!0),this._read(t.highWaterMark),t.sync=!1,t.reading||(e=ht(r,t))),null===(n=e>0?_t(e,t):null)?(t.needReadable=!0,e=0):t.length-=e,0===t.length&&(t.ended||(t.needReadable=!0),r!==e&&t.ended&&gt(this)),null!==n&&this.emit("data",n),n},ot.prototype._read=function(e){this.emit("error",new Error("not implemented"))},ot.prototype.pipe=function(e,t){var r=this,n=this._readableState;switch(n.pipesCount){case 0:n.pipes=e;break;case 1:n.pipes=[n.pipes,e];break;default:n.pipes.push(e)}n.pipesCount+=1,nt("pipe count=%d opts=%j",n.pipesCount,t);var i=!t||!1!==t.end?a:f;function o(e){nt("onunpipe"),e===r&&f()}function a(){nt("onend"),e.end()}n.endEmitted?de(i):r.once("end",i),e.on("unpipe",o);var s=function(e){return function(){var t=e._readableState;nt("pipeOnDrain",t.awaitDrain),t.awaitDrain&&t.awaitDrain--,0===t.awaitDrain&&e.listeners("data").length&&(t.flowing=!0,pt(e))}}(r);e.on("drain",s);var h=!1;function f(){nt("cleanup"),e.removeListener("close",d),e.removeListener("finish",p),e.removeListener("drain",s),e.removeListener("error",u),e.removeListener("unpipe",o),r.removeListener("end",a),r.removeListener("end",f),r.removeListener("data",c),h=!0,!n.awaitDrain||e._writableState&&!e._writableState.needDrain||s()}var l=!1;function c(t){nt("ondata"),l=!1,!1!==e.write(t)||l||((1===n.pipesCount&&n.pipes===e||n.pipesCount>1&&-1!==wt(n.pipes,e))&&!h&&(nt("false write response, pause",r._readableState.awaitDrain),r._readableState.awaitDrain++,l=!0),r.pause())}function u(t){var r;nt("onerror",t),_(),e.removeListener("error",u),0===(r="error",e.listeners(r).length)&&e.emit("error",t)}function d(){e.removeListener("finish",p),_()}function p(){nt("onfinish"),e.removeListener("close",d),_()}function _(){nt("unpipe"),r.unpipe(e)}return r.on("data",c),function(e,t,r){if("function"==typeof e.prependListener)return e.prependListener(t,r);e._events&&e._events[t]?Array.isArray(e._events[t])?e._events[t].unshift(r):e._events[t]=[r,e._events[t]]:e.on(t,r)}(e,"error",u),e.once("close",d),e.once("finish",p),e.emit("pipe",r),n.flowing||(nt("pipe resume"),r.resume()),e},ot.prototype.unpipe=function(e){var t=this._readableState;if(0===t.pipesCount)return this;if(1===t.pipesCount)return e&&e!==t.pipes?this:(e||(e=t.pipes),t.pipes=null,t.pipesCount=0,t.flowing=!1,e&&e.emit("unpipe",this),this);if(!e){var r=t.pipes,n=t.pipesCount;t.pipes=null,t.pipesCount=0,t.flowing=!1;for(var i=0;i<n;i++)r[i].emit("unpipe",this);return this}var o=wt(t.pipes,e);return-1===o?this:(t.pipes.splice(o,1),t.pipesCount-=1,1===t.pipesCount&&(t.pipes=t.pipes[0]),e.emit("unpipe",this),this)},ot.prototype.on=function(e,t){var r=ve.prototype.on.call(this,e,t);if("data"===e)!1!==this._readableState.flowing&&this.resume();else if("readable"===e){var n=this._readableState;n.endEmitted||n.readableListening||(n.readableListening=n.needReadable=!0,n.emittedReadable=!1,n.reading?n.length&&ft(this):de(ut,this))}return r},ot.prototype.addListener=ot.prototype.on,ot.prototype.resume=function(){var e=this._readableState;return e.flowing||(nt("resume"),e.flowing=!0,function(e,t){t.resumeScheduled||(t.resumeScheduled=!0,de(dt,e,t))}(this,e)),this},ot.prototype.pause=function(){return nt("call pause flowing=%j",this._readableState.flowing),!1!==this._readableState.flowing&&(nt("pause"),this._readableState.flowing=!1,this.emit("pause")),this},ot.prototype.wrap=function(e){var t=this._readableState,r=!1,n=this;for(var i in e.on("end",function(){if(nt("wrapped end"),t.decoder&&!t.ended){var e=t.decoder.end();e&&e.length&&n.push(e)}n.push(null)}),e.on("data",function(i){(nt("wrapped data"),t.decoder&&(i=t.decoder.write(i)),t.objectMode&&null==i)||(t.objectMode||i&&i.length)&&(n.push(i)||(r=!0,e.pause()))}),e)void 0===this[i]&&"function"==typeof e[i]&&(this[i]=function(t){return function(){return e[t].apply(e,arguments)}}(i));return function(e,t){for(var r=0,n=e.length;r<n;r++)t(e[r],r)}(["error","close","destroy","pause","resume"],function(t){e.on(t,n.emit.bind(n,t))}),n._read=function(t){nt("wrapped _read",t),r&&(r=!1,e.resume())},n},ot._fromList=_t,kt.WritableState=mt,Be(kt,ve),mt.prototype.getBuffer=function(){for(var e=this.bufferedRequest,t=[];e;)t.push(e),e=e.next;return t},kt.prototype.pipe=function(){this.emit("error",new Error("Cannot pipe, not readable"))},kt.prototype.write=function(e,t,r){var n=this._writableState,i=!1;return"function"==typeof t&&(r=t,t=null),p.isBuffer(e)?t="buffer":t||(t=n.defaultEncoding),"function"!=typeof r&&(r=bt),n.ended?function(e,t){var r=new Error("write after end");e.emit("error",r),de(t,r)}(this,r):function(e,t,r,n){var i=!0,o=!1;return null===r?o=new TypeError("May not write null values to stream"):p.isBuffer(r)||"string"==typeof r||void 0===r||t.objectMode||(o=new TypeError("Invalid non-string/buffer chunk")),o&&(e.emit("error",o),de(n,o),i=!1),i}(this,n,e,r)&&(n.pendingcb++,i=function(e,t,r,n,i){r=function(e,t,r){return e.objectMode||!1===e.decodeStrings||"string"!=typeof t||(t=p.from(t,r)),t}(t,r,n),p.isBuffer(r)&&(n="buffer");var o=t.objectMode?1:r.length;t.length+=o;var a=t.length<t.highWaterMark;a||(t.needDrain=!0);if(t.writing||t.corked){var s=t.lastBufferedRequest;t.lastBufferedRequest=new yt(r,n,i),s?s.next=t.lastBufferedRequest:t.bufferedRequest=t.lastBufferedRequest,t.bufferedRequestCount+=1}else Et(e,t,!1,o,r,n,i);return a}(this,n,e,t,r)),i},kt.prototype.cork=function(){this._writableState.corked++},kt.prototype.uncork=function(){var e=this._writableState;e.corked&&(e.corked--,e.writing||e.corked||e.finished||e.bufferProcessing||!e.bufferedRequest||xt(this,e))},kt.prototype.setDefaultEncoding=function(e){if("string"==typeof e&&(e=e.toLowerCase()),!(["hex","utf8","utf-8","ascii","binary","base64","ucs2","ucs-2","utf16le","utf-16le","raw"].indexOf((e+"").toLowerCase())>-1))throw new TypeError("Unknown encoding: "+e);return this._writableState.defaultEncoding=e,this},kt.prototype._write=function(e,t,r){r(new Error("not implemented"))},kt.prototype._writev=null,kt.prototype.end=function(e,t,r){var n=this._writableState;"function"==typeof e?(r=e,e=null,t=null):"function"==typeof t&&(r=t,t=null),null!=e&&this.write(e,t),n.corked&&(n.corked=1,this.uncork()),n.ending||n.finished||function(e,t,r){t.ending=!0,Bt(e,t),r&&(t.finished?de(r):e.once("finish",r));t.ended=!0,e.writable=!1}(this,n,r)},Be(Ct,ot);for(var Lt=Object.keys(kt.prototype),Tt=0;Tt<Lt.length;Tt++){var Mt=Lt[Tt];Ct.prototype[Mt]||(Ct.prototype[Mt]=kt.prototype[Mt])}function Ct(e){if(!(this instanceof Ct))return new Ct(e);ot.call(this,e),kt.call(this,e),e&&!1===e.readable&&(this.readable=!1),e&&!1===e.writable&&(this.writable=!1),this.allowHalfOpen=!0,e&&!1===e.allowHalfOpen&&(this.allowHalfOpen=!1),this.once("end",Dt)}function Dt(){this.allowHalfOpen||this._writableState.ended||de(It,this)}function It(e){e.end()}function Pt(e){this.afterTransform=function(t,r){return function(e,t,r){var n=e._transformState;n.transforming=!1;var i=n.writecb;if(!i)return e.emit("error",new Error("no writecb in Transform class"));n.writechunk=null,n.writecb=null,null!=r&&e.push(r);i(t);var o=e._readableState;o.reading=!1,(o.needReadable||o.length<o.highWaterMark)&&e._read(o.highWaterMark)}(e,t,r)},this.needTransform=!1,this.transforming=!1,this.writecb=null,this.writechunk=null,this.writeencoding=null}function Ot(e){if(!(this instanceof Ot))return new Ot(e);Ct.call(this,e),this._transformState=new Pt(this);var t=this;this._readableState.needReadable=!0,this._readableState.sync=!1,e&&("function"==typeof e.transform&&(this._transform=e.transform),"function"==typeof e.flush&&(this._flush=e.flush)),this.once("prefinish",function(){"function"==typeof this._flush?this._flush(function(e){Ut(t,e)}):Ut(t)})}function Ut(e,t){if(t)return e.emit("error",t);var r=e._writableState,n=e._transformState;if(r.length)throw new Error("Calling transform done when ws.length != 0");if(n.transforming)throw new Error("Calling transform done when still transforming");return e.push(null)}function Ht(e){if(!(this instanceof Ht))return new Ht(e);Ot.call(this,e)}function Ft(){ve.call(this)}Be(Ot,Ct),Ot.prototype.push=function(e,t){return this._transformState.needTransform=!1,Ct.prototype.push.call(this,e,t)},Ot.prototype._transform=function(e,t,r){throw new Error("Not implemented")},Ot.prototype._write=function(e,t,r){var n=this._transformState;if(n.writecb=r,n.writechunk=e,n.writeencoding=t,!n.transforming){var i=this._readableState;(n.needTransform||i.needReadable||i.length<i.highWaterMark)&&this._read(i.highWaterMark)}},Ot.prototype._read=function(e){var t=this._transformState;null!==t.writechunk&&t.writecb&&!t.transforming?(t.transforming=!0,this._transform(t.writechunk,t.writeencoding,t.afterTransform)):t.needTransform=!0},Be(Ht,Ot),Ht.prototype._transform=function(e,t,r){r(null,e)},Be(Ft,ve),Ft.Readable=ot,Ft.Writable=kt,Ft.Duplex=Ct,Ft.Transform=Ot,Ft.PassThrough=Ht,Ft.Stream=Ft,Ft.prototype.pipe=function(e,t){var r=this;function n(t){e.writable&&!1===e.write(t)&&r.pause&&r.pause()}function i(){r.readable&&r.resume&&r.resume()}r.on("data",n),e.on("drain",i),e._isStdio||t&&!1===t.end||(r.on("end",a),r.on("close",s));var o=!1;function a(){o||(o=!0,e.end())}function s(){o||(o=!0,"function"==typeof e.destroy&&e.destroy())}function h(e){if(f(),0===ve.listenerCount(this,"error"))throw e}function f(){r.removeListener("data",n),e.removeListener("drain",i),r.removeListener("end",a),r.removeListener("close",s),r.removeListener("error",h),e.removeListener("error",h),r.removeListener("end",f),r.removeListener("close",f),e.removeListener("close",f)}return r.on("error",h),e.on("error",h),r.on("end",f),r.on("close",f),e.on("close",f),e.emit("pipe",r),e};var Nt={2:"need dictionary",1:"stream end",0:"","-1":"file error","-2":"stream error","-3":"data error","-4":"insufficient memory","-5":"buffer error","-6":"incompatible version"};function Zt(){this.input=null,this.next_in=0,this.avail_in=0,this.total_in=0,this.output=null,this.next_out=0,this.avail_out=0,this.total_out=0,this.msg="",this.state=null,this.data_type=2,this.adler=0}function jt(e,t,r,n,i){if(t.subarray&&e.subarray)e.set(t.subarray(r,r+n),i);else for(var o=0;o<n;o++)e[i+o]=t[r+o]}var Wt=Uint8Array,Yt=Uint16Array,Kt=Int32Array,Xt=4,qt=0,Vt=1,Gt=2;function $t(e){for(var t=e.length;--t>=0;)e[t]=0}var Jt=0,Qt=1,er=2,tr=29,rr=256,nr=rr+1+tr,ir=30,or=19,ar=2*nr+1,sr=15,hr=16,fr=7,lr=256,cr=16,ur=17,dr=18,pr=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0],_r=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],gr=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,2,3,7],vr=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],wr=new Array(2*(nr+2));$t(wr);var br=new Array(2*ir);$t(br);var yr=new Array(512);$t(yr);var mr=new Array(256);$t(mr);var kr=new Array(tr);$t(kr);var Er,Sr,xr,Rr=new Array(ir);function Ar(e,t,r,n,i){this.static_tree=e,this.extra_bits=t,this.extra_base=r,this.elems=n,this.max_length=i,this.has_stree=e&&e.length}function Br(e,t){this.dyn_tree=e,this.max_code=0,this.stat_desc=t}function zr(e){return e<256?yr[e]:yr[256+(e>>>7)]}function Lr(e,t){e.pending_buf[e.pending++]=255&t,e.pending_buf[e.pending++]=t>>>8&255}function Tr(e,t,r){e.bi_valid>hr-r?(e.bi_buf|=t<<e.bi_valid&65535,Lr(e,e.bi_buf),e.bi_buf=t>>hr-e.bi_valid,e.bi_valid+=r-hr):(e.bi_buf|=t<<e.bi_valid&65535,e.bi_valid+=r)}function Mr(e,t,r){Tr(e,r[2*t],r[2*t+1])}function Cr(e,t){var r=0;do{r|=1&e,e>>>=1,r<<=1}while(--t>0);return r>>>1}function Dr(e,t,r){var n,i,o=new Array(sr+1),a=0;for(n=1;n<=sr;n++)o[n]=a=a+r[n-1]<<1;for(i=0;i<=t;i++){var s=e[2*i+1];0!==s&&(e[2*i]=Cr(o[s]++,s))}}function Ir(e){var t;for(t=0;t<nr;t++)e.dyn_ltree[2*t]=0;for(t=0;t<ir;t++)e.dyn_dtree[2*t]=0;for(t=0;t<or;t++)e.bl_tree[2*t]=0;e.dyn_ltree[2*lr]=1,e.opt_len=e.static_len=0,e.last_lit=e.matches=0}function Pr(e){e.bi_valid>8?Lr(e,e.bi_buf):e.bi_valid>0&&(e.pending_buf[e.pending++]=e.bi_buf),e.bi_buf=0,e.bi_valid=0}function Or(e,t,r,n){var i=2*t,o=2*r;return e[i]<e[o]||e[i]===e[o]&&n[t]<=n[r]}function Ur(e,t,r){for(var n=e.heap[r],i=r<<1;i<=e.heap_len&&(i<e.heap_len&&Or(t,e.heap[i+1],e.heap[i],e.depth)&&i++,!Or(t,n,e.heap[i],e.depth));)e.heap[r]=e.heap[i],r=i,i<<=1;e.heap[r]=n}function Hr(e,t,r){var n,i,o,a,s=0;if(0!==e.last_lit)do{n=e.pending_buf[e.d_buf+2*s]<<8|e.pending_buf[e.d_buf+2*s+1],i=e.pending_buf[e.l_buf+s],s++,0===n?Mr(e,i,t):(Mr(e,(o=mr[i])+rr+1,t),0!==(a=pr[o])&&Tr(e,i-=kr[o],a),Mr(e,o=zr(--n),r),0!==(a=_r[o])&&Tr(e,n-=Rr[o],a))}while(s<e.last_lit);Mr(e,lr,t)}function Fr(e,t){var r,n,i,o=t.dyn_tree,a=t.stat_desc.static_tree,s=t.stat_desc.has_stree,h=t.stat_desc.elems,f=-1;for(e.heap_len=0,e.heap_max=ar,r=0;r<h;r++)0!==o[2*r]?(e.heap[++e.heap_len]=f=r,e.depth[r]=0):o[2*r+1]=0;for(;e.heap_len<2;)o[2*(i=e.heap[++e.heap_len]=f<2?++f:0)]=1,e.depth[i]=0,e.opt_len--,s&&(e.static_len-=a[2*i+1]);for(t.max_code=f,r=e.heap_len>>1;r>=1;r--)Ur(e,o,r);i=h;do{r=e.heap[1],e.heap[1]=e.heap[e.heap_len--],Ur(e,o,1),n=e.heap[1],e.heap[--e.heap_max]=r,e.heap[--e.heap_max]=n,o[2*i]=o[2*r]+o[2*n],e.depth[i]=(e.depth[r]>=e.depth[n]?e.depth[r]:e.depth[n])+1,o[2*r+1]=o[2*n+1]=i,e.heap[1]=i++,Ur(e,o,1)}while(e.heap_len>=2);e.heap[--e.heap_max]=e.heap[1],function(e,t){var r,n,i,o,a,s,h=t.dyn_tree,f=t.max_code,l=t.stat_desc.static_tree,c=t.stat_desc.has_stree,u=t.stat_desc.extra_bits,d=t.stat_desc.extra_base,p=t.stat_desc.max_length,_=0;for(o=0;o<=sr;o++)e.bl_count[o]=0;for(h[2*e.heap[e.heap_max]+1]=0,r=e.heap_max+1;r<ar;r++)(o=h[2*h[2*(n=e.heap[r])+1]+1]+1)>p&&(o=p,_++),h[2*n+1]=o,n>f||(e.bl_count[o]++,a=0,n>=d&&(a=u[n-d]),s=h[2*n],e.opt_len+=s*(o+a),c&&(e.static_len+=s*(l[2*n+1]+a)));if(0!==_){do{for(o=p-1;0===e.bl_count[o];)o--;e.bl_count[o]--,e.bl_count[o+1]+=2,e.bl_count[p]--,_-=2}while(_>0);for(o=p;0!==o;o--)for(n=e.bl_count[o];0!==n;)(i=e.heap[--r])>f||(h[2*i+1]!==o&&(e.opt_len+=(o-h[2*i+1])*h[2*i],h[2*i+1]=o),n--)}}(e,t),Dr(o,f,e.bl_count)}function Nr(e,t,r){var n,i,o=-1,a=t[1],s=0,h=7,f=4;for(0===a&&(h=138,f=3),t[2*(r+1)+1]=65535,n=0;n<=r;n++)i=a,a=t[2*(n+1)+1],++s<h&&i===a||(s<f?e.bl_tree[2*i]+=s:0!==i?(i!==o&&e.bl_tree[2*i]++,e.bl_tree[2*cr]++):s<=10?e.bl_tree[2*ur]++:e.bl_tree[2*dr]++,s=0,o=i,0===a?(h=138,f=3):i===a?(h=6,f=3):(h=7,f=4))}function Zr(e,t,r){var n,i,o=-1,a=t[1],s=0,h=7,f=4;for(0===a&&(h=138,f=3),n=0;n<=r;n++)if(i=a,a=t[2*(n+1)+1],!(++s<h&&i===a)){if(s<f)do{Mr(e,i,e.bl_tree)}while(0!=--s);else 0!==i?(i!==o&&(Mr(e,i,e.bl_tree),s--),Mr(e,cr,e.bl_tree),Tr(e,s-3,2)):s<=10?(Mr(e,ur,e.bl_tree),Tr(e,s-3,3)):(Mr(e,dr,e.bl_tree),Tr(e,s-11,7));s=0,o=i,0===a?(h=138,f=3):i===a?(h=6,f=3):(h=7,f=4)}}$t(Rr);var jr=!1;function Wr(e){jr||(!function(){var e,t,r,n,i,o=new Array(sr+1);for(r=0,n=0;n<tr-1;n++)for(kr[n]=r,e=0;e<1<<pr[n];e++)mr[r++]=n;for(mr[r-1]=n,i=0,n=0;n<16;n++)for(Rr[n]=i,e=0;e<1<<_r[n];e++)yr[i++]=n;for(i>>=7;n<ir;n++)for(Rr[n]=i<<7,e=0;e<1<<_r[n]-7;e++)yr[256+i++]=n;for(t=0;t<=sr;t++)o[t]=0;for(e=0;e<=143;)wr[2*e+1]=8,e++,o[8]++;for(;e<=255;)wr[2*e+1]=9,e++,o[9]++;for(;e<=279;)wr[2*e+1]=7,e++,o[7]++;for(;e<=287;)wr[2*e+1]=8,e++,o[8]++;for(Dr(wr,nr+1,o),e=0;e<ir;e++)br[2*e+1]=5,br[2*e]=Cr(e,5);Er=new Ar(wr,pr,rr+1,nr,sr),Sr=new Ar(br,_r,0,ir,sr),xr=new Ar(new Array(0),gr,0,or,fr)}(),jr=!0),e.l_desc=new Br(e.dyn_ltree,Er),e.d_desc=new Br(e.dyn_dtree,Sr),e.bl_desc=new Br(e.bl_tree,xr),e.bi_buf=0,e.bi_valid=0,Ir(e)}function Yr(e,t,r,n){Tr(e,(Jt<<1)+(n?1:0),3),function(e,t,r,n){Pr(e),n&&(Lr(e,r),Lr(e,~r)),jt(e.pending_buf,e.window,t,r,e.pending),e.pending+=r}(e,t,r,!0)}function Kr(e){Tr(e,Qt<<1,3),Mr(e,lr,wr),function(e){16===e.bi_valid?(Lr(e,e.bi_buf),e.bi_buf=0,e.bi_valid=0):e.bi_valid>=8&&(e.pending_buf[e.pending++]=255&e.bi_buf,e.bi_buf>>=8,e.bi_valid-=8)}(e)}function Xr(e,t,r,n){var i,o,a=0;e.level>0?(e.strm.data_type===Gt&&(e.strm.data_type=function(e){var t,r=4093624447;for(t=0;t<=31;t++,r>>>=1)if(1&r&&0!==e.dyn_ltree[2*t])return qt;if(0!==e.dyn_ltree[18]||0!==e.dyn_ltree[20]||0!==e.dyn_ltree[26])return Vt;for(t=32;t<rr;t++)if(0!==e.dyn_ltree[2*t])return Vt;return qt}(e)),Fr(e,e.l_desc),Fr(e,e.d_desc),a=function(e){var t;for(Nr(e,e.dyn_ltree,e.l_desc.max_code),Nr(e,e.dyn_dtree,e.d_desc.max_code),Fr(e,e.bl_desc),t=or-1;t>=3&&0===e.bl_tree[2*vr[t]+1];t--);return e.opt_len+=3*(t+1)+5+5+4,t}(e),i=e.opt_len+3+7>>>3,(o=e.static_len+3+7>>>3)<=i&&(i=o)):i=o=r+5,r+4<=i&&-1!==t?Yr(e,t,r,n):e.strategy===Xt||o===i?(Tr(e,(Qt<<1)+(n?1:0),3),Hr(e,wr,br)):(Tr(e,(er<<1)+(n?1:0),3),function(e,t,r,n){var i;for(Tr(e,t-257,5),Tr(e,r-1,5),Tr(e,n-4,4),i=0;i<n;i++)Tr(e,e.bl_tree[2*vr[i]+1],3);Zr(e,e.dyn_ltree,t-1),Zr(e,e.dyn_dtree,r-1)}(e,e.l_desc.max_code+1,e.d_desc.max_code+1,a+1),Hr(e,e.dyn_ltree,e.dyn_dtree)),Ir(e),n&&Pr(e)}function qr(e,t,r){return e.pending_buf[e.d_buf+2*e.last_lit]=t>>>8&255,e.pending_buf[e.d_buf+2*e.last_lit+1]=255&t,e.pending_buf[e.l_buf+e.last_lit]=255&r,e.last_lit++,0===t?e.dyn_ltree[2*r]++:(e.matches++,t--,e.dyn_ltree[2*(mr[r]+rr+1)]++,e.dyn_dtree[2*zr(t)]++),e.last_lit===e.lit_bufsize-1}function Vr(e,t,r,n){for(var i=65535&e|0,o=e>>>16&65535|0,a=0;0!==r;){r-=a=r>2e3?2e3:r;do{o=o+(i=i+t[n++]|0)|0}while(--a);i%=65521,o%=65521}return i|o<<16|0}var Gr=function(){for(var e,t=[],r=0;r<256;r++){e=r;for(var n=0;n<8;n++)e=1&e?3988292384^e>>>1:e>>>1;t[r]=e}return t}();function $r(e,t,r,n){var i=Gr,o=n+r;e^=-1;for(var a=n;a<o;a++)e=e>>>8^i[255&(e^t[a])];return-1^e}var Jr,Qr=0,en=1,tn=3,rn=4,nn=5,on=0,an=1,sn=-2,hn=-3,fn=-5,ln=-1,cn=1,un=2,dn=3,pn=4,_n=2,gn=8,vn=9,wn=286,bn=30,yn=19,mn=2*wn+1,kn=15,En=3,Sn=258,xn=Sn+En+1,Rn=32,An=42,Bn=69,zn=73,Ln=91,Tn=103,Mn=113,Cn=666,Dn=1,In=2,Pn=3,On=4,Un=3;function Hn(e,t){return e.msg=Nt[t],t}function Fn(e){return(e<<1)-(e>4?9:0)}function Nn(e){for(var t=e.length;--t>=0;)e[t]=0}function Zn(e){var t=e.state,r=t.pending;r>e.avail_out&&(r=e.avail_out),0!==r&&(jt(e.output,t.pending_buf,t.pending_out,r,e.next_out),e.next_out+=r,t.pending_out+=r,e.total_out+=r,e.avail_out-=r,t.pending-=r,0===t.pending&&(t.pending_out=0))}function jn(e,t){Xr(e,e.block_start>=0?e.block_start:-1,e.strstart-e.block_start,t),e.block_start=e.strstart,Zn(e.strm)}function Wn(e,t){e.pending_buf[e.pending++]=t}function Yn(e,t){e.pending_buf[e.pending++]=t>>>8&255,e.pending_buf[e.pending++]=255&t}function Kn(e,t){var r,n,i=e.max_chain_length,o=e.strstart,a=e.prev_length,s=e.nice_match,h=e.strstart>e.w_size-xn?e.strstart-(e.w_size-xn):0,f=e.window,l=e.w_mask,c=e.prev,u=e.strstart+Sn,d=f[o+a-1],p=f[o+a];e.prev_length>=e.good_match&&(i>>=2),s>e.lookahead&&(s=e.lookahead);do{if(f[(r=t)+a]===p&&f[r+a-1]===d&&f[r]===f[o]&&f[++r]===f[o+1]){o+=2,r++;do{}while(f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&f[++o]===f[++r]&&o<u);if(n=Sn-(u-o),o=u-Sn,n>a){if(e.match_start=t,a=n,n>=s)break;d=f[o+a-1],p=f[o+a]}}}while((t=c[t&l])>h&&0!=--i);return a<=e.lookahead?a:e.lookahead}function Xn(e){var t,r,n,i,o,a,s,h,f,l,c=e.w_size;do{if(i=e.window_size-e.lookahead-e.strstart,e.strstart>=c+(c-xn)){jt(e.window,e.window,c,c,0),e.match_start-=c,e.strstart-=c,e.block_start-=c,t=r=e.hash_size;do{n=e.head[--t],e.head[t]=n>=c?n-c:0}while(--r);t=r=c;do{n=e.prev[--t],e.prev[t]=n>=c?n-c:0}while(--r);i+=c}if(0===e.strm.avail_in)break;if(a=e.strm,s=e.window,h=e.strstart+e.lookahead,f=i,l=void 0,(l=a.avail_in)>f&&(l=f),r=0===l?0:(a.avail_in-=l,jt(s,a.input,a.next_in,l,h),1===a.state.wrap?a.adler=Vr(a.adler,s,l,h):2===a.state.wrap&&(a.adler=$r(a.adler,s,l,h)),a.next_in+=l,a.total_in+=l,l),e.lookahead+=r,e.lookahead+e.insert>=En)for(o=e.strstart-e.insert,e.ins_h=e.window[o],e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+1])&e.hash_mask;e.insert&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[o+En-1])&e.hash_mask,e.prev[o&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=o,o++,e.insert--,!(e.lookahead+e.insert<En)););}while(e.lookahead<xn&&0!==e.strm.avail_in)}function qn(e,t){for(var r,n;;){if(e.lookahead<xn){if(Xn(e),e.lookahead<xn&&t===Qr)return Dn;if(0===e.lookahead)break}if(r=0,e.lookahead>=En&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+En-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),0!==r&&e.strstart-r<=e.w_size-xn&&(e.match_length=Kn(e,r)),e.match_length>=En)if(n=qr(e,e.strstart-e.match_start,e.match_length-En),e.lookahead-=e.match_length,e.match_length<=e.max_lazy_match&&e.lookahead>=En){e.match_length--;do{e.strstart++,e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+En-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart}while(0!=--e.match_length);e.strstart++}else e.strstart+=e.match_length,e.match_length=0,e.ins_h=e.window[e.strstart],e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+1])&e.hash_mask;else n=qr(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++;if(n&&(jn(e,!1),0===e.strm.avail_out))return Dn}return e.insert=e.strstart<En-1?e.strstart:En-1,t===rn?(jn(e,!0),0===e.strm.avail_out?Pn:On):e.last_lit&&(jn(e,!1),0===e.strm.avail_out)?Dn:In}function Vn(e,t){for(var r,n,i;;){if(e.lookahead<xn){if(Xn(e),e.lookahead<xn&&t===Qr)return Dn;if(0===e.lookahead)break}if(r=0,e.lookahead>=En&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+En-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart),e.prev_length=e.match_length,e.prev_match=e.match_start,e.match_length=En-1,0!==r&&e.prev_length<e.max_lazy_match&&e.strstart-r<=e.w_size-xn&&(e.match_length=Kn(e,r),e.match_length<=5&&(e.strategy===cn||e.match_length===En&&e.strstart-e.match_start>4096)&&(e.match_length=En-1)),e.prev_length>=En&&e.match_length<=e.prev_length){i=e.strstart+e.lookahead-En,n=qr(e,e.strstart-1-e.prev_match,e.prev_length-En),e.lookahead-=e.prev_length-1,e.prev_length-=2;do{++e.strstart<=i&&(e.ins_h=(e.ins_h<<e.hash_shift^e.window[e.strstart+En-1])&e.hash_mask,r=e.prev[e.strstart&e.w_mask]=e.head[e.ins_h],e.head[e.ins_h]=e.strstart)}while(0!=--e.prev_length);if(e.match_available=0,e.match_length=En-1,e.strstart++,n&&(jn(e,!1),0===e.strm.avail_out))return Dn}else if(e.match_available){if((n=qr(e,0,e.window[e.strstart-1]))&&jn(e,!1),e.strstart++,e.lookahead--,0===e.strm.avail_out)return Dn}else e.match_available=1,e.strstart++,e.lookahead--}return e.match_available&&(n=qr(e,0,e.window[e.strstart-1]),e.match_available=0),e.insert=e.strstart<En-1?e.strstart:En-1,t===rn?(jn(e,!0),0===e.strm.avail_out?Pn:On):e.last_lit&&(jn(e,!1),0===e.strm.avail_out)?Dn:In}function Gn(e,t,r,n,i){this.good_length=e,this.max_lazy=t,this.nice_length=r,this.max_chain=n,this.func=i}function $n(){this.strm=null,this.status=0,this.pending_buf=null,this.pending_buf_size=0,this.pending_out=0,this.pending=0,this.wrap=0,this.gzhead=null,this.gzindex=0,this.method=gn,this.last_flush=-1,this.w_size=0,this.w_bits=0,this.w_mask=0,this.window=null,this.window_size=0,this.prev=null,this.head=null,this.ins_h=0,this.hash_size=0,this.hash_bits=0,this.hash_mask=0,this.hash_shift=0,this.block_start=0,this.match_length=0,this.prev_match=0,this.match_available=0,this.strstart=0,this.match_start=0,this.lookahead=0,this.prev_length=0,this.max_chain_length=0,this.max_lazy_match=0,this.level=0,this.strategy=0,this.good_match=0,this.nice_match=0,this.dyn_ltree=new Yt(2*mn),this.dyn_dtree=new Yt(2*(2*bn+1)),this.bl_tree=new Yt(2*(2*yn+1)),Nn(this.dyn_ltree),Nn(this.dyn_dtree),Nn(this.bl_tree),this.l_desc=null,this.d_desc=null,this.bl_desc=null,this.bl_count=new Yt(kn+1),this.heap=new Yt(2*wn+1),Nn(this.heap),this.heap_len=0,this.heap_max=0,this.depth=new Yt(2*wn+1),Nn(this.depth),this.l_buf=0,this.lit_bufsize=0,this.last_lit=0,this.d_buf=0,this.opt_len=0,this.static_len=0,this.matches=0,this.insert=0,this.bi_buf=0,this.bi_valid=0}function Jn(e){var t,r=function(e){var t;return e&&e.state?(e.total_in=e.total_out=0,e.data_type=_n,(t=e.state).pending=0,t.pending_out=0,t.wrap<0&&(t.wrap=-t.wrap),t.status=t.wrap?An:Mn,e.adler=2===t.wrap?0:1,t.last_flush=Qr,Wr(t),on):Hn(e,sn)}(e);return r===on&&((t=e.state).window_size=2*t.w_size,Nn(t.head),t.max_lazy_match=Jr[t.level].max_lazy,t.good_match=Jr[t.level].good_length,t.nice_match=Jr[t.level].nice_length,t.max_chain_length=Jr[t.level].max_chain,t.strstart=0,t.block_start=0,t.lookahead=0,t.insert=0,t.match_length=t.prev_length=En-1,t.match_available=0,t.ins_h=0),r}function Qn(e,t){var r,n,i,o;if(!e||!e.state||t>nn||t<0)return e?Hn(e,sn):sn;if(n=e.state,!e.output||!e.input&&0!==e.avail_in||n.status===Cn&&t!==rn)return Hn(e,0===e.avail_out?fn:sn);if(n.strm=e,r=n.last_flush,n.last_flush=t,n.status===An)if(2===n.wrap)e.adler=0,Wn(n,31),Wn(n,139),Wn(n,8),n.gzhead?(Wn(n,(n.gzhead.text?1:0)+(n.gzhead.hcrc?2:0)+(n.gzhead.extra?4:0)+(n.gzhead.name?8:0)+(n.gzhead.comment?16:0)),Wn(n,255&n.gzhead.time),Wn(n,n.gzhead.time>>8&255),Wn(n,n.gzhead.time>>16&255),Wn(n,n.gzhead.time>>24&255),Wn(n,9===n.level?2:n.strategy>=un||n.level<2?4:0),Wn(n,255&n.gzhead.os),n.gzhead.extra&&n.gzhead.extra.length&&(Wn(n,255&n.gzhead.extra.length),Wn(n,n.gzhead.extra.length>>8&255)),n.gzhead.hcrc&&(e.adler=$r(e.adler,n.pending_buf,n.pending,0)),n.gzindex=0,n.status=Bn):(Wn(n,0),Wn(n,0),Wn(n,0),Wn(n,0),Wn(n,0),Wn(n,9===n.level?2:n.strategy>=un||n.level<2?4:0),Wn(n,Un),n.status=Mn);else{var a=gn+(n.w_bits-8<<4)<<8;a|=(n.strategy>=un||n.level<2?0:n.level<6?1:6===n.level?2:3)<<6,0!==n.strstart&&(a|=Rn),a+=31-a%31,n.status=Mn,Yn(n,a),0!==n.strstart&&(Yn(n,e.adler>>>16),Yn(n,65535&e.adler)),e.adler=1}if(n.status===Bn)if(n.gzhead.extra){for(i=n.pending;n.gzindex<(65535&n.gzhead.extra.length)&&(n.pending!==n.pending_buf_size||(n.gzhead.hcrc&&n.pending>i&&(e.adler=$r(e.adler,n.pending_buf,n.pending-i,i)),Zn(e),i=n.pending,n.pending!==n.pending_buf_size));)Wn(n,255&n.gzhead.extra[n.gzindex]),n.gzindex++;n.gzhead.hcrc&&n.pending>i&&(e.adler=$r(e.adler,n.pending_buf,n.pending-i,i)),n.gzindex===n.gzhead.extra.length&&(n.gzindex=0,n.status=zn)}else n.status=zn;if(n.status===zn)if(n.gzhead.name){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=$r(e.adler,n.pending_buf,n.pending-i,i)),Zn(e),i=n.pending,n.pending===n.pending_buf_size)){o=1;break}o=n.gzindex<n.gzhead.name.length?255&n.gzhead.name.charCodeAt(n.gzindex++):0,Wn(n,o)}while(0!==o);n.gzhead.hcrc&&n.pending>i&&(e.adler=$r(e.adler,n.pending_buf,n.pending-i,i)),0===o&&(n.gzindex=0,n.status=Ln)}else n.status=Ln;if(n.status===Ln)if(n.gzhead.comment){i=n.pending;do{if(n.pending===n.pending_buf_size&&(n.gzhead.hcrc&&n.pending>i&&(e.adler=$r(e.adler,n.pending_buf,n.pending-i,i)),Zn(e),i=n.pending,n.pending===n.pending_buf_size)){o=1;break}o=n.gzindex<n.gzhead.comment.length?255&n.gzhead.comment.charCodeAt(n.gzindex++):0,Wn(n,o)}while(0!==o);n.gzhead.hcrc&&n.pending>i&&(e.adler=$r(e.adler,n.pending_buf,n.pending-i,i)),0===o&&(n.status=Tn)}else n.status=Tn;if(n.status===Tn&&(n.gzhead.hcrc?(n.pending+2>n.pending_buf_size&&Zn(e),n.pending+2<=n.pending_buf_size&&(Wn(n,255&e.adler),Wn(n,e.adler>>8&255),e.adler=0,n.status=Mn)):n.status=Mn),0!==n.pending){if(Zn(e),0===e.avail_out)return n.last_flush=-1,on}else if(0===e.avail_in&&Fn(t)<=Fn(r)&&t!==rn)return Hn(e,fn);if(n.status===Cn&&0!==e.avail_in)return Hn(e,fn);if(0!==e.avail_in||0!==n.lookahead||t!==Qr&&n.status!==Cn){var s=n.strategy===un?function(e,t){for(var r;;){if(0===e.lookahead&&(Xn(e),0===e.lookahead)){if(t===Qr)return Dn;break}if(e.match_length=0,r=qr(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++,r&&(jn(e,!1),0===e.strm.avail_out))return Dn}return e.insert=0,t===rn?(jn(e,!0),0===e.strm.avail_out?Pn:On):e.last_lit&&(jn(e,!1),0===e.strm.avail_out)?Dn:In}(n,t):n.strategy===dn?function(e,t){for(var r,n,i,o,a=e.window;;){if(e.lookahead<=Sn){if(Xn(e),e.lookahead<=Sn&&t===Qr)return Dn;if(0===e.lookahead)break}if(e.match_length=0,e.lookahead>=En&&e.strstart>0&&(n=a[i=e.strstart-1])===a[++i]&&n===a[++i]&&n===a[++i]){o=e.strstart+Sn;do{}while(n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&n===a[++i]&&i<o);e.match_length=Sn-(o-i),e.match_length>e.lookahead&&(e.match_length=e.lookahead)}if(e.match_length>=En?(r=qr(e,1,e.match_length-En),e.lookahead-=e.match_length,e.strstart+=e.match_length,e.match_length=0):(r=qr(e,0,e.window[e.strstart]),e.lookahead--,e.strstart++),r&&(jn(e,!1),0===e.strm.avail_out))return Dn}return e.insert=0,t===rn?(jn(e,!0),0===e.strm.avail_out?Pn:On):e.last_lit&&(jn(e,!1),0===e.strm.avail_out)?Dn:In}(n,t):Jr[n.level].func(n,t);if(s!==Pn&&s!==On||(n.status=Cn),s===Dn||s===Pn)return 0===e.avail_out&&(n.last_flush=-1),on;if(s===In&&(t===en?Kr(n):t!==nn&&(Yr(n,0,0,!1),t===tn&&(Nn(n.head),0===n.lookahead&&(n.strstart=0,n.block_start=0,n.insert=0))),Zn(e),0===e.avail_out))return n.last_flush=-1,on}return t!==rn?on:n.wrap<=0?an:(2===n.wrap?(Wn(n,255&e.adler),Wn(n,e.adler>>8&255),Wn(n,e.adler>>16&255),Wn(n,e.adler>>24&255),Wn(n,255&e.total_in),Wn(n,e.total_in>>8&255),Wn(n,e.total_in>>16&255),Wn(n,e.total_in>>24&255)):(Yn(n,e.adler>>>16),Yn(n,65535&e.adler)),Zn(e),n.wrap>0&&(n.wrap=-n.wrap),0!==n.pending?on:an)}Jr=[new Gn(0,0,0,0,function(e,t){var r=65535;for(r>e.pending_buf_size-5&&(r=e.pending_buf_size-5);;){if(e.lookahead<=1){if(Xn(e),0===e.lookahead&&t===Qr)return Dn;if(0===e.lookahead)break}e.strstart+=e.lookahead,e.lookahead=0;var n=e.block_start+r;if((0===e.strstart||e.strstart>=n)&&(e.lookahead=e.strstart-n,e.strstart=n,jn(e,!1),0===e.strm.avail_out))return Dn;if(e.strstart-e.block_start>=e.w_size-xn&&(jn(e,!1),0===e.strm.avail_out))return Dn}return e.insert=0,t===rn?(jn(e,!0),0===e.strm.avail_out?Pn:On):(e.strstart>e.block_start&&(jn(e,!1),e.strm.avail_out),Dn)}),new Gn(4,4,8,4,qn),new Gn(4,5,16,8,qn),new Gn(4,6,32,32,qn),new Gn(4,4,16,16,Vn),new Gn(8,16,32,32,Vn),new Gn(8,16,128,128,Vn),new Gn(8,32,128,256,Vn),new Gn(32,128,258,1024,Vn),new Gn(32,258,258,4096,Vn)];var ei=30,ti=12;function ri(e,t){var r,n,i,o,a,s,h,f,l,c,u,d,p,_,g,v,w,b,y,m,k,E,S,x,R;r=e.state,n=e.next_in,x=e.input,i=n+(e.avail_in-5),o=e.next_out,R=e.output,a=o-(t-e.avail_out),s=o+(e.avail_out-257),h=r.dmax,f=r.wsize,l=r.whave,c=r.wnext,u=r.window,d=r.hold,p=r.bits,_=r.lencode,g=r.distcode,v=(1<<r.lenbits)-1,w=(1<<r.distbits)-1;e:do{p<15&&(d+=x[n++]<<p,p+=8,d+=x[n++]<<p,p+=8),b=_[d&v];t:for(;;){if(d>>>=y=b>>>24,p-=y,0===(y=b>>>16&255))R[o++]=65535&b;else{if(!(16&y)){if(0==(64&y)){b=_[(65535&b)+(d&(1<<y)-1)];continue t}if(32&y){r.mode=ti;break e}e.msg="invalid literal/length code",r.mode=ei;break e}m=65535&b,(y&=15)&&(p<y&&(d+=x[n++]<<p,p+=8),m+=d&(1<<y)-1,d>>>=y,p-=y),p<15&&(d+=x[n++]<<p,p+=8,d+=x[n++]<<p,p+=8),b=g[d&w];r:for(;;){if(d>>>=y=b>>>24,p-=y,!(16&(y=b>>>16&255))){if(0==(64&y)){b=g[(65535&b)+(d&(1<<y)-1)];continue r}e.msg="invalid distance code",r.mode=ei;break e}if(k=65535&b,p<(y&=15)&&(d+=x[n++]<<p,(p+=8)<y&&(d+=x[n++]<<p,p+=8)),(k+=d&(1<<y)-1)>h){e.msg="invalid distance too far back",r.mode=ei;break e}if(d>>>=y,p-=y,k>(y=o-a)){if((y=k-y)>l&&r.sane){e.msg="invalid distance too far back",r.mode=ei;break e}if(E=0,S=u,0===c){if(E+=f-y,y<m){m-=y;do{R[o++]=u[E++]}while(--y);E=o-k,S=R}}else if(c<y){if(E+=f+c-y,(y-=c)<m){m-=y;do{R[o++]=u[E++]}while(--y);if(E=0,c<m){m-=y=c;do{R[o++]=u[E++]}while(--y);E=o-k,S=R}}}else if(E+=c-y,y<m){m-=y;do{R[o++]=u[E++]}while(--y);E=o-k,S=R}for(;m>2;)R[o++]=S[E++],R[o++]=S[E++],R[o++]=S[E++],m-=3;m&&(R[o++]=S[E++],m>1&&(R[o++]=S[E++]))}else{E=o-k;do{R[o++]=R[E++],R[o++]=R[E++],R[o++]=R[E++],m-=3}while(m>2);m&&(R[o++]=R[E++],m>1&&(R[o++]=R[E++]))}break}}break}}while(n<i&&o<s);n-=m=p>>3,d&=(1<<(p-=m<<3))-1,e.next_in=n,e.next_out=o,e.avail_in=n<i?i-n+5:5-(n-i),e.avail_out=o<s?s-o+257:257-(o-s),r.hold=d,r.bits=p}var ni=15,ii=852,oi=592,ai=0,si=1,hi=2,fi=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],li=[16,16,16,16,16,16,16,16,17,17,17,17,18,18,18,18,19,19,19,19,20,20,20,20,21,21,21,21,16,72,78],ci=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577,0,0],ui=[16,16,16,16,17,17,18,18,19,19,20,20,21,21,22,22,23,23,24,24,25,25,26,26,27,27,28,28,29,29,64,64];function di(e,t,r,n,i,o,a,s){var h,f,l,c,u,d,p,_,g,v=s.bits,w=0,b=0,y=0,m=0,k=0,E=0,S=0,x=0,R=0,A=0,B=null,z=0,L=new Yt(ni+1),T=new Yt(ni+1),M=null,C=0;for(w=0;w<=ni;w++)L[w]=0;for(b=0;b<n;b++)L[t[r+b]]++;for(k=v,m=ni;m>=1&&0===L[m];m--);if(k>m&&(k=m),0===m)return i[o++]=20971520,i[o++]=20971520,s.bits=1,0;for(y=1;y<m&&0===L[y];y++);for(k<y&&(k=y),x=1,w=1;w<=ni;w++)if(x<<=1,(x-=L[w])<0)return-1;if(x>0&&(e===ai||1!==m))return-1;for(T[1]=0,w=1;w<ni;w++)T[w+1]=T[w]+L[w];for(b=0;b<n;b++)0!==t[r+b]&&(a[T[t[r+b]]++]=b);if(e===ai?(B=M=a,d=19):e===si?(B=fi,z-=257,M=li,C-=257,d=256):(B=ci,M=ui,d=-1),A=0,b=0,w=y,u=o,E=k,S=0,l=-1,c=(R=1<<k)-1,e===si&&R>ii||e===hi&&R>oi)return 1;for(;;){p=w-S,a[b]<d?(_=0,g=a[b]):a[b]>d?(_=M[C+a[b]],g=B[z+a[b]]):(_=96,g=0),h=1<<w-S,y=f=1<<E;do{i[u+(A>>S)+(f-=h)]=p<<24|_<<16|g|0}while(0!==f);for(h=1<<w-1;A&h;)h>>=1;if(0!==h?(A&=h-1,A+=h):A=0,b++,0==--L[w]){if(w===m)break;w=t[r+a[b]]}if(w>k&&(A&c)!==l){for(0===S&&(S=k),u+=y,x=1<<(E=w-S);E+S<m&&!((x-=L[E+S])<=0);)E++,x<<=1;if(R+=1<<E,e===si&&R>ii||e===hi&&R>oi)return 1;i[l=A&c]=k<<24|E<<16|u-o|0}}return 0!==A&&(i[u+A]=w-S<<24|64<<16|0),s.bits=k,0}var pi=0,_i=1,gi=2,vi=4,wi=5,bi=6,yi=0,mi=1,ki=2,Ei=-2,Si=-3,xi=-4,Ri=-5,Ai=8,Bi=1,zi=2,Li=3,Ti=4,Mi=5,Ci=6,Di=7,Ii=8,Pi=9,Oi=10,Ui=11,Hi=12,Fi=13,Ni=14,Zi=15,ji=16,Wi=17,Yi=18,Ki=19,Xi=20,qi=21,Vi=22,Gi=23,$i=24,Ji=25,Qi=26,eo=27,to=28,ro=29,no=30,io=31,oo=32,ao=852,so=592;function ho(e){return(e>>>24&255)+(e>>>8&65280)+((65280&e)<<8)+((255&e)<<24)}function fo(){this.mode=0,this.last=!1,this.wrap=0,this.havedict=!1,this.flags=0,this.dmax=0,this.check=0,this.total=0,this.head=null,this.wbits=0,this.wsize=0,this.whave=0,this.wnext=0,this.window=null,this.hold=0,this.bits=0,this.length=0,this.offset=0,this.extra=0,this.lencode=null,this.distcode=null,this.lenbits=0,this.distbits=0,this.ncode=0,this.nlen=0,this.ndist=0,this.have=0,this.next=null,this.lens=new Yt(320),this.work=new Yt(288),this.lendyn=null,this.distdyn=null,this.sane=0,this.back=0,this.was=0}function lo(e){var t;return e&&e.state?((t=e.state).wsize=0,t.whave=0,t.wnext=0,function(e){var t;return e&&e.state?(t=e.state,e.total_in=e.total_out=t.total=0,e.msg="",t.wrap&&(e.adler=1&t.wrap),t.mode=Bi,t.last=0,t.havedict=0,t.dmax=32768,t.head=null,t.hold=0,t.bits=0,t.lencode=t.lendyn=new Kt(ao),t.distcode=t.distdyn=new Kt(so),t.sane=1,t.back=-1,yi):Ei}(e)):Ei}function co(e,t){var r,n;return e?(n=new fo,e.state=n,n.window=null,(r=function(e,t){var r,n;return e&&e.state?(n=e.state,t<0?(r=0,t=-t):(r=1+(t>>4),t<48&&(t&=15)),t&&(t<8||t>15)?Ei:(null!==n.window&&n.wbits!==t&&(n.window=null),n.wrap=r,n.wbits=t,lo(e))):Ei}(e,t))!==yi&&(e.state=null),r):Ei}var uo,po,_o=!0;function go(e){if(_o){var t;for(uo=new Kt(512),po=new Kt(32),t=0;t<144;)e.lens[t++]=8;for(;t<256;)e.lens[t++]=9;for(;t<280;)e.lens[t++]=7;for(;t<288;)e.lens[t++]=8;for(di(_i,e.lens,0,288,uo,0,e.work,{bits:9}),t=0;t<32;)e.lens[t++]=5;di(gi,e.lens,0,32,po,0,e.work,{bits:5}),_o=!1}e.lencode=uo,e.lenbits=9,e.distcode=po,e.distbits=5}function vo(e,t){var r,n,i,o,a,s,h,f,l,c,u,d,p,_,g,v,w,b,y,m,k,E,S,x,R=0,A=new Wt(4),B=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15];if(!e||!e.state||!e.output||!e.input&&0!==e.avail_in)return Ei;(r=e.state).mode===Hi&&(r.mode=Fi),a=e.next_out,i=e.output,h=e.avail_out,o=e.next_in,n=e.input,s=e.avail_in,f=r.hold,l=r.bits,c=s,u=h,E=yi;e:for(;;)switch(r.mode){case Bi:if(0===r.wrap){r.mode=Fi;break}for(;l<16;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(2&r.wrap&&35615===f){r.check=0,A[0]=255&f,A[1]=f>>>8&255,r.check=$r(r.check,A,2,0),f=0,l=0,r.mode=zi;break}if(r.flags=0,r.head&&(r.head.done=!1),!(1&r.wrap)||(((255&f)<<8)+(f>>8))%31){e.msg="incorrect header check",r.mode=no;break}if((15&f)!==Ai){e.msg="unknown compression method",r.mode=no;break}if(l-=4,k=8+(15&(f>>>=4)),0===r.wbits)r.wbits=k;else if(k>r.wbits){e.msg="invalid window size",r.mode=no;break}r.dmax=1<<k,e.adler=r.check=1,r.mode=512&f?Oi:Hi,f=0,l=0;break;case zi:for(;l<16;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(r.flags=f,(255&r.flags)!==Ai){e.msg="unknown compression method",r.mode=no;break}if(57344&r.flags){e.msg="unknown header flags set",r.mode=no;break}r.head&&(r.head.text=f>>8&1),512&r.flags&&(A[0]=255&f,A[1]=f>>>8&255,r.check=$r(r.check,A,2,0)),f=0,l=0,r.mode=Li;case Li:for(;l<32;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}r.head&&(r.head.time=f),512&r.flags&&(A[0]=255&f,A[1]=f>>>8&255,A[2]=f>>>16&255,A[3]=f>>>24&255,r.check=$r(r.check,A,4,0)),f=0,l=0,r.mode=Ti;case Ti:for(;l<16;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}r.head&&(r.head.xflags=255&f,r.head.os=f>>8),512&r.flags&&(A[0]=255&f,A[1]=f>>>8&255,r.check=$r(r.check,A,2,0)),f=0,l=0,r.mode=Mi;case Mi:if(1024&r.flags){for(;l<16;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}r.length=f,r.head&&(r.head.extra_len=f),512&r.flags&&(A[0]=255&f,A[1]=f>>>8&255,r.check=$r(r.check,A,2,0)),f=0,l=0}else r.head&&(r.head.extra=null);r.mode=Ci;case Ci:if(1024&r.flags&&((d=r.length)>s&&(d=s),d&&(r.head&&(k=r.head.extra_len-r.length,r.head.extra||(r.head.extra=new Array(r.head.extra_len)),jt(r.head.extra,n,o,d,k)),512&r.flags&&(r.check=$r(r.check,n,d,o)),s-=d,o+=d,r.length-=d),r.length))break e;r.length=0,r.mode=Di;case Di:if(2048&r.flags){if(0===s)break e;d=0;do{k=n[o+d++],r.head&&k&&r.length<65536&&(r.head.name+=String.fromCharCode(k))}while(k&&d<s);if(512&r.flags&&(r.check=$r(r.check,n,d,o)),s-=d,o+=d,k)break e}else r.head&&(r.head.name=null);r.length=0,r.mode=Ii;case Ii:if(4096&r.flags){if(0===s)break e;d=0;do{k=n[o+d++],r.head&&k&&r.length<65536&&(r.head.comment+=String.fromCharCode(k))}while(k&&d<s);if(512&r.flags&&(r.check=$r(r.check,n,d,o)),s-=d,o+=d,k)break e}else r.head&&(r.head.comment=null);r.mode=Pi;case Pi:if(512&r.flags){for(;l<16;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(f!==(65535&r.check)){e.msg="header crc mismatch",r.mode=no;break}f=0,l=0}r.head&&(r.head.hcrc=r.flags>>9&1,r.head.done=!0),e.adler=r.check=0,r.mode=Hi;break;case Oi:for(;l<32;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}e.adler=r.check=ho(f),f=0,l=0,r.mode=Ui;case Ui:if(0===r.havedict)return e.next_out=a,e.avail_out=h,e.next_in=o,e.avail_in=s,r.hold=f,r.bits=l,ki;e.adler=r.check=1,r.mode=Hi;case Hi:if(t===wi||t===bi)break e;case Fi:if(r.last){f>>>=7&l,l-=7&l,r.mode=eo;break}for(;l<3;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}switch(r.last=1&f,l-=1,3&(f>>>=1)){case 0:r.mode=Ni;break;case 1:if(go(r),r.mode=Xi,t===bi){f>>>=2,l-=2;break e}break;case 2:r.mode=Wi;break;case 3:e.msg="invalid block type",r.mode=no}f>>>=2,l-=2;break;case Ni:for(f>>>=7&l,l-=7&l;l<32;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if((65535&f)!=(f>>>16^65535)){e.msg="invalid stored block lengths",r.mode=no;break}if(r.length=65535&f,f=0,l=0,r.mode=Zi,t===bi)break e;case Zi:r.mode=ji;case ji:if(d=r.length){if(d>s&&(d=s),d>h&&(d=h),0===d)break e;jt(i,n,o,d,a),s-=d,o+=d,h-=d,a+=d,r.length-=d;break}r.mode=Hi;break;case Wi:for(;l<14;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(r.nlen=257+(31&f),f>>>=5,l-=5,r.ndist=1+(31&f),f>>>=5,l-=5,r.ncode=4+(15&f),f>>>=4,l-=4,r.nlen>286||r.ndist>30){e.msg="too many length or distance symbols",r.mode=no;break}r.have=0,r.mode=Yi;case Yi:for(;r.have<r.ncode;){for(;l<3;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}r.lens[B[r.have++]]=7&f,f>>>=3,l-=3}for(;r.have<19;)r.lens[B[r.have++]]=0;if(r.lencode=r.lendyn,r.lenbits=7,S={bits:r.lenbits},E=di(pi,r.lens,0,19,r.lencode,0,r.work,S),r.lenbits=S.bits,E){e.msg="invalid code lengths set",r.mode=no;break}r.have=0,r.mode=Ki;case Ki:for(;r.have<r.nlen+r.ndist;){for(;v=(R=r.lencode[f&(1<<r.lenbits)-1])>>>16&255,w=65535&R,!((g=R>>>24)<=l);){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(w<16)f>>>=g,l-=g,r.lens[r.have++]=w;else{if(16===w){for(x=g+2;l<x;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(f>>>=g,l-=g,0===r.have){e.msg="invalid bit length repeat",r.mode=no;break}k=r.lens[r.have-1],d=3+(3&f),f>>>=2,l-=2}else if(17===w){for(x=g+3;l<x;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}l-=g,k=0,d=3+(7&(f>>>=g)),f>>>=3,l-=3}else{for(x=g+7;l<x;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}l-=g,k=0,d=11+(127&(f>>>=g)),f>>>=7,l-=7}if(r.have+d>r.nlen+r.ndist){e.msg="invalid bit length repeat",r.mode=no;break}for(;d--;)r.lens[r.have++]=k}}if(r.mode===no)break;if(0===r.lens[256]){e.msg="invalid code -- missing end-of-block",r.mode=no;break}if(r.lenbits=9,S={bits:r.lenbits},E=di(_i,r.lens,0,r.nlen,r.lencode,0,r.work,S),r.lenbits=S.bits,E){e.msg="invalid literal/lengths set",r.mode=no;break}if(r.distbits=6,r.distcode=r.distdyn,S={bits:r.distbits},E=di(gi,r.lens,r.nlen,r.ndist,r.distcode,0,r.work,S),r.distbits=S.bits,E){e.msg="invalid distances set",r.mode=no;break}if(r.mode=Xi,t===bi)break e;case Xi:r.mode=qi;case qi:if(s>=6&&h>=258){e.next_out=a,e.avail_out=h,e.next_in=o,e.avail_in=s,r.hold=f,r.bits=l,ri(e,u),a=e.next_out,i=e.output,h=e.avail_out,o=e.next_in,n=e.input,s=e.avail_in,f=r.hold,l=r.bits,r.mode===Hi&&(r.back=-1);break}for(r.back=0;v=(R=r.lencode[f&(1<<r.lenbits)-1])>>>16&255,w=65535&R,!((g=R>>>24)<=l);){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(v&&0==(240&v)){for(b=g,y=v,m=w;v=(R=r.lencode[m+((f&(1<<b+y)-1)>>b)])>>>16&255,w=65535&R,!(b+(g=R>>>24)<=l);){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}f>>>=b,l-=b,r.back+=b}if(f>>>=g,l-=g,r.back+=g,r.length=w,0===v){r.mode=Qi;break}if(32&v){r.back=-1,r.mode=Hi;break}if(64&v){e.msg="invalid literal/length code",r.mode=no;break}r.extra=15&v,r.mode=Vi;case Vi:if(r.extra){for(x=r.extra;l<x;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}r.length+=f&(1<<r.extra)-1,f>>>=r.extra,l-=r.extra,r.back+=r.extra}r.was=r.length,r.mode=Gi;case Gi:for(;v=(R=r.distcode[f&(1<<r.distbits)-1])>>>16&255,w=65535&R,!((g=R>>>24)<=l);){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(0==(240&v)){for(b=g,y=v,m=w;v=(R=r.distcode[m+((f&(1<<b+y)-1)>>b)])>>>16&255,w=65535&R,!(b+(g=R>>>24)<=l);){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}f>>>=b,l-=b,r.back+=b}if(f>>>=g,l-=g,r.back+=g,64&v){e.msg="invalid distance code",r.mode=no;break}r.offset=w,r.extra=15&v,r.mode=$i;case $i:if(r.extra){for(x=r.extra;l<x;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}r.offset+=f&(1<<r.extra)-1,f>>>=r.extra,l-=r.extra,r.back+=r.extra}if(r.offset>r.dmax){e.msg="invalid distance too far back",r.mode=no;break}r.mode=Ji;case Ji:if(0===h)break e;if(d=u-h,r.offset>d){if((d=r.offset-d)>r.whave&&r.sane){e.msg="invalid distance too far back",r.mode=no;break}d>r.wnext?(d-=r.wnext,p=r.wsize-d):p=r.wnext-d,d>r.length&&(d=r.length),_=r.window}else _=i,p=a-r.offset,d=r.length;d>h&&(d=h),h-=d,r.length-=d;do{i[a++]=_[p++]}while(--d);0===r.length&&(r.mode=qi);break;case Qi:if(0===h)break e;i[a++]=r.length,h--,r.mode=qi;break;case eo:if(r.wrap){for(;l<32;){if(0===s)break e;s--,f|=n[o++]<<l,l+=8}if(u-=h,e.total_out+=u,r.total+=u,u&&(e.adler=r.check=r.flags?$r(r.check,i,u,a-u):Vr(r.check,i,u,a-u)),u=h,(r.flags?f:ho(f))!==r.check){e.msg="incorrect data check",r.mode=no;break}f=0,l=0}r.mode=to;case to:if(r.wrap&&r.flags){for(;l<32;){if(0===s)break e;s--,f+=n[o++]<<l,l+=8}if(f!==(4294967295&r.total)){e.msg="incorrect length check",r.mode=no;break}f=0,l=0}r.mode=ro;case ro:E=mi;break e;case no:E=Si;break e;case io:return xi;case oo:default:return Ei}return e.next_out=a,e.avail_out=h,e.next_in=o,e.avail_in=s,r.hold=f,r.bits=l,(r.wsize||u!==e.avail_out&&r.mode<no&&(r.mode<eo||t!==vi))&&function(e,t,r,n){var i,o=e.state;null===o.window&&(o.wsize=1<<o.wbits,o.wnext=0,o.whave=0,o.window=new Wt(o.wsize)),n>=o.wsize?(jt(o.window,t,r-o.wsize,o.wsize,0),o.wnext=0,o.whave=o.wsize):((i=o.wsize-o.wnext)>n&&(i=n),jt(o.window,t,r-n,i,o.wnext),(n-=i)?(jt(o.window,t,r-n,n,0),o.wnext=n,o.whave=o.wsize):(o.wnext+=i,o.wnext===o.wsize&&(o.wnext=0),o.whave<o.wsize&&(o.whave+=i)))}(e,e.output,e.next_out,u-e.avail_out),c-=e.avail_in,u-=e.avail_out,e.total_in+=c,e.total_out+=u,r.total+=u,r.wrap&&u&&(e.adler=r.check=r.flags?$r(r.check,i,u,e.next_out-u):Vr(r.check,i,u,e.next_out-u)),e.data_type=r.bits+(r.last?64:0)+(r.mode===Hi?128:0)+(r.mode===Xi||r.mode===Zi?256:0),(0===c&&0===u||t===vi)&&E===yi&&(E=Ri),E}var wo,bo=1,yo=7;function mo(e){if(e<bo||e>yo)throw new TypeError("Bad argument");this.mode=e,this.init_done=!1,this.write_in_progress=!1,this.pending_close=!1,this.windowBits=0,this.level=0,this.memLevel=0,this.strategy=0,this.dictionary=null}function ko(e,t){for(var r=0;r<e.length;r++)this[t+r]=e[r]}mo.prototype.init=function(e,t,r,n,i){var o;switch(this.windowBits=e,this.level=t,this.memLevel=r,this.strategy=n,3!==this.mode&&4!==this.mode||(this.windowBits+=16),this.mode===yo&&(this.windowBits+=32),5!==this.mode&&6!==this.mode||(this.windowBits=-this.windowBits),this.strm=new Zt,this.mode){case bo:case 3:case 5:o=function(e,t,r,n,i,o){if(!e)return sn;var a=1;if(t===ln&&(t=6),n<0?(a=0,n=-n):n>15&&(a=2,n-=16),i<1||i>vn||r!==gn||n<8||n>15||t<0||t>9||o<0||o>pn)return Hn(e,sn);8===n&&(n=9);var s=new $n;return e.state=s,s.strm=e,s.wrap=a,s.gzhead=null,s.w_bits=n,s.w_size=1<<s.w_bits,s.w_mask=s.w_size-1,s.hash_bits=i+7,s.hash_size=1<<s.hash_bits,s.hash_mask=s.hash_size-1,s.hash_shift=~~((s.hash_bits+En-1)/En),s.window=new Wt(2*s.w_size),s.head=new Yt(s.hash_size),s.prev=new Yt(s.w_size),s.lit_bufsize=1<<i+6,s.pending_buf_size=4*s.lit_bufsize,s.pending_buf=new Wt(s.pending_buf_size),s.d_buf=1*s.lit_bufsize,s.l_buf=3*s.lit_bufsize,s.level=t,s.strategy=o,s.method=r,Jn(e)}(this.strm,this.level,8,this.windowBits,this.memLevel,this.strategy);break;case 2:case 4:case 6:case yo:o=co(this.strm,this.windowBits);break;default:throw new Error("Unknown mode "+this.mode)}0===o?(this.write_in_progress=!1,this.init_done=!0):this._error(o)},mo.prototype.params=function(){throw new Error("deflateParams Not supported")},mo.prototype._writeCheck=function(){if(!this.init_done)throw new Error("write before init");if(0===this.mode)throw new Error("already finalized");if(this.write_in_progress)throw new Error("write already in progress");if(this.pending_close)throw new Error("close is pending")},mo.prototype.write=function(e,t,r,n,i,o,a){this._writeCheck(),this.write_in_progress=!0;var s=this;return de(function(){s.write_in_progress=!1;var h=s._write(e,t,r,n,i,o,a);s.callback(h[0],h[1]),s.pending_close&&s.close()}),this},mo.prototype.writeSync=function(e,t,r,n,i,o,a){return this._writeCheck(),this._write(e,t,r,n,i,o,a)},mo.prototype._write=function(e,t,r,n,i,o,a){if(this.write_in_progress=!0,0!==e&&1!==e&&2!==e&&3!==e&&4!==e&&5!==e)throw new Error("Invalid flush value");null==t&&(t=new p(0),n=0,r=0),i._set?i.set=i._set:i.set=ko;var s,h=this.strm;switch(h.avail_in=n,h.input=t,h.next_in=r,h.avail_out=a,h.output=i,h.next_out=o,this.mode){case bo:case 3:case 5:s=Qn(h,e);break;case yo:case 2:case 4:case 6:s=vo(h,e);break;default:throw new Error("Unknown mode "+this.mode)}return 1!==s&&0!==s&&this._error(s),this.write_in_progress=!1,[h.avail_in,h.avail_out]},mo.prototype.close=function(){this.write_in_progress?this.pending_close=!0:(this.pending_close=!1,this.mode===bo||3===this.mode||5===this.mode?function(e){var t;e&&e.state&&((t=e.state.status)!==An&&t!==Bn&&t!==zn&&t!==Ln&&t!==Tn&&t!==Mn&&t!==Cn?Hn(e,sn):(e.state=null,t===Mn&&Hn(e,hn)))}(this.strm):function(e){if(!e||!e.state)return Ei;var t=e.state;t.window&&(t.window=null),e.state=null}(this.strm),this.mode=0)},mo.prototype.reset=function(){switch(this.mode){case bo:case 5:wo=Jn(this.strm);break;case 2:case 6:wo=lo(this.strm)}0!==wo&&this._error(wo)},mo.prototype._error=function(e){this.onerror(Nt[e]+": "+this.strm.msg,e),this.write_in_progress=!1,this.pending_close&&this.close()};var Eo=Object.freeze({NONE:0,DEFLATE:bo,INFLATE:2,GZIP:3,GUNZIP:4,DEFLATERAW:5,INFLATERAW:6,UNZIP:yo,Z_NO_FLUSH:0,Z_PARTIAL_FLUSH:1,Z_SYNC_FLUSH:2,Z_FULL_FLUSH:3,Z_FINISH:4,Z_BLOCK:5,Z_TREES:6,Z_OK:0,Z_STREAM_END:1,Z_NEED_DICT:2,Z_ERRNO:-1,Z_STREAM_ERROR:-2,Z_DATA_ERROR:-3,Z_BUF_ERROR:-5,Z_NO_COMPRESSION:0,Z_BEST_SPEED:1,Z_BEST_COMPRESSION:9,Z_DEFAULT_COMPRESSION:-1,Z_FILTERED:1,Z_HUFFMAN_ONLY:2,Z_RLE:3,Z_FIXED:4,Z_DEFAULT_STRATEGY:0,Z_BINARY:0,Z_TEXT:1,Z_UNKNOWN:2,Z_DEFLATED:8,Zlib:mo});var So={};Object.keys(Eo).forEach(function(e){So[e]=Eo[e]}),So.Z_MIN_WINDOWBITS=8,So.Z_MAX_WINDOWBITS=15,So.Z_DEFAULT_WINDOWBITS=15,So.Z_MIN_CHUNK=64,So.Z_MAX_CHUNK=1/0,So.Z_DEFAULT_CHUNK=16384,So.Z_MIN_MEMLEVEL=1,So.Z_MAX_MEMLEVEL=9,So.Z_DEFAULT_MEMLEVEL=8,So.Z_MIN_LEVEL=-1,So.Z_MAX_LEVEL=9,So.Z_DEFAULT_LEVEL=So.Z_DEFAULT_COMPRESSION;var xo={Z_OK:So.Z_OK,Z_STREAM_END:So.Z_STREAM_END,Z_NEED_DICT:So.Z_NEED_DICT,Z_ERRNO:So.Z_ERRNO,Z_STREAM_ERROR:So.Z_STREAM_ERROR,Z_DATA_ERROR:So.Z_DATA_ERROR,Z_MEM_ERROR:So.Z_MEM_ERROR,Z_BUF_ERROR:So.Z_BUF_ERROR,Z_VERSION_ERROR:So.Z_VERSION_ERROR};function Ro(e,t,r){var n=[],i=0;function o(){for(var t;null!==(t=e.read());)n.push(t),i+=t.length;e.once("readable",o)}function a(){var t=p.concat(n,i);n=[],r(null,t),e.close()}e.on("error",function(t){e.removeListener("end",a),e.removeListener("readable",o),r(t)}),e.on("end",a),e.end(t),o()}function Ao(e,t){if("string"==typeof t&&(t=new p(t)),!$(t))throw new TypeError("Not a string or buffer");var r=So.Z_FINISH;return e._processChunk(t,r)}function Bo(e){if(!(this instanceof Bo))return new Bo(e);Io.call(this,e,So.DEFLATE)}function zo(e){if(!(this instanceof zo))return new zo(e);Io.call(this,e,So.INFLATE)}function Lo(e){if(!(this instanceof Lo))return new Lo(e);Io.call(this,e,So.GZIP)}function To(e){if(!(this instanceof To))return new To(e);Io.call(this,e,So.GUNZIP)}function Mo(e){if(!(this instanceof Mo))return new Mo(e);Io.call(this,e,So.DEFLATERAW)}function Co(e){if(!(this instanceof Co))return new Co(e);Io.call(this,e,So.INFLATERAW)}function Do(e){if(!(this instanceof Do))return new Do(e);Io.call(this,e,So.UNZIP)}function Io(e,t){if(this._opts=e=e||{},this._chunkSize=e.chunkSize||So.Z_DEFAULT_CHUNK,Ot.call(this,e),e.flush&&e.flush!==So.Z_NO_FLUSH&&e.flush!==So.Z_PARTIAL_FLUSH&&e.flush!==So.Z_SYNC_FLUSH&&e.flush!==So.Z_FULL_FLUSH&&e.flush!==So.Z_FINISH&&e.flush!==So.Z_BLOCK)throw new Error("Invalid flush flag: "+e.flush);if(this._flushFlag=e.flush||So.Z_NO_FLUSH,e.chunkSize&&(e.chunkSize<So.Z_MIN_CHUNK||e.chunkSize>So.Z_MAX_CHUNK))throw new Error("Invalid chunk size: "+e.chunkSize);if(e.windowBits&&(e.windowBits<So.Z_MIN_WINDOWBITS||e.windowBits>So.Z_MAX_WINDOWBITS))throw new Error("Invalid windowBits: "+e.windowBits);if(e.level&&(e.level<So.Z_MIN_LEVEL||e.level>So.Z_MAX_LEVEL))throw new Error("Invalid compression level: "+e.level);if(e.memLevel&&(e.memLevel<So.Z_MIN_MEMLEVEL||e.memLevel>So.Z_MAX_MEMLEVEL))throw new Error("Invalid memLevel: "+e.memLevel);if(e.strategy&&e.strategy!=So.Z_FILTERED&&e.strategy!=So.Z_HUFFMAN_ONLY&&e.strategy!=So.Z_RLE&&e.strategy!=So.Z_FIXED&&e.strategy!=So.Z_DEFAULT_STRATEGY)throw new Error("Invalid strategy: "+e.strategy);if(e.dictionary&&!$(e.dictionary))throw new Error("Invalid dictionary: it should be a Buffer instance");this._binding=new So.Zlib(t);var r=this;this._hadError=!1,this._binding.onerror=function(e,t){r._binding=null,r._hadError=!0;var n=new Error(e);n.errno=t,n.code=So.codes[t],r.emit("error",n)};var n=So.Z_DEFAULT_COMPRESSION;"number"==typeof e.level&&(n=e.level);var i=So.Z_DEFAULT_STRATEGY;"number"==typeof e.strategy&&(i=e.strategy),this._binding.init(e.windowBits||So.Z_DEFAULT_WINDOWBITS,n,e.memLevel||So.Z_DEFAULT_MEMLEVEL,i,e.dictionary),this._buffer=new p(this._chunkSize),this._offset=0,this._closed=!1,this._level=n,this._strategy=i,this.once("end",this.close)}Object.keys(xo).forEach(function(e){xo[xo[e]]=e}),Be(Io,Ot),Io.prototype.params=function(e,t,r){if(e<So.Z_MIN_LEVEL||e>So.Z_MAX_LEVEL)throw new RangeError("Invalid compression level: "+e);if(t!=So.Z_FILTERED&&t!=So.Z_HUFFMAN_ONLY&&t!=So.Z_RLE&&t!=So.Z_FIXED&&t!=So.Z_DEFAULT_STRATEGY)throw new TypeError("Invalid strategy: "+t);if(this._level!==e||this._strategy!==t){var n=this;this.flush(So.Z_SYNC_FLUSH,function(){n._binding.params(e,t),n._hadError||(n._level=e,n._strategy=t,r&&r())})}else de(r)},Io.prototype.reset=function(){return this._binding.reset()},Io.prototype._flush=function(e){this._transform(new p(0),"",e)},Io.prototype.flush=function(e,t){var r=this._writableState;if(("function"==typeof e||void 0===e&&!t)&&(t=e,e=So.Z_FULL_FLUSH),r.ended)t&&de(t);else if(r.ending)t&&this.once("end",t);else if(r.needDrain){var n=this;this.once("drain",function(){n.flush(t)})}else this._flushFlag=e,this.write(new p(0),"",t)},Io.prototype.close=function(e){if(e&&de(e),!this._closed){this._closed=!0,this._binding.close();var t=this;de(function(){t.emit("close")})}},Io.prototype._transform=function(e,t,r){var n,i=this._writableState,o=(i.ending||i.ended)&&(!e||i.length===e.length);if(null===!e&&!$(e))return r(new Error("invalid input"));o?n=So.Z_FINISH:(n=this._flushFlag,e.length>=i.length&&(this._flushFlag=this._opts.flush||So.Z_NO_FLUSH)),this._processChunk(e,n,r)},Io.prototype._processChunk=function(e,t,r){var n=e&&e.length,i=this._chunkSize-this._offset,o=0,a=this,s="function"==typeof r;if(!s){var h,f=[],l=0;this.on("error",function(e){h=e});do{var c=this._binding.writeSync(t,e,o,n,this._buffer,this._offset,i)}while(!this._hadError&&_(c[0],c[1]));if(this._hadError)throw h;var u=p.concat(f,l);return this.close(),u}var d=this._binding.write(t,e,o,n,this._buffer,this._offset,i);function _(h,c){if(!a._hadError){var u=i-c;if(function(e,t){if(!e)throw new Error(t)}(u>=0,"have should not go down"),u>0){var d=a._buffer.slice(a._offset,a._offset+u);a._offset+=u,s?a.push(d):(f.push(d),l+=d.length)}if((0===c||a._offset>=a._chunkSize)&&(i=a._chunkSize,a._offset=0,a._buffer=new p(a._chunkSize)),0===c){if(o+=n-h,n=h,!s)return!0;var g=a._binding.write(t,e,o,n,a._buffer,a._offset,a._chunkSize);return g.callback=_,void(g.buffer=e)}if(!s)return!1;r()}}d.buffer=e,d.callback=_},Be(Bo,Io),Be(zo,Io),Be(Lo,Io),Be(To,Io),Be(Mo,Io),Be(Co,Io),Be(Do,Io);var Po={codes:xo,createDeflate:function(e){return new Bo(e)},createInflate:function(e){return new zo(e)},createDeflateRaw:function(e){return new Mo(e)},createInflateRaw:function(e){return new Co(e)},createGzip:function(e){return new Lo(e)},createGunzip:function(e){return new To(e)},createUnzip:function(e){return new Do(e)},deflate:function(e,t,r){return"function"==typeof t&&(r=t,t={}),Ro(new Bo(t),e,r)},deflateSync:function(e,t){return Ao(new Bo(t),e)},gzip:function(e,t,r){return"function"==typeof t&&(r=t,t={}),Ro(new Lo(t),e,r)},gzipSync:function(e,t){return Ao(new Lo(t),e)},deflateRaw:function(e,t,r){return"function"==typeof t&&(r=t,t={}),Ro(new Mo(t),e,r)},deflateRawSync:function(e,t){return Ao(new Mo(t),e)},unzip:function(e,t,r){return"function"==typeof t&&(r=t,t={}),Ro(new Do(t),e,r)},unzipSync:function(e,t){return Ao(new Do(t),e)},inflate:function(e,t,r){return"function"==typeof t&&(r=t,t={}),Ro(new zo(t),e,r)},inflateSync:function(e,t){return Ao(new zo(t),e)},gunzip:function(e,t,r){return"function"==typeof t&&(r=t,t={}),Ro(new To(t),e,r)},gunzipSync:function(e,t){return Ao(new To(t),e)},inflateRaw:function(e,t,r){return"function"==typeof t&&(r=t,t={}),Ro(new Co(t),e,r)},inflateRawSync:function(e,t){return Ao(new Co(t),e)},Deflate:Bo,Inflate:zo,Gzip:Lo,Gunzip:To,DeflateRaw:Mo,InflateRaw:Co,Unzip:Do,Zlib:Io};return class{constructor(e,t,r){this.SDKAPPID=e,this.EXPIRETIME=r,this.PRIVATEKEY=t}genTestUserSig(e){return this._isNumber(this.SDKAPPID)?this._isString(this.PRIVATEKEY)?this._isString(e)?this._isNumber(this.EXPIRETIME)?(console.log("sdkAppID="+this.SDKAPPID+" key="+this.PRIVATEKEY+" userID="+e+" expire="+this.EXPIRETIME),this.genSigWithUserbuf(e,this.EXPIRETIME,null)):(console.error("expireTime must be a number"),""):(console.error("userID must be a string"),""):(console.error("privateKey must be a string"),""):(console.error("sdkAppID must be a number"),"")}newBuffer(e,t){return p.from?p.from(e,t):new p(e,t)}unescape(e){return e.replace(/_/g,"=").replace(/\-/g,"/").replace(/\*/g,"+")}escape(e){return e.replace(/\+/g,"*").replace(/\//g,"-").replace(/=/g,"_")}encode(e){return this.escape(this.newBuffer(e).toString("base64"))}decode(e){return this.newBuffer(this.unescape(e),"base64")}base64encode(e){return this.newBuffer(e).toString("base64")}base64decode(e){return this.newBuffer(e,"base64").toString()}_hmacsha256(e,t,r,n){let i="TLS.identifier:"+e+"\n";i+="TLS.sdkappid:"+this.SDKAPPID+"\n",i+="TLS.time:"+t+"\n",i+="TLS.expire:"+r+"\n",null!=n&&(i+="TLS.userbuf:"+n+"\n");let o=te.HmacSHA256(i,this.PRIVATEKEY);return te.enc.Base64.stringify(o)}_utc(){return Math.round(Date.now()/1e3)}_isNumber(e){return null!==e&&("number"==typeof e&&!isNaN(e-0)||"object"==typeof e&&e.constructor===Number)}_isString(e){return"string"==typeof e}genSigWithUserbuf(e,t,r){let n=this._utc(),i={"TLS.ver":"2.0","TLS.identifier":e,"TLS.sdkappid":this.SDKAPPID,"TLS.time":n,"TLS.expire":t},o="";if(null!=r){let a=this.base64encode(r);i["TLS.userbuf"]=a,o=this._hmacsha256(e,n,t,a)}else o=this._hmacsha256(e,n,t,null);i["TLS.sig"]=o;let a=JSON.stringify(i),s=Po.deflateSync(this.newBuffer(a)).toString("base64"),h=this.escape(s);return console.log("ret="+h),h}validate(e){let t=this.decode(e),r=Po.inflateSync(t);console.log("validate ret="+r)}}});
