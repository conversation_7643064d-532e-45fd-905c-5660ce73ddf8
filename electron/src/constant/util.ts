import { ipc<PERSON><PERSON><PERSON> } from 'electron';
import fs from 'fs';
import os from 'os';
import path from 'path';

const isMac = os.platform() === 'darwin';
const isWindows = os.platform() === 'win32';
const isLinux = os.platform() === 'linux';

function getTCICPath(appPath: string) {
  let sdkPath = '';
  let sdkPath1 = '';
  let nodePath = '';
  const devPath = path.resolve(appPath, '..');
  if (isWindows) {
    nodePath = `${appPath}\\` + 'node_modules';
    sdkPath = `${nodePath}\\` + 'tcic-electron-sdk';
    sdkPath1 = `${nodePath}\\` + '@tencent' + '\\' + 'tcic-electron-sdk';
  } else {
    nodePath = `${appPath}/` + 'node_modules';
    sdkPath = `${nodePath}/` + 'tcic-electron-sdk';
    sdkPath1 = `${nodePath}/` + '@tencent' + '/' + 'tcic-electron-sdk';
  }
  if (fs.existsSync(sdkPath)) {
    return sdkPath;
  }

  if (fs.existsSync(sdkPath1)) {
    return sdkPath1;
  }

  return devPath;
}

export function getAppPath() {
  // TODO:
  return getTCICPath(process.env.APP_ASAR);
}
