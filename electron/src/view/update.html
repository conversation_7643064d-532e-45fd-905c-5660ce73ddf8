<style type="text/css">
    .tcic-maskbg {
        background-color: #000000bf;
        position: fixed;
        z-index: 5000;
        width: 100%;
        height: 100%;
        left: 0px;
        top: 0px;
    }

    .tcic-update-dialog {
        max-width: 400px;
        margin: 50px auto;
        background: #1d2029;
        padding: 20px;
        color: #000;
        background: #FFFFFF;
        box-shadow: 0px 6px 20px rgba(24, 64, 94, 0.1);
        border-radius: 10px;
    }

    .tcic-dialog-content {
        padding: 15px;
        margin-bottom: 15px;
        background: #F6F7FB;
        border-radius: 4px;
        min-height: 176px;
    }

    .tcic-dialog-content ul {
        margin: 0px;
        padding-left: 30px;
        padding-right: 20px;
        font-size: 12px;
        line-height: 18px;
    }

    .tcic-dialog-content ul li {
        margin-bottom: 5px;
    }

    .tcic-dialog-foot button.impt,
    .tcic-dialog-foot button.weak {
        background: #006eff;
        border: none;
        border: 1px solid #006eff;
        padding: 6px 12px;
        border-radius: 5px;
        color: #fff;
        min-width: 96px;
        vertical-align: middle;
        cursor: pointer;
        font-size: 12px;
    }

    .tcic-dialog-foot button.weak {
        color: #006EFF;
        background: #fff;
        border: 1px solid #ccc;
    }

    .tcic-dialog-foot {
        display: none;
        margin-top: 20px;
    }

    .tcic-dialog-foot .tcic-dialog-btnwrap {
        text-align: center;
        word-spacing: 20px;

    }

    .tcic-dialog-logo {
        text-align: center;
    }

    .tcic-dialog-logo h2 {
        font-weight: 600;
        font-family: 'TencentSans';
        font-style: normal;
        font-weight: 700;
        font-size: 21.5385px;
        line-height: 32px;
        text-align: center;
    }

    .tcic-dialog-process-wrapper {
        text-align: center;
    }

    .tcic-dialog-process-wrapper .tcic-dialog-process {
        width: 1%;
        height: 10px;
        text-align: left;
        display: inline-block;
        background: #006EFF;
        border-radius: 1px;
        transition: 0.1s;
    }

    .tcic-dialog-process-wrapper .tcic-dialog-process-total {
        background: #ECEFF8;
        border-radius: 2px;
        text-align: left;
        line-height: 10px;
    }

    .tcic-dialog-process-desc {
        text-align: left;
        margin-bottom: 20px;
    }

    .tcic-dialog-process-wrapper div {
        margin: 15px 0px;
    }
</style>
<div class="tcic-maskbg" style="display:none" id="tcic-check-dialog__1">
    <div class="tcic-update-dialog">
        <div class="tcic-dialog-header">
            <div class="tcic-dialog-logo">
                <h2 id="tcic-update-dialog-title">Version Update Notification</h2>
            </div>
        </div>
        <div class="tcic-dialog-content" id="tcic-check-dialog__2">
            <!-- <ul>
                <li>msg</li>
                <li>msg</li>
            </ul> -->
        </div>


        <div class="tcic-dialog-process-wrapper" id="tcic-check-dialog__3">
            <div class="tcic-dialog-process-desc" id="tcic-dialog-process-desc">Downloading Installer...</div>
            <div class="tcic-dialog-process-total">
                <span class="tcic-dialog-process" id="tcic-dialog-process-bar"></span>
            </div>
        </div>
        <div class="tcic-dialog-foot" id="tcic-check-dialog__4" style="display:block">
            <div class="tcic-dialog-process-desc" id="tcic-dialog-process-confirm-desc"></div>
            <div class="tcic-dialog-content" id="tcic-dialog-process-confirm-content">
                <!-- <ul>
                    <li>msg</li>
                    <li>msg</li>
                </ul> -->
            </div>
            <div class="tcic-dialog-btnwrap">
                <button id="tcic-confirmBtn_1" class="impt">Ok</button>
                <button id="tcic-confirmBtn_2" class="weak">Cancel</button>
            </div>
        </div>
        <div class="tcic-dialog-foot" id="tcic-check-dialog__5" style="display:block">
            <div class="tcic-dialog-btnwrap">
                <button id="tcic-confirmBtn_3" class="impt" onclick="_tcic_update_dialog.cancel()">Close</button>
            </div>
        </div>
    </div>
</div>
<script>
    (function (global) {
        function getEl(id) {
            return document.getElementById(id);
        }
        function cancel() {
            getEl('tcic-check-dialog__1').style.display = 'none'
        }

        function show(elId) {
            getEl(elId).style.display = 'block'
        }
        function hide(elId) {
            getEl(elId).style.display = 'none'
        }
        function toggleItems(showList, hideList) {
            showList.forEach(item => {
                show(item)
            })
            hideList.forEach(item => {
                hide(item)
            })
        }
        function msgBox(msgList) {
            try {
                let d1 = getEl('tcic-check-dialog__1');
                let footer = getEl('tcic-check-dialog__5');
                let msgBox = getEl('tcic-check-dialog__2');
                let msgContent = '<ul>';
                let len = msgList.length;
                if (len) {
                    if (len == 1) {
                        msgContent = `<div>${msgList[0]}</div>`
                    } else {
                        msgContent += msgList.map(msg => `<li>${msg}</li>`);
                        msgContent += '</ul>'
                    }
                } else {
                    msgContent = '<div>No update log</div>'
                }
                msgBox.innerHTML = msgContent
                d1.style.display = 'block'

                toggleItems(['tcic-check-dialog__1', 'tcic-check-dialog__2', 'tcic-check-dialog__5'], ['tcic-check-dialog__3', 'tcic-check-dialog__4'])

            } catch (err) {
                console.err(err)
            }
        }
        function process(desc, percent) {
            try {
                let descEl = getEl('tcic-dialog-process-desc');
                let processBarEl = getEl('tcic-dialog-process-bar');
                descEl.innerText = desc
                processBarEl.style.width = `${percent}%`
                toggleItems(['tcic-check-dialog__1', 'tcic-check-dialog__3'], ['tcic-check-dialog__2', 'tcic-check-dialog__4', 'tcic-check-dialog__5'])
            } catch (err) {
                console.err(err)
            }
        }
        function sconfirm(descList) {
            toggleItems(['tcic-check-dialog__1', 'tcic-check-dialog__4'], ['tcic-dialog-process-confirm-content', 'tcic-check-dialog__2', 'tcic-check-dialog__3', 'tcic-check-dialog__5'])
            return new Promise((resolve) => {
                try {
                    let descEl = getEl('tcic-dialog-process-confirm-desc');
                    let okBtn = getEl('tcic-confirmBtn_1');
                    let cancelBtn = getEl('tcic-confirmBtn_2');
                    let content = getEl('tcic-dialog-process-confirm-content');
                    let msgMain = `<div>${descList.shift()}</div>`;
                    let msgContent = '<ul>';
                    let len = descList.length;
                    if (len) {
                        msgContent += descList.map(msg => `<li>${msg}</li>`).join('');
                        msgContent += '</ul>';
                        content.style.display = 'block';
                    }
                    descEl.innerHTML = msgMain;
                    content.innerHTML = msgContent;
                    let okHandler = () => {
                        okBtn.removeEventListener('click', okHandler);
                        cancel()
                        resolve(true)
                    }
                    let cancelHandler = () => {
                        cancelBtn.removeEventListener('click', cancelHandler);
                        cancel()
                        resolve(false)
                    }
                    okBtn.addEventListener('click', okHandler);
                    cancelBtn.addEventListener('click', cancelHandler);
                } catch (err) {
                    console.err(err)
                }

            })
        }

        function setText(option) {
            let titleEl = getEl('tcic-update-dialog-title')
            let okBtn = getEl("tcic-confirmBtn_1")
            let cancelBtn = getEl("tcic-confirmBtn_2")
            let closeBtn = getEl("tcic-confirmBtn_3")
            if (titleEl) {
                titleEl.innerText = option.title?option.title:titleEl.innerText;
                okBtn.innerText = option.ok?option.ok:okBtn.innerText;
                cancelBtn.innerText = option.cancel?option.cancel:cancelBtn.innerText;
                closeBtn.innerText = option.close?option.close:closeBtn.innerText;
            }
        }

        global._tcic_update_dialog = {
            setText,
            msgBox,
            process,
            sconfirm,
            cancel,
        }
    })(window)
</script>