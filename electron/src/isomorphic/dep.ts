
import type * as Updater from '@tencent/lcic-electron-hotupdater';
import { type default as UI } from '@tencent/tcic-web-ui';


// @ts-ignore
export const nodeRequire: NodeRequire = typeof __webpack_require__ === 'function' ? __non_webpack_require__ : require;
const updater = nodeRequire('@tencent/lcic-electron-hotupdater') as typeof Updater;


export const {
  checkForUpdatesCron,
  manualUpdates,
  fetchPkgMeta,
  setting,
} = updater;


export const ui: UI = nodeRequire('@tencent/tcic-web-ui');


// 绕过webpack处理的require,用原生node require

function tryResolve(packName: string,  throwOut = true): string {
  try {
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    return nodeRequire.resolve(packName);
  } catch (error) {
    if (throwOut) {
      throw error;
    }
    console.error('Failed to require.resolve', packName, error);
    return '';
  }
}
