/* eslint-disable new-cap */
import { BrowserWindow } from 'electron';

const winMap: Map<string, BrowserWindow> = new Map();
export const BrowserWindowProxy = new Proxy(BrowserWindow, {
  construct(target: any, args: any[]) {
    console.log(
      '%c [ target: any, args ]-6',
      'font-size:13px; background:pink; color:#bf2c9f;',
      target,
      args
    );

    // 在创建新的 BrowserWindow 时打断点
    // 创建新的 BrowserWindow
    // @ts-ignore
    const instance = new BrowserWindow(...args);
    let name = `_$${Date.now()}`;
    if (args.length && args[0]._$name) {
      name = args[0]._$name;
    }
    // @ts-ignore
    instance._$name = name;
    winMap.set(name, instance);

    return new Proxy(instance, {
      get(target: any, prop) {
        // 在访问 BrowserWindow 的属性或方法时打断点
        console.log(
          '%c [ get win ]-15',
          'font-size:13px; background:pink; color:#bf2c9f;',
          (target as any)._$name,
          prop
        );
        if (
          prop === 'show' &&
          (target as any)._$name === 'tcicScreenShareMainWindow'
        ) {
          debugger;
        }
        // 返回原始的属性或方法
        return target[prop];
      },
      set(target, prop, value) {
        // 在修改 BrowserWindow 的属性时打断点
        console.log(
          '%c [ set win ]-16',
          'font-size:13px; background:pink; color:#bf2c9f;',
          target._$name,
          prop
        );
        // 修改原始的属性
        target[prop] = value;

        return true;
      },
    });
  },
});
