// @ts-check

// If won't use `@ts-check` - just remove that comments (with `@type` JSDoc below).

/** @type import('dts-bundle-generator/config-schema').OutputOptions */
const commonOutputParams = {
    inlineDeclareGlobals: false,
    sortNodes: true,
    exportReferencedTypes: true,
    noBanner: true,
    respectPreserveConstEnum: true,
};

/** @type import('dts-bundle-generator/config-schema').BundlerConfig */
const config = {
    compilationOptions: {
        preferredConfigPath: './tsconfig.json',
    },

    entries: [
        {
            filePath: './src/main.ts',
            outFile: './dist/index.d.ts',
            noCheck: true,
            output: commonOutputParams,
        },

        //   {
        //       filePath: './src/second.ts',
        //       outFile: './out/second.d.ts',
        //       failOnClass: true,

        //       libraries: {
        //           inlinedLibraries: ['@my-company/package'],
        //       },

        //       output: commonOutputParams,
        //   },
    ],
};

module.exports = config;
