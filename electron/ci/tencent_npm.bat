REM 您可以通过setEnv函数设置原子间传递的参数
REM call:setEnv "FILENAME" "package.zip"
REM 然后在后续的原子的表单中使用${{FILENAME}}引用这个变量

REM 您可以在质量红线中创建自定义指标，然后通过setGateValue函数设置指标值
REM call:setGateValue "CodeCoverage" $myValue
REM 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住

REM cd %WORKSPACE% 可进入当前工作空间目录

cd electron
nvm use 14.17.5 64

echo TCIC_VERSION=${Major}.${Minor}.${BK_CI_BUILD_NUM} > .env
echo TCIC_CACHE_VERSION=${CACHE_VERSION} >> .env

npm --no-git-tag-version version ${Major}.${Minor}.${BK_CI_BUILD_NUM}



@REM step 2


cd electron
npm config set registry https://mirrors.tencent.com/npm/
npm i
npm run build




@REM step 3

REM 您可以通过setEnv函数设置原子间传递的参数
REM call:setEnv "FILENAME" "package.zip"
REM 然后在后续的原子的表单中使用${{FILENAME}}引用这个变量

REM 您可以在质量红线中创建自定义指标，然后通过setGateValue函数设置指标值
REM call:setGateValue "CodeCoverage" $myValue
REM 然后在质量红线选择相应指标和阈值。若不满足，流水线在执行时将会被卡住

REM cd %WORKSPACE% 可进入当前工作空间目录

cd electron

"C:\Program Files\Git\bin\sh.exe" .\scripts\cache.sh



cd electron
npm publish