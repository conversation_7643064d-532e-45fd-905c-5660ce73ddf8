import { type IpcMainEvent, type IpcMainInvokeEvent } from 'electron';

export interface IPCListenerPair {
  channel: string,
  listener: (event: IpcMainEvent, ...args: any[]) => void,
}

export interface IPCHandlerPair {
  channel: string,
  handler: (event: IpcMainInvokeEvent, ...args: any[]) => (Promise<void>) | (any),
}

export interface IPCCall {
  module: string,
  method: string,
  params: any[],
}

export interface ScreenShareParams {
  type: 0 | 1, // 0: 分享应用 1: 分享桌面
  displayId: string,
  followTarget: boolean,
}

export type ScreenBoardToolType = 0 // 鼠标
| 1 | 2 | 3 | 4 | 5 | 6 | 7 | 8 | 9;

export interface IPCClickableArea {
  rects: {x: number, y: number, width: number, height: number}[],
}

export interface IPCClickableAreaDetectMode {
  mode: 'screenCapture' | 'domRect'
}

declare global {
  /* eslint no-var: off */
  // @eslint-disable-line no-var
  var dbg: any;
  // interface globalThis {
  //   dbg: any;
  // }
  const TCIC_VERSION: string;
}
