require('./env');
const path = require("path");
const webpack = require("webpack");
const nodeExternals = require("webpack-node-externals");
// const pkg = require("./package.json");
// const versionConfig = require("./version");
const os = require("os");

// exports.isProd = () => process.env.NODE_ENV === "production";
// process.env.NODE_ENV = "development";

function getTCICEnvironment() {
  const result = {};
  // 取所有的环境变量，然后过滤出以TCIC开头的环境变量
  // 返回一个环境变量配置对象，提供给webpack InterpolateHtmlPlugin插件
  Object.keys(process.env)
    .filter(item => /TCIC/.exec(item))
    .forEach(
      (key) => {
        result[key] = JSON.stringify(process.env[key]);
      }
    );

  return result;
}

// const ENV = getTCICEnvironment();

const defineParams = {
  // PRODUCTION: JSON.stringify(false),
  // VERSION_CONFIG: JSON.stringify(versionConfig),
  // ...ENV
};


const targetPlatform = (function () {
  let target = os.platform();
  for (let i = 0; i < process.argv.length; i++) {
    if (process.argv[i].includes('--target_platform=')) {
      target = process.argv[i].replace('--target_platform=', '');
      break;
    }
  }
  if (!['win32', 'darwin'].includes) target = os.platform();
  return target;
})();
console.log('targetPlatform', targetPlatform);

// https://github1s.com/TencentCloud/trtc-education-electron/blob/HEAD/.erb/configs/webpack.config.base.js#L24-L34
// 此处的路径，需要与 package.json 中 electron-builder 配置中 .node 文件加载路径一只
const getRewritePath = function () {
  console.log('getRewritePath:', process.env.NODE_ENV);
  let rewritePathString = '';
  if (process.env.NODE_ENV === 'production') {
    rewritePathString = targetPlatform === 'win32' ? './resources' : '../Resources';
  } else if (process.env.NODE_ENV === 'development') {
    rewritePathString = 'node_modules/trtc-electron-sdk/build/Release';
  }
  return rewritePathString;
};

exports.base = {
  node: {
    global: false,
    __filename: false,
    __dirname: false,
  },
  target: 'node',
  externalsType: 'commonjs',
  externalsPresets: {
    node: true,
    electron: true,
  },
  externals: [
    nodeExternals({
      modulesFromFile: true,
    }),
    /^node:/,
    'electron',
    '@tencent/lcic-electron-hotupdater',
    '@tencent/lcic-electron-hotupdater/dist/updater',
    '@tencent/lcic-electron-hotupdater/dist/alias',
    'lcic-electron-hotupdater',
    '@tencent/tcic-web-ui',
    'tcic-web-ui',
    '@sentry/electron/main',
    '@sentry/integrations',
    '@sentry/node',
    '@sentry/electron/renderer',
    '@sentry/electron/preload',
    '@sentry/electron/renderer/integrations',
    '@sentry/cli',
    /^@sentry\//,
    /^xmagic-plugin/,
    '@sentry/browser',  // 这俩TerserPlugin会报找不到@之类的错..
    '@sentry/core',
    // https://webpack.js.org/configuration/externals/#combining-syntaxes
    // https://stackoverflow.com/questions/67550537/unexpected-character-for-webpack-externals-option
    // {
    //   ["@sentry/browser"]:{
    //     root: "@sentry/browser"
    //   }
    // },
  ],
  plugins: [
    new webpack.DefinePlugin({
      ...defineParams,
    }),
    // new webpack.IgnorePlugin({
    //   resourceRegExp: /.*/,
    //   contextRegExp:  /require\.resolve/
    // }),
  ],
  resolve: {
    extensions: [".tsx", ".ts", ".js"],
    // 为兼容node-fetch打包进来的require('node:xxx')
    alias: {
      'node:fs$': 'fs',
      'node:stream$': 'stream',
      'node:util$': 'util',
      'node:http$': 'http',
      'node:https$': 'https',
      'node:buffer$': 'buffer',
      'node:zlib$': 'zlib',
      'node:url$': 'url',
      'node:net$': 'net',
      '@': path.join(__dirname, 'src'),
    }
  },
  module: {
    rules: [
      { test: /\.node$/, loader: 'native-ext-loader', options: { rewritePath: getRewritePath() } },
      {
        test: /\.tsx?$/,
        // exclude: [path.resolve(__dirname, "node_modules")],
        exclude: /node_modules/,
        use: [
          {
            loader: 'ts-loader'
          },
          // {
          //   loader: 'babel-loader',
          //   options: {
          //     presets: ['@babel/preset-env', '@babel/preset-typescript'],
          //     plugins: ['@babel/plugin-transform-modules-commonjs']
          //   }
          // },
        ]
      },
    ],
  },
};
