## 相关地址

- 测试环境：https://devops.woa.com/console/pipeline/ecc/p-d22eeb44fd0f4984b628c7087a9268be/history
- 正式环境：https://devops.woa.com/console/pipeline/ecc/p-2dae422b89a249e5b82cb4910801c0e7/history
- 编包项目：https://git.woa.com/tcic/tcic_desktop_client/tree/custom_1.7.3
- @tencent/tcic-electron-sdk npm 包：https://mirrors.tencent.com/#/private/npm/detail?repo_id=537&project_name=%40tencent%2Ftcic-electron-sdk&search_label=package_name&search_value=tcic&page_num=1

## 当前项目流程

```sh
# 修改 electron/src/version.ts 中的版本号，测试环境修改为 `1.7.3.test`

# 修改 electron/package.json 的版本号，从上面 @tencent/tcic-electron-sdk 网站看，自己找个版本号往上改 1.7.3548.beta0

cd electron

# 更新离线包，可能需要多次执行，一定要成功了再走后面流程
npm run cache-download

# 编译包
npm run build

# 发布包
tnpm publish
```

## 编包项目流程

```sh
# 去编包项目：https://git.woa.com/tcic/tcic_desktop_client/tree/custom_1.7.3

# 把 package.json 中的 `@tencent/tcic-electron-sdk` 版本号改成上面 publish 的版本号

npm i && npm push
```

## 发包

去流水线点一点，选择 tcic_desktop_client 项目刚刚推的分支
