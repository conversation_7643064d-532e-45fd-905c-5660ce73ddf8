#!/bin/bash

# 获取环境变量 QCI_REPO_BRANCH 的值
BRANCH=${QCI_REPO_BRANCH}

# 检查 QCI_REPO_BRANCH 是否为 "develop"
if [[ "$BRANCH" == "develop" ]]; then
   echo "TARGET_DIR=latest" >> $QCI_ENV_FILE
   export TARGET_DIR="latest"
   # 检查 QCI_REPO_BRANCH 是否以 "develop/" 开头
elif [[ "$BRANCH" == develop-* ]]; then
    # 提取 "develop/" 后面的部分作为 TARGET_DIR
   export TARGET_DIR="${BRANCH#develop-}"
   echo "TARGET_DIR=${BRANCH#develop-}" >> $QCI_ENV_FILE
elif [[ "$BRANCH" == test-* ]]; then
    # 提取 "test/" 后面的部分作为 TARGET_DIR
   export TARGET_DIR="${BRANCH#test-}"
   echo "TARGET_DIR=${BRANCH#test-}" >> $QCI_ENV_FILE
else
    export TARGET_DIR="undefined"  # 如果不符合条件，可以设置为其他值
    echo "TARGET_DIR=undefined" >> $QCI_ENV_FILE
fi

# 输出结果
echo "TARGET_DIR is set to: $TARGET_DIR"

ls -a

if [ "$TARGET_DIR" != "latest" ]; then
    # 定义要操作的目录
    TARGET_PATH="${QCI_WORKSPACE}apaas-sdk/web/ui/dist"
    ls -a $TARGET_PATH
    # 检查目录是否存在
    if [ -d "$TARGET_PATH" ]; then
        cd $TARGET_PATH
        # mv class-*.html class.html
        if ls class-*.html 1> /dev/null 2>&1; then
            mv class-*.html class.html
        else
            echo "不存在 class-*.html 文件"
        fi
    else
        echo "Directory $TARGET_PATH does not exist."
    fi
else
    echo "TARGET_DIR is set to latest, no action taken exec."
fi

    
