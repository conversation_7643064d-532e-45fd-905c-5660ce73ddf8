

function get_branch() {
  # set -e
  # local branch=${BK_CI_GIT_REPO_BRANCH:-${QCI_REPO_BRANCH}}
  local branch=${branch:-$(git rev-parse --abbrev-ref HEAD)}
  branch=$(echo "$branch" | tr -s '/' '-')
  # 修正子模块游离HEAD获取分支名不正确的问题..
  if [[ branch == "HEAD" ]]; then
    local commit=$(get_commit)
    branch_name=$(git branch -r --contains $commit | sed -e 's/.*\///' -e 's/\^.*//')
  fi
  echo $branch
  # set +e
}

function get_commit() {
  # local commit=${BK_CI_GIT_REPO_HEAD_COMMIT_ID:-${QCI_REPO_commit}}
  local commit=${commit:-$(git rev-parse --short HEAD)}
  echo $commit
}

# 当前脚本所在目录
function get_scripts_dir() {
  echo $(cd "$(dirname "${BASH_SOURCE[0]}")" &>/dev/null && pwd)
}

function get_prever() {
  echo ${1:-1.$(date +'%y%m.%d%H%M')}
}

# 工作目录/传参的package.json
function new_version() {
  # .%S
  local mainver=$(node -p "require('./package.json').version.split('-')[0]")
  # 1.yymm.ddHHMM
  local prever=${1:-`get_prever`}
  # -${TCIC_WEB_BASE_PATH}
  echo ${mainver}-$(get_branch)-${prever}
}
