version: 2
env:
  FROM_ARTIFACT:
    value: 'no'
    type: select
    hide: true
    desc: 跳过构建过程直接从制品库拉取,一般用于出错回滚
    option: yes;no
  ENV_PREFIX:
    value: dev-
    type: select
    desc: web环境,对应不同环境的域名。清空选择为现网.
    option: dev-;test-;
  PUBLISH_MODE:
    value: only-web
    type: select
    require: true
    desc: "发布模式:\n  only-web (只发到cos桶)\n  only-npm (只发离线包到npm,现网latest时会自动发npm离线包)\n\
      \  both (两者都发)\n"
    option: only-web;both;only-npm
  NPM_SCOPE:
    value: '@tencent/'
    type: select
    hide: true
    desc: 选择发布到@tencent/内网包还是外网, ENV_PREFIX清空(现网)时, 强制发npm官方registry
    option: '@tencent/;both;npm-official'
  EXTRA_NPM_TAG:
    value: ''
    type: string
    hide: true
    desc: 额外的npm tag
  MFA_KEY:
    type: string
    require: true
    hide: true
    desc: NPM发布KEY
    readonly: true
    secret: X1X1cqqtnNyu0LgM4FqvsbB238pCrT45dPE7IBi3xbi1nVAysiiV8memwHttt08OgdDw3kr8mrZDUD1Dn9W1KIcOZOjN4arLXaHtP7VL3biZ1HV8+vtD4+sFCvqAi5s54rXhdH9bkNocCpfOtc0sE9ok18dV3KbDfRrpqP3lCrc=
  NPM_USERNAME:
    value: xkazer
    type: string
    require: true
    hide: true
    desc: NPM_USERNAME
    readonly: true
  NPM_EMAIL:
    value: <EMAIL>
    type: string
    require: true
    hide: true
    desc: NPM发布鉴权邮箱
    readonly: true
  NPM_TOKEN:
    type: string
    require: true
    hide: true
    desc: npm token
    readonly: true
    secret: risUQDEmEfSdsY5MDN0r6dKD+F1xtP4QmGzMHonNdyu4v2UiD4QEZpTLUkQw40km7QGfSjAdUg6ax/2gJ654SgcqT65JmkYynpcLhvJL0PhM1/JHgBQL8KWL9ssn5kgfEv/MvuQyntP1tiuSTUC93RMtfqK/RCi13DidDOgYm4g=
  TNPM_PASSWORD:
    type: string
    require: true
    hide: true
    desc: tnpm token
    readonly: true
    secret: So1noVTJg8UmhyVz/1TOsa+yxkIU4t9uffKLsmVxbATEItDXOHY4C6goxM0WSv+kK10K2Ep8z+4TAPiB4VRpaKWB6ry50BwcwsIbs9JgQdEGsQnQfy4Y4PU65LiKhin//uEgLFbKE/x2vEVN/MAT4zt7F+aJQxKPcm8re1t+hTg=
  TNPM_USERNAME:
    value: frankshao
    type: string
    require: true
    hide: true
    desc: tnpm用户名
    readonly: true
  SECRET_KEY:
    type: string
    hide: true
    readonly: true
    secret: RDpfBxUEjmkxzMkWUafBCl9yvRVjmlHVcvy+EZFY9KEiS6ziA8sMDxXit3M6BYMvaQbuaqS6oRLNLh0cmi6TkAdYy99bO2m34JxZ4VYllaHzaDJEvepf+TOuZmowvArB2GNTq969n86PgbjjR+apBGZBH0FfRjsgENo/T3dztso=
  SECRET_ID:
    type: string
    hide: true
    desc: cos secrect id
    readonly: true
    secret: Qcxiq3RBJF8ydMEOdhEvuZYt4XI2Em6hwY848BGfa0Z8Sa/rcyBWxLsLnDMNM8D+eYpKpInV6KV5dgpYDxCc0lMdDOkWZcPtP8555z+LdXeocq0jje8R1hKlPD7A72JeCwztL7gHG4OsyGNaVr30fai9lTA1ZUmNivz85eZufLU=
  GENERIC_TOKEN:
    type: string
    require: true
    hide: true
    desc: 制品库token
    readonly: true
    secret: IN8sln/ZXT7DOaiE/eHAUi0IYr+F1ISrxO1TTStHICBjbeTF1adus/Dwhhmbg1sHh6ZndqIwMJd1NKkqUxEM9zUmfouAyFLH8nlvIL7hXPBBJTfSzLhjtZSv+Kw3UcaAxjbNGKZ9roDOvaLTW151kmGpiLWFNXjkhvucGqfsuZo=
stages:
- stage: 构建并发布NPM
  tasks:
  - task: 构建发布
    cmds:
    - plugin: cmds
      params:
        cmds:
        - sh .ci/build-web-publish.sh
        - echo end.
      label: build-web-publish.sh
    output_timeout: 1h
- prompt:
  - detail: 现网人工确认
    timeout_status: 6
    env: {}
    timeout: 48h
    if: ${TCIC_WEB_BASE_PATH} = "latest"
    msg: 发布现网latest人工确认(将自动发npm包)，发布环境域名前缀为${ENV_PREFIX}
    to: $QCI_TRIGGER
worker:
  language: node_js-16
  label: JOB_MATRIX_DEVCLOUD
  tools: []
code_checkout_type: CHECKOUT_ONLY_BRANCH
