set -ex

# nvm --version
# nvm list
# nvm install 16.14.2

# $(git rev-parse HEAD)
tag=$(git tag --contains ${QCI_REPO_COMMIT})
if [[ $PUBLISH_NPM == "yes" ]] && (echo $tag | grep -q "^npm/${NPM_SCOPE}"); then
  echo "当前提交已发布至${tag}, 请勿重复发布npm"
  exit 2
fi

# TODO: 用semver 检查 $NPM_VERSION和$NPM_TAG是否合法

cd $QCI_WORKSPACE/electron

# npm i -g pnpm

echo 构建

# 发npm不需要执行任何依赖的生命周期hook脚本(pre,post,install)
npm i @tencent/lcic-electron-hotupdater@latest --save --ignore-scripts
npm i @tencent/tcic-web-ui@latest --save --ignore-scripts

# npm i --ignore-scripts

# 编译 tcic-ui

npm run eslint-nofix

# 为空时只做lint检查.
if [[ -z "$NPM_SCOPE" ]]; then
  echo $NPM_SCOPE为空!
  exit 0
fi

# 构建时,全都使用内网包..
npm run build

yum install -y jq

npm run jq

# 下载旧版离线包
# TCIC_CACHE_VERSION=1.8.0
# ./scripts/cache.sh

function publish_npm() {
  if [[ $NPM_SCOPE == "both" ]]; then
    git status
    git add .
    # 不可提交.. 否则push tag时会被push
    # git commit -m "ci temp."
    # 先发内网,再发外网
    export NPM_SCOPE="@tencent/"
    . $QCI_WORKSPACE/.ci/publish-npm.sh $1

    # git reset --hard
    # 恢复发包过程对package.json, .npmrc的修改
    git checkout -- .
    export NPM_SCOPE="npm-official"

  fi

  # 发外网时把所有内网依赖都改为外网.
  if [[ $NPM_SCOPE == "npm-official" ]]; then
    # npm remove @tencent/lcic-electron-hotupdater
    # npm remove @tencent/tcic-web-ui
    jq 'del(.dependencies["@tencent/lcic-electron-hotupdater"], .dependencies["@tencent/tcic-web-ui"])' package.json >temp.json && mv temp.json package.json
    cat package.json
    npm i lcic-electron-hotupdater@latest --save --ignore-scripts
    npm i tcic-web-ui@latest --save --ignore-scripts
    cat package.json

    . $QCI_WORKSPACE/.ci/publish-npm.sh $1
  elif [[ $NPM_SCOPE == "@tencent/" ]]; then
    . $QCI_WORKSPACE/.ci/publish-npm.sh $1
  fi

}

publish_npm tcic-electron-sdk
