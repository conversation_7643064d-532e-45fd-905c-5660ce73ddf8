# 原 web流水线备份
# http://tencent.coding.woa.com/p/TEDU_SAAS/ci/job/435251/build/current
# 现网
# http://tencent.coding.woa.com/p/TEDU_SAAS/ci/job/435257/build/current

# .coding-ci-web.yaml
# '# 脚本内容默认是单行命令执行。'
# - '    # 如需增加多行命令，请在开始和结束用"--"标识，例如：'
# - '    # --'
# - '    # if !(command -v unzip)'
# - '    # then'
# - '    #   yum install -y unzip'
# - '    # fi'

set -ex

# nvm --version
# nvm list
# nvm install 16.14.2

# 处理参数

export PUBLISH_COS=no
export PUBLISH_NPM=no

if [[ $PUBLISH_MODE == "only-web" ]]; then
  export PUBLISH_COS=yes
elif [[ $PUBLISH_MODE == "only-npm" ]]; then
  export PUBLISH_NPM=yes
elif [[ $PUBLISH_MODE == "both" ]]; then
  export PUBLISH_COS=yes
  export PUBLISH_NPM=yes
fi

echo "PUBLISH_COS=${PUBLISH_COS:-no}" >>$QCI_ENV_FILE
echo "PUBLISH_NPM=${PUBLISH_NPM:-no}" >>$QCI_ENV_FILE

# $(git rev-parse HEAD)
tag=$(git tag --contains ${QCI_REPO_COMMIT})
if [[ $PUBLISH_NPM == "yes" ]] && (echo $tag | grep -q "^npm/${NPM_SCOPE}"); then
  echo "当前提交已发布${tag},请勿重复发布npm"
  echo "NPM_PUBLISH_VER=当前提交已发布${tag},请勿重复发布npm" >>$QCI_ENV_FILE
  exit 2
fi

# TODO: 用semver 检查 $NPM_VERSION和$NPM_TAG是否合法

npm config set registry https://mirrors.tencent.com/npm/
# npm cache clean -f

regex="^web-ui-dist/([^/]*)"
if [[ ${QCI_REPO_TAG} != "None" ]]; then
  if [[ ${QCI_REPO_TAG} =~ $regex ]]; then
    QCI_REPO_BRANCH="${BASH_REMATCH[1]}"
    echo "从${QCI_REPO_TAG}提取后的分支名:${QCI_REPO_BRANCH}"
  else
    echo "不匹配的git tag:${QCI_REPO_TAG},无法解析分支名"
    exit 2
  fi
fi

# 若不填,使用当前分支名作为tag
BASE_PATH=${BASE_PATH:-$QCI_REPO_BRANCH}
# 转小写
BASE_PATH=$(echo "$BASE_PATH" | tr '[:upper:]' '[:lower:]')
# 替换所有特殊符号为-
# BASE_PATH=$(echo "$BASE_PATH" | tr -s '[:punct:]' '-')
# 仅替换/
BASE_PATH=$(echo "$BASE_PATH" | tr -s '/' '-')

export TCIC_WEB_BASE_PATH="${BASE_PATH}"

echo "TCIC_WEB_BASE_PATH=${BASE_PATH}" >>$QCI_ENV_FILE

# 现网latest时自动发npm, 以同步离线包
if [[ -z "${ENV_PREFIX}" ]] && [[ "${TCIC_WEB_BASE_PATH}" == "latest" ]]; then
  export PUBLISH_PROD=yes
  export PUBLISH_NPM=yes
  export NPM_SCOPE=both
fi
echo "PUBLISH_PROD=${PUBLISH_PROD:-no}" >>$QCI_ENV_FILE

# 发布现网且标签为latest时, 同时标记hot-hupdate标签
# if [[ -z "${ENV_PREFIX}" ]] && [[ ${NPM_TAG} == "latest" ]]; then
#   export EXTRA_NPM_TAG=${EXTRA_NPM_TAG:-"hot-update"}
# fi

if [[ $FROM_ARTIFACT == "yes" ]]; then
  # TODO: 指定TCIC_WEB_BASE_PATH为latest
  cd $QCI_WORKSPACE/web/ui
  # 下载备份并解压.
  file=dist-${QCI_REPO_COMMIT}.zip
  wget -P ./ https://mirrors.tencent.com/repository/generic/frankshao_public/lcic/${ENV_PREFIX}web-ui/${TCIC_WEB_BASE_PATH}/$file
  unzip ./$file
  # exit 0

else
  # lint检查
  cd $QCI_WORKSPACE/web/sdk

  # with --force, or --legacy-peer-deps
  # npm install --verbose
  npm install --no-package-lock --legacy-peer-deps

  npm run eslint-nofix

  # lint检查
  cd $QCI_WORKSPACE/web/ui

  # --legacy-peer-deps
  # npm install --verbose
  npm install --no-package-lock

  npm run eslint-nofix

  # 与后面git tag, npm版本号复用
  datever=$(date +'%y%m%d.%H%M.%S')

  # 编译 tcic-sdk
  cd $QCI_WORKSPACE/web/sdk
  SDK_NPM_VERSION=$(node -p "require('./package.json').version.split('-')[0]")-${TCIC_WEB_BASE_PATH}.$datever
  echo SDK版本号:$SDK_NPM_VERSION
  npm version $SDK_NPM_VERSION --no-git-tag-version
  if [[ -z "${ENV_PREFIX}" ]]; then
    npm run prod
  else
    npm run test
  fi

  # 编译 tcic-ui
  cd $QCI_WORKSPACE/web/ui

  npm run build
  # 生成旧版离线包 (上传到COS时才需要)
  if [[ $PUBLISH_COS == "yes" ]]; then
    echo 生成旧版web离线包...
    cd $QCI_WORKSPACE/web/ui/dist
    # dist/cache
    mkdir cache
    zipfile=./cache/offline_${QCI_REPO_COMMIT}.zip
    zip -r $zipfile ./
    md5sum $zipfile | cut -d" " -f1 >md5.txt
    # 拷到cache目录,与zip文件同级..
    cp -f ../cache/tiwcache.json ./cache

    ls -al cache/

  fi

  # 如果是现网, 备份到generic制品库并打git tag
  if [[ -z $ENV_PREFIX ]]; then
    cd $QCI_WORKSPACE/web/ui
    # 打包dist
    bak=dist-${QCI_REPO_COMMIT}.zip
    zip -r ./$bak ./dist
    # 上传到generic制品库
    curl -iv --request PUT -u frankshao:${GENERIC_TOKEN} --url http://mirrors.tencent.com/repository/generic/frankshao_public/lcic/${ENV_PREFIX}web-ui/${TCIC_WEB_BASE_PATH}/$bak --upload-file $bak
    # 打web发布tag
    # 保持npm 版本的一致性

    export NPM_VERSION=$(node -p "require('./package.json').version.split('-')[0]")-${TCIC_WEB_BASE_PATH}.$datever
    git_tag=web-ui-dist/${TCIC_WEB_BASE_PATH}/$datever
    git tag -a $git_tag -m "CI TRIGGER:${QCI_TRIGGER}\n"
    git push origin $git_tag
  fi
fi

function publish_npm() {
  if [[ $NPM_SCOPE == "both" ]]; then
    git status
    git add .
    # 不可提交.. 否则push tag时会被push
    # git commit -m "ci temp."

    export NPM_SCOPE="@tencent/"
    . $QCI_WORKSPACE/.ci/publish-npm.sh $1

    # git reset --hard
    git checkout -- .

    export NPM_SCOPE="npm-official"
    . $QCI_WORKSPACE/.ci/publish-npm.sh $1
  else
    . $QCI_WORKSPACE/.ci/publish-npm.sh $1
  fi
}

# 发布npm
cd $QCI_WORKSPACE/web/ui

if [[ $PUBLISH_NPM == "yes" ]]; then
  # 替换package.json中的basePath
  sed -i "s|#basePath-placeholder#|${TCIC_WEB_BASE_PATH}|g" ./package.json

  # 去掉package.json中不必要的字段
  yum install -y jq
  npm run jq

  publish_npm tcic-web-ui

fi

# 看下生成结果.
echo 构建完成.
ls -al $QCI_WORKSPACE/web/ui/dist
