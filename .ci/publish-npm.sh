set -ex

if [[ -z "$NPM_SCOPE" ]]; then
  echo $NPM_SCOPE为空!
  exit 1
fi

pkg=$1
# 使用分支名作为tag
NPM_TAG=$QCI_REPO_BRANCH
# 转小写
NPM_TAG=$(echo "$NPM_TAG" | tr '[:upper:]' '[:lower:]')
# 替换/为-  TAG不允许/
# NPM_TAG=$(echo "$NPM_TAG" | tr -s '[:punct:]' '-')
NPM_TAG=$(echo "$NPM_TAG" | tr -s '/' '-')

# 1.7.3这种不允许作为tag, 替换为1_7_3
NPM_TAG=$(echo "$NPM_TAG" | tr -s '.' '_')

if [[ $NPM_SCOPE == "npm-official" ]]; then
  # 替换package name去掉@tencent
  sed -i "s|@tencent/${pkg}|${pkg}|g" ./package.json

  # npm config delete registry
  # npm access public
  # npm --no-git-tag-version version [<newversion> | major | minor | patch | premajor | preminor | prepatch | prerelease | from-git]

  echo '# ---ci---' >>.npmrc
  echo registry=https://registry.npmjs.org >>.npmrc
  echo //registry.npmjs.org/:_authToken=${NPM_TOKEN} >>.npmrc
  echo '# ---ci---' >>.npmrc
  yum install -y oathtool

  otp="--otp=$(oathtool --totp -b ${MFA_KEY})"
else
  echo '# ---ci---' >>.npmrc
  echo @tencent:registry=https://mirrors.tencent.com/npm/ >>.npmrc
  echo "email=$<EMAIL>" >>.npmrc
  echo "//mirrors.tencent.com/npm/:_authToken=$(echo -n $TNPM_USERNAME:$TNPM_PASSWORD | base64)" >>.npmrc
  echo '# ---ci---' >>.npmrc
fi

PREID=''
# 根据TAG生成版本号
if [[ -z "$NPM_VERSION" ]]; then
  # 设置package.json中的版本号 + 当前日期作为preid
  export NPM_VERSION=$(node -p "require('./package.json').version.split('-')[0]")-${NPM_TAG}.$(date +'%y%m%d.%H%M.%S')
  # 始终以NPM_TAG做为preid 预发布名  prerelease
  # PREID="--preid $NPM_TAG"
  # 获取NPM_TAG(TCIC_WEB_BASE_PATH)的当前版本
  # TAG_VERSION=$(npm dist-tag ls | grep ^${NPM_TAG} | awk -F ': ' '{print $2}')
  # TODO: ~~如果尚未发布此NPM_TAG, 以latest的版本号为基础, 并以当前tag作为prerelease id~~
  # TODO: version这里有问题.. 应始终以当前分支名做筛选后+1, 而非上次+1
  # TODO: 通知变量不显示问题. 默认置空
  # TAG对应的版本不存在, 用当前package.json的版本
  # ver=$(node -p "require('./package.json').version")
  # if [[ -z "$TAG_VERSION" ]]; then
  #   # TAG_VERSION=$(npm dist-tag ls | grep ^latest | awk -F ': ' '{print $2}')
  #   TAG_VERSION=$ver
  # fi

  # if [[ -z "$TAG_VERSION" ]]; then
  #   echo npm上不存在此tag
  #   exit 1
  # fi

  # if [[ "$ver" != "$TAG_VERSION" ]]; then
  #   # 写入获取到TAG对应的版本号 到 package.json,
  #   # TODO: 此--preid not working
  #   npm version $TAG_VERSION --no-git-tag-version $PREID
  # fi
  # +1 prerelease
  # npm version ${NPM_VERSION_INC:-prerelease} --no-git-tag-version $PREID
else
  # 强制指定npm版本号
  echo $NPM_VERSION
fi

npm version $NPM_VERSION --no-git-tag-version $PREID

NPM_PKG=$(node -p "require('./package.json').name")
NPM_VERSION=$(node -p "require('./package.json').version")

git_tag="npm/${NPM_PKG}/@${NPM_TAG}/${NPM_VERSION}"
echo 发布版本: $git_tag
cat ./package.json

force=''
if [[ $NPM_FORCE_VERSION == 'yes' ]]; then
  force=' --force'
fi

echo 发布npm by: $(npm whoami)
npm publish $otp $force --tag $NPM_TAG

echo "NPM_PUBLISH_VER=${NPM_PKG}@${NPM_VERSION}" >>$QCI_ENV_FILE

# 供流水线通知使用
export NPM_RESULT=$(npm view ${NPM_PKG}"@"${NPM_VERSION})
echo NPM_RESULT=$NPM_RESULT

if [[ $NPM_SCOPE == "npm-official" ]]; then
  echo "NPM_RESULT_PUBLIC=${NPM_RESULT}" >>$QCI_ENV_FILE
else
  echo "NPM_RESULT_PRIVATE=${NPM_RESULT}" >>$QCI_ENV_FILE
fi

if [[ -n $EXTRA_NPM_TAG ]]; then
  npm $otp dist-tag add ${NPM_PKG}"@"${NPM_VERSION} $EXTRA_NPM_TAG
fi

# 打git tag
# npm dist-tag add "${NPM_PKG}"@"${NPM_VERSION}" $NPM_TAG
git tag -a $git_tag -m "CI TRIGGER:${QCI_TRIGGER}\nNPM_TAG:${NPM_TAG}"
git push origin $git_tag
