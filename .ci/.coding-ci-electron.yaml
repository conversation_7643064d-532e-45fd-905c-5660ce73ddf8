version: 2
env:
  NPM_SCOPE:
    value: ''
    type: select
    # require: true
    desc: 选择发布到@tencent/内网包还是外网,npm-official发布到官方npm仓库
    option: '@tencent/;both;npm-official'
  EXTRA_NPM_TAG:
    hide: true
    value: ''
    type: string
    desc: 额外的npm tag

  NPM_TAG:
    hide: true
    value: ''
    type: string
    # require: true
    desc: 'npm dist-tag ls'

  # NPM_VERSION_INC:
  #   value: prerelease
  #   type: select
  #   require: true
  #   desc: '要增加的npm版本号
  #     格式: major.minor.patch-<preid>-prerelease(x.x.x)
  #     '
  #   option: prerelease;patch;minor
  NPM_VERSION:
    hide: true
    value: ''
    type: string
    desc: 要发布的npm版本号,格式(x.y.z-aa.b.c...),不填则按照当前选择tag的版本自动+1(prerelease/patch/minor)

  # NPM_RESULT:
  #   value: ''
  #   type: string
  # NPM_FORCE_VERSION:
  #   value: ''
  #   type: select
  #   desc: npm中存在对应版本时是否强制发布版本号
  #   option: yes;no

  MFA_KEY:
    hide: true
    type: string
    require: true
    desc: NPM发布KEY
    readonly: true
    secret: X1X1cqqtnNyu0LgM4FqvsbB238pCrT45dPE7IBi3xbi1nVAysiiV8memwHttt08OgdDw3kr8mrZDUD1Dn9W1KIcOZOjN4arLXaHtP7VL3biZ1HV8+vtD4+sFCvqAi5s54rXhdH9bkNocCpfOtc0sE9ok18dV3KbDfRrpqP3lCrc=
  NPM_USERNAME:
    hide: true
    value: xkazer
    type: string
    require: true
    desc: NPM_USERNAME
    readonly: true
  NPM_EMAIL:
    hide: true
    value: <EMAIL>
    type: string
    require: true
    desc: NPM发布鉴权邮箱
    readonly: true
  NPM_TOKEN:
    hide: true
    type: string
    require: true
    desc: npm token
    readonly: true
    secret: risUQDEmEfSdsY5MDN0r6dKD+F1xtP4QmGzMHonNdyu4v2UiD4QEZpTLUkQw40km7QGfSjAdUg6ax/2gJ654SgcqT65JmkYynpcLhvJL0PhM1/JHgBQL8KWL9ssn5kgfEv/MvuQyntP1tiuSTUC93RMtfqK/RCi13DidDOgYm4g=
  TNPM_PASSWORD:
    hide: true
    type: string
    require: true
    desc: tnpm token
    readonly: true
    secret: So1noVTJg8UmhyVz/1TOsa+yxkIU4t9uffKLsmVxbATEItDXOHY4C6goxM0WSv+kK10K2Ep8z+4TAPiB4VRpaKWB6ry50BwcwsIbs9JgQdEGsQnQfy4Y4PU65LiKhin//uEgLFbKE/x2vEVN/MAT4zt7F+aJQxKPcm8re1t+hTg=
  TNPM_USERNAME:
    hide: true
    value: frankshao
    type: string
    require: true
    desc: tnpm用户名
    readonly: true

stages:
  - stage: 构建并发布NPM
    tasks:
      - task: 新任务
        cmds:
          - plugin: cmds
            params:
              cmds:
                - sh .ci/build-electron-sdk-npm.sh
                - echo end.
            label: build-electron-sdk-npm.sh
worker:
  language: node_js-16
  label: JOB_MATRIX_DEVCLOUD
  tools: []
code_checkout_type: CHECKOUT_ONLY_BRANCH
