#include <jni.h>
#include <android/log.h>
#include <string>


extern "C"
JNIEXPORT jstring JNICALL
Java_com_tencent_tcic_tcic000O00000o0O0o_tcic000O0000Oo01(JNIEnv *env, jclass thiz) {
    std::string hello = "asIeDZ45hTljOka6eWA84";
    return env->NewStringUTF(hello.c_str());
}

extern "C"
JNIEXPORT jstring JNICALL
Java_com_tencent_tcic_tcic000O00000o0O0o_tcic000O00000oo1(JNIEnv *env, jclass thiz) {
    std::string hello = "vAad4Oszx1D2YKiZ";
    return env->NewStringUTF(hello.c_str());
}

extern "C"
JNIEXPORT jstring JNICALL
Java_com_tencent_tcic_tcic000O00000o0O0o_tcic000O0000OoO1(JNIEnv *env, jclass thiz) {
    std::string hello = "b+LCZg3X8rDh6r/tL3iJSzJ";
    return env->NewStringUTF(hello.c_str());
}

extern "C"
JNIEXPORT jstring JNICALL
Java_com_tencent_tcic_tcic000O00000o0O0o_tcic000O0000O0o1(JNIEnv *env, jclass thiz) {
    std::string hello = "spodBNcXFigGwOZUWw8NJhnv";
    return env->NewStringUTF(hello.c_str());
}

extern "C"
JNIEXPORT jstring JNICALL
Java_com_tencent_tcic_tcic000O00000o0O0o_tcic000O00000oO1(JNIEnv *env, jclass thiz) {
    std::string hello = "hnBBAHDvwupCW7B5ILP9acE=";
    return env->NewStringUTF(hello.c_str());
}