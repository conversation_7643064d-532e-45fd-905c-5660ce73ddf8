<template>
  <!-- eslint-disable max-len vue/no-use-v-if-with-v-for vue/no-lone-template-->
  <div
    id="app"
    :class="{ mobile: platformInfo.isMobile }"
    @click="clickNumClick"
  >
    <!-- pc/pad/mobile  start /-->
    <div class="main">
      <div class="intro-wrap">
        <div :class="['intro-pic', { 'intro-pic-mobile': platformInfo.isMobile }]" />
      </div>
      <div class="setting-wrap">
        <div
          id="setting"
          class="setting scroll init-hide"
          style="display: none;"
        >
          <!-- 首页-->
          <div
            v-show="showLogin"
            class="common-section switch full flex-column"
          >
            <div class="config">
              <!-- 暂时不做设备检测  -->
              <!-- <div class="config-item">
                <a class="config-btn" href="javascript:;" @click="gotoPage('detection')">
                  <span class="icon icon-detection" />
                  <span class="text">{{$t('设备检测')}}</span>
                </a>
              </div> -->
              <div
                class="config-item dropdown"
                :class="{ active: showLanguageSelect }"
              >
                <a
                  class="config-btn dropdown-btn"
                  href="javascript:;"
                  @click="showLanguageSelect=!showLanguageSelect"
                >
                  <span class="icon icon-lang" />
                  <span class="text">{{ currentLngItem.label }}</span>
                  <i class="icon-dropdown" />
                </a>
                <div class="dropdown-content">
                  <span
                    v-for="lngItem in languageList"
                    :key="lngItem.value"
                    :class="{ active: currentLngItem.value === lngItem.value }"
                    @click="switchLanguage(lngItem)"
                  >{{ lngItem.label }}</span>
                </div>
              </div>
            </div>
            <div class="brand">
              <div class="brand-logo">
                <div class="logo-wrap">
                  <div class="logo-icon" />
                  <div
                    class="logo-text"
                    :style="currentLngItem.logoTextStyle"
                  >
                    <span v-if="typeof currentLngItem.logoText === 'string' ">{{ currentLngItem.logoText }}</span>
                    <template v-else>
                      <span>{{ currentLngItem.logoText[0] }}</span>
                      <span>{{ currentLngItem.logoText[1] }}</span>
                    </template>
                  </div>
                </div>
              </div>
              <p class="brand-text version">
                {{ $t('TCIC 版本') }}: {{ currentVersion }}
              </p>
              <!-- 当前环境，注意不要用 v-show，要占位置，以免切换环境时 brand 其他元素跳动-->
              <p
                class="brand-text env"
                :style="`visibility: ${envRadio && envRadio !== 'prod' ? 'visible' : 'hidden'}`"
              >
                {{ $t('课堂环境') }}: {{ envRadio }}
              </p>
            </div>
            <div
              v-show="needRoomList"
              class="config"
            >
              <div
                class="config-item dropdown"
              >
                <a
                  class="config-btn dropdown-btn"
                  target="_blank"
                  href="https://class.qcloudclass.com/latest/network.html"
                >
                  <span class="icon icon-detection" />
                  <span class="text">{{ $t('网络检测') }}</span>
                </a>
              </div>
            </div>
            <div
              v-show="needRoomList"
              class="room-list-wrap scroll"
            >
              <ul
                class="room-list"
                :data-stat="roomListShowData.statinfo"
              >
                <li
                  v-for="room in roomListShowData.list"
                  :key="room.roomId"
                  class="room-item"
                  :data-sort-value="room.sortValue"
                  :data-room-id="room.RoomId"
                >
                  <div class="info">
                    <div class="info-name">
                      {{ room.Name }}
                    </div>
                    <div class="info-time small">
                      {{ getFormatRoomTime({ StartTime: room.RealStartTime || room.StartTime, EndTime: room.RealEndTime || room.EndTime }) }}
                    </div>
                    <div class="info-other small">
                      <span class="other-item status">
                        <span :class="`icon icon-room-status status-${room.Status}`" />
                        <span :class="`txt status-${room.Status}`">{{ $t((loginPageConfig.login.roomList.roomStatus.options[room.Status] || {}).label) || `Status ${room.Status}` }}</span>
                      </span>
                      <span class="other-item">
                        <span class="icon icon-room-max-mic" />
                        <!-- 创建房间时代表最大连麦数的参数是MaxMicNumber，但在后端返回的数据中MaxRTCMember代表最大连麦数 -->
                        <span class="txt">{{ $t((loginPageConfig.login.roomList.roomMaxMicNumber.options[room.MaxRTCMember] || {}).label) || `${room.MaxRTCMember}` }}</span>
                      </span>
                    </div>
                  </div>
                  <div class="op">
                    <div
                      v-show="room.Status !== 2 && room.Status !== 3"
                      :class="`btn status-${room.Status}`"
                      @click="enterRoomFromRoomList(room)"
                    >
                      <span class="btn-txt">{{ $t('加入课堂') }}</span>
                      <span class="btn-line" />
                      <span
                        class="btn-icon icon-copy"
                        @click.stop="copyAllInfo(room)"
                      />
                    </div>
                    <div
                      v-show="room.Status === 2 || room.Status === 3"
                      :class="`btn status-${room.Status} ${room.RecordUrl ? '' : 'disabled'}`"
                    >
                      <a
                        v-if="room.RecordUrl"
                        class="btn-full"
                        :href="room.RecordUrl"
                        target="_blank"
                      >
                        <span class="btn-txt">{{ $t('查看回放') }}</span>
                      </a>
                      <span
                        v-else
                        class="btn-txt"
                      >{{ $t('查看回放') }}</span>
                    </div>
                  </div>
                </li>
              </ul>
              <div
                v-show="!roomListShowData.list.length && roomListShowData.message"
                class="room-list-empty"
                @click="onClickEmptyRoomList()"
              >
                {{ roomListShowData.message }}
              </div>
            </div>
            <div
              v-if="platformInfo.isMobile && needRoomList"
              class="ai-room-create-btn"
            >
              <div
                class="login-button primary ai-create"
                @click="createAiRoom"
              >
                <span class="btn-icon icon-create-ai" />
                <span class="btn-txt">{{ $t('体验AI课堂') }}</span>
              </div>
            </div>
            <div
              v-show="needRoomList"
              :class="`login-button-group small`"
            >
              <div
                :class="['login-button', {'primary': !platformInfo.isMobile}]"
                @click="gotoPage('create')"
              >
                <span class="btn-icon icon-create" />
                <span class="btn-txt">{{ $t('创建课堂') }}</span>
              </div>
              <div
                class="login-button"
                @click="gotoPage('enter')"
              >
                <span class="btn-icon icon-enter" />
                <span class="btn-txt">{{ $t('进入课堂') }}</span>
              </div>
            </div>
            <div
              v-show="!needRoomList"
              class="describtion-text"
              v-html="$t(descriptionText)"
            />
          </div>

          <!-- PC版详情 -->
          <div
            v-show="!showLogin"
            class="pc-detail full flex-column"
          >
            <div class="tab-head">
              <a
                class="svg-icon back"
                @click="gotoPage('login')"
              >
                <span class="icon-back" />
              </a>
              <div class="title">
                {{ currentPageTitle }}
              </div>
              <div
                v-show="envRadio && envRadio !== 'prod'"
                class="env-dot"
              />
            </div>
            <!-- 设备检测 -->
            <div
              v-show="showDetection"
              class="pc-section scroll detection"
            >
              <div class="tab-body flex-column">
                <div class="pc-form-wrap scroll" />
                <div class="pc-button-wrap">
                  <div
                    class="main-button"
                    @click="gotoPage('login')"
                  >
                    {{ $t('完成') }}
                  </div>
                </div>
              </div>
            </div>
            <!-- 创建课堂房间 -->
            <div
              v-show="showCreateRoom"
              class="pc-section scroll create"
            >
              <!-- 创建 -->
              <div
                v-show="!createSuccess"
                class="tab-body flex-column"
              >
                <div class="pc-form-wrap scroll">
                  <!-- 课堂信息 -->
                  <!-- <p class="input-type">
                    {{ $t('课堂信息') }}
                  </p> -->
                  <div
                    v-for="(item, index) in loginPageConfig.create.roomInfo.formConfig"
                    v-show="isItemVisible(item)"
                    :key="index"
                  >
                    <div class="input-item">
                      <div class="label">
                        <span>{{ $t(item.label) }}</span><span
                          v-show="item.required"
                          style="color:red;margin-left:10px;"
                        >*</span>
                      </div>
                      <div class="input-area">
                        <input
                          v-if="item.type==='text'"
                          v-model="idInputs[item.name]"
                          type="text"
                          class="input-control"
                          :maxlength="item.maxlength"
                          :placeholder="$t(item.message)"
                          @dblclick="selectValue($event)"
                        >
                        <div
                          v-if="item.type==='text'"
                          v-show="item.randomNum"
                          class="random"
                          @click="randomNum(item.randomNumPrefix)"
                        >
                          {{ $t('随机') }}
                        </div>
                      </div>
                    </div>
                    <div
                      v-if="item.remindEmpty"
                      class="err-remind"
                    >
                      <span v-if="!idInputs[item.name]">{{ $t(item.message) }}</span>
                    </div>
                  </div>
                  <!-- 课堂设置 -->
                  <!-- <p class="input-type">
                    {{ $t('课堂设置') }}
                  </p> -->
                  <div class="dialog-tab-page-foot">
                    <div
                      v-for="(item, index) in loginPageConfig.create.roomSetting.formConfigMain"
                      v-show="isItemVisible(item)"
                      :key="index"
                      class="input-item"
                    >
                      <div class="label">
                        <span>{{ $t(item.label) }}</span><span
                          v-show="item.required"
                          style="color:red;margin-left:10px;"
                        >*</span>
                      </div>
                      <div
                        class="input-area"
                        :style="item.areaStyle"
                      >
                        <el-date-picker
                          v-if="item.type==='datetimePicker'"
                          v-model="roomConfig[item.name]"
                          type="datetime"
                          size="small"
                          :editable="false"
                          :placeholder="$t(item.message)"
                          prefix-icon="el-icon-date"
                          format="yyyy-MM-dd HH:mm"
                        />
                        <el-select
                          v-if="item.type==='selector'"
                          v-model="roomConfig[item.name]"
                          size="small"
                          :placeholder="$t(item.message)"
                          :disabled="!isItemEnable(item)"
                        >
                          <el-option
                            v-for="option in item.options"
                            v-show="isItemOptionVisible(item, option)"
                            :key="option.label"
                            :label="$t(option.label)"
                            :value="option.value"
                            :disabled="!isItemOptionEnable(item, option)"
                          />
                        </el-select>
                        <el-radio-group
                          v-if="item.type==='radioGroup'"
                          v-model="roomConfig[item.name]"
                          :disabled="!isItemEnable(item)"
                        >
                          <div
                            v-if="item.name ==='RoomType'"
                          >
                            <div

                              v-for="option in item.options"
                              :key="option.value"
                              :class="`room-wrapper ${roomConfig[item.name] === option.value ? 'active' : ''}`"
                            >
                              <el-radio
                                v-show="isItemOptionVisible(item, option)"
                                :label="option.value"
                                :value="option.value"
                                :disabled="!isItemOptionEnable(item, option)"
                              >
                                {{ $t(option.label) }}
                                <div class="room-desc">
                                  {{ $t(option.desc) }}
                                </div>
                              </el-radio>
                            </div>
                          </div>
                          <span
                            v-else
                          >
                            <el-radio
                              v-for="option in item.options"
                              v-show="isItemOptionVisible(item, option)"
                              :key="`e_${option.value}`"
                              :label="option.value"
                              :value="option.value"
                              :disabled="!isItemOptionEnable(item, option)"
                            >
                              {{ $t(option.label) }}
                            </el-radio>
                          </span>
                        </el-radio-group>
                        <el-switch
                          v-if="item.type==='switch'"
                          v-model="roomConfig[item.name]"
                          size="small"
                          active-color="#006cff"
                          inactive-color="#959595"
                          :active-text="getSwitchText(item, true)"
                          :active-value="getSwitchValue(item, true)"
                          :inactive-value="getSwitchValue(item, false)"
                          :disabled="!isItemEnable(item)"
                        />
                      </div>
                    </div>
                    <div
                      v-show="!showMoreDetail"
                      class="more-info"
                      @click="toggleDetail"
                    >
                      {{ $t("展开更多课堂配置") }} <img src="./assets/img/icon-down.svg">
                    </div>
                    <div
                      v-show="showMoreDetail"
                      class="detail-wrap"
                    >
                      <div
                        v-for="(item, index) in loginPageConfig.create.roomSetting.formConfigDetail"
                        v-show="isItemVisible(item)"
                        :key="`detail_${index}`"
                        class="input-item"
                      >
                        <div class="label">
                          <span>{{ $t(item.label) }}</span><span
                            v-show="item.required"
                            style="color:red;margin-left:10px;"
                          >*</span>
                        </div>
                        <div class="input-area">
                          <el-date-picker
                            v-if="item.type==='datetimePicker'"
                            v-model="roomConfig[item.name]"
                            type="datetime"
                            size="small"
                            :editable="false"
                            :placeholder="$t(item.message)"
                            prefix-icon="el-icon-date"
                            format="yyyy-MM-dd HH:mm"
                          />
                          <el-select
                            v-if="item.type==='selector'"
                            v-model="roomConfig[item.name]"
                            size="small"
                            :placeholder="$t(item.message)"
                            :disabled="!isItemEnable(item)"
                          >
                            <el-option
                              v-for="option in item.options"
                              v-show="isItemOptionVisible(item, option)"
                              :key="option.label"
                              :label="$t(option.label)"
                              :value="option.value"
                              :disabled="!isItemOptionEnable(item, option)"
                            />
                          </el-select>
                          <el-radio-group
                            v-if="item.type==='radioGroup'"
                            v-model="roomConfig[item.name]"
                            :disabled="!isItemEnable(item)"
                          >
                            <el-radio
                              v-for="option in item.options"
                              v-show="isItemOptionVisible(item, option)"
                              :key="option.value"
                              :label="option.value"
                              :value="option.value"
                              :disabled="!isItemOptionEnable(item, option)"
                            >
                              {{ $t(option.label) }}
                            </el-radio>
                          </el-radio-group>
                          <el-switch
                            v-if="item.type==='switch'"
                            v-model="roomConfig[item.name]"
                            size="small"
                            active-color="#006cff"
                            inactive-color="#959595"
                            :active-text="getSwitchText(item, true)"
                            :active-value="getSwitchValue(item, true)"
                            :inactive-value="getSwitchValue(item, false)"
                            :disabled="!isItemEnable(item)"
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 创建按钮 -->
                <div class="pc-button-wrap">
                  <div
                    class="main-button"
                    @click="createRoom()"
                  >
                    {{ $t('创建课堂') }}
                  </div>
                </div>
              </div>
              <!-- 创建成功 -->
              <div
                v-show="createSuccess"
                class="tab-body tab-success flex-column"
              >
                <div class="pc-form-wrap flex-column">
                  <div class="create-success">
                    <span class="icon-success" />
                    <span class="text-success">{{ $t('课堂创建成功') }}</span>
                  </div>
                </div>
                <div class="room-info">
                  <div class="room-info-container">
                    <p class="name">
                      {{ idInputs.className }}
                    </p>
                    <div class="straight-line" />
                    <div class="read-only-form">
                      <div class="time">
                        <span class="title">
                          <!-- 上课时间 -->
                          {{ $t('上课时间') }}
                        </span>
                        <span class="content">
                          {{ getFormatRoomTime() }}
                        </span>
                      </div>
                      <div class="room-id">
                        <span class="title">
                          <!-- 课堂ID -->
                          {{ $t('课堂ID') }}
                        </span>
                        <span class="content">
                          {{ splitNumber(createdRoomInfo.RoomId) }}
                          <span
                            class="icon-copy"
                            :title="$t('复制')"
                            @click="copyRoomId(createdRoomInfo.RoomId)"
                          />
                        </span>
                      </div>
                      <div class="time">
                        <span class="title">
                          <!-- 课堂地址 -->
                          {{ $t('课堂地址') }}
                        </span>
                        <span class="content class-url">
                          <span>{{ shareUrl }}</span>
                          <span
                            class="icon-copy"
                            :title="$t('复制')"
                            @click="copyClassUrl(shareUrl)"
                          />
                        </span>
                      </div>
                    </div>
                    <div class="straight-line bottom" />
                    <div class="copy-all-info">
                      <span
                        class="copy-all-button"
                        @click="copyAllInfo()"
                      >
                        <!-- 复制全部信息 -->
                        {{ $t('全部复制') }}
                      </span>
                    </div>
                  </div>
                </div>

                <div class="pc-button-wrap">
                  <div
                    class="main-button"
                    @click="onJoinClass(true)"
                  >
                    {{ $t('立即进入') }}
                  </div>
                  <div class="other-button-wrap">
                    <a
                      class="no-enter"
                      href="javascript:;"
                      @click="gotoPage('login')"
                    >
                      {{ $t('暂不进入') }}
                    </a>
                    <!-- <a class="no-enter" href="javascript:;" @click="createSuccess=false" >{{$t('重新创建')}}</a> -->
                  </div>
                </div>
              </div>
            </div>
            <!-- 进入课堂房间 -->
            <div
              v-show="showEnterRoom"
              class="pc-section scroll enter"
            >
              <div class="tab-body flex-column">
                <div class="pc-form-wrap scroll">
                  <!-- 课堂信息 -->
                  <p class="input-type">
                    {{ $t('课堂信息') }}
                  </p>
                  <div
                    v-for="(item, index) in loginPageConfig.enter.roomInfo.formConfig"
                    v-show="isItemVisible(item)"
                    :key="index"
                    :data-name="item.name"
                  >
                    <div class="input-item">
                      <div class="label">
                        <span>{{ $t(item.label) }}</span><span
                          v-show="item.required"
                          style="color:red;margin-left:10px;"
                        >*</span>
                      </div>
                      <div class="input-area">
                        <input
                          v-if="item.type==='number'"
                          v-model="idInputs[item.name]"
                          type="number"
                          class="input-control numberInput"
                          style="ime-mode: disabled"
                          pattern="[0-9]*"
                          oninput="value=value.replace(/[^\d]/g,'')"
                          :maxlength="item.maxlength"
                          :placeholder="$t(item.message)"
                          @dblclick="selectValue($event)"
                        >
                        <input
                          v-if="item.type==='text'"
                          v-model="idInputs[item.name]"
                          type="text"
                          class="input-control"
                          :maxlength="item.maxlength"
                          :placeholder="$t(item.message)"
                          @dblclick="selectValue($event)"
                        >
                        <div
                          v-if="item.type==='text'"
                          v-show="item.randomNum"
                          class="random"
                          @click="randomNum(item.randomNumPrefix)"
                        >
                          {{ $t('随机') }}
                        </div>
                        <el-radio-group
                          v-if="item.type==='radioGroup'"
                          v-model="roomConfig[item.name]"
                          size="small"
                        >
                          <el-radio
                            v-for="option in item.options"
                            v-show="isItemOptionVisible(item, option)"
                            :key="option.value"
                            :label="option.value"
                            :value="option.value"
                            :disabled="!isItemOptionEnable(item, option)"
                          >
                            {{ $t(option.label) }}
                          </el-radio>
                        </el-radio-group>
                      </div>
                    </div>
                    <div
                      v-if="item.remindEmpty"
                      class="err-remind"
                    >
                      <span v-if="!idInputs[item.name]">{{ $t(item.message) }}</span>
                    </div>
                  </div>
                  <!-- 设备设置 -->
                  <p class="input-type">
                    {{ $t('设备设置') }}
                  </p>
                  <div
                    v-for="(item, index) in loginPageConfig.enter.deviceSetting.formConfig"
                    v-show="isItemVisible(item)"
                    :key="index"
                    :data-name="item.name"
                  >
                    <div class="input-item">
                      <div class="label">
                        <span>{{ $t(item.label) }}</span><span
                          v-show="item.required"
                          style="color:red;margin-left:10px;"
                        >*</span>
                      </div>
                      <div class="input-area">
                        <el-switch
                          v-if="item.type==='switch'"
                          v-model="roomConfig[item.name]"
                          size="small"
                          active-color="#006cff"
                          inactive-color="#959595"
                          :active-text="getSwitchText(item, true)"
                          :active-value="getSwitchValue(item, true)"
                          :inactive-value="getSwitchValue(item, false)"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 进入按钮 -->
                <div class="pc-button-wrap">
                  <div
                    class="main-button"
                    @click="onJoinClass(true)"
                  >
                    {{ $t('立即进入') }}
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 手机竖屏版详情 -->
          <div
            v-show="!showLogin"
            class="phone-detail"
          >
            <div class="detail">
              <div class="phone-detail-head">
                <a
                  class="svg-icon back"
                  href="javascript:;"
                  @click="gotoPage('login')"
                >
                  <span class="icon-back-phone" />
                </a>
                <div class="title">
                  {{ currentPageTitle }}
                </div>
                <div
                  v-show="envRadio && envRadio !== 'prod'"
                  class="env-dot"
                />
              </div>
              <!-- 设备检测 -->
              <div
                v-if="showDetection"
                class="phone-card phone-detection"
              >
                <!-- TODO  -->
              </div>
              <!-- 创建课堂房间 -->
              <div v-if="showCreateRoom">
                <!-- 创建 -->
                <div v-show="!createSuccess">
                  <div :class="['phone-card class-info class-create-info', { 'wide-text': isWideText }]">
                    <div
                      v-for="(item, index) in phoneConfig.create.roomInfo.formConfig"
                      :key="index"
                      class="dialog-item dialog-input"
                    >
                      <div class="label">
                        <span>{{ $t(item.label) }}</span>
                      </div>
                      <el-input
                        v-model="idInputs[item.name]"
                        class="role-info-value"
                        :placeholder="$t(item.message)"
                      />
                      <div
                        v-if="item.randomNum"
                        class="random phone-detail-select"
                        @click="randomNum(item.randomNumPrefix)"
                      >
                        {{ $t('随机') }}
                      </div>
                    </div>
                  </div>
                  <div class="phone-card class-basic-setting">
                    <div
                      v-for="(item,index) in phoneConfig.create.basicSetting.formConfig"
                      :key="index"
                      class="dialog-item dialog-input"
                      @click="onSwitchSelector(item)"
                    >
                      <div class="label">
                        <span>{{ $t(item.label) }}</span>
                      </div>
                      <span
                        v-if="item.type === 'selector' || item.type === 'radioGroup'"
                        class="info-value"
                      >{{ getOptionLabel(item) }}</span>
                      <span
                        v-if="item.type === 'selector' || item.type === 'radioGroup'"
                        class="phone-detail-select icon-right-arrow"
                      />
                      <el-date-picker
                        v-if="item.name === 'PickerTime'"
                        v-model="roomConfig.PickerTime"
                        type="datetime"
                        :editable="false"
                        :style="{'width': '76%'}"
                        :placeholder="$t('开始时间')"
                        prefix-icon="el-datetime-icon"
                        clear-icon="el-datetime-clear-icon"
                        format="yyyy-MM-dd HH:mm"
                        align="center"
                      />
                    </div>
                  </div>
                  <div class="phone-card class-super-setting">
                    <div
                      v-for="(item,index) in phoneConfig.create.superSetting.formConfig"
                      :key="index"
                      class="dialog-item dialog-input"
                      @click="onSwitchSelector(item)"
                    >
                      <div class="label">
                        <span>{{ $t(item.label) }}</span>
                      </div>
                      <span class="info-value">{{ $t(item.options[roomConfig[item.name]].label) }}</span>
                      <span
                        v-if="item.type === 'selector'"
                        class="phone-detail-select icon-right-arrow"
                      />
                    </div>
                  </div>
                </div>
                <!-- 创建成功 -->
                <div v-show="createSuccess">
                  <div class="create-success">
                    <span class="icon-success" />
                    <span class="text-success">{{ $t('课堂创建成功') }}</span>
                  </div>
                </div>
              </div>
              <!-- 进入课堂房间 -->
              <div v-if="showEnterRoom">
                <div :class="['phone-card class-info class-enter-info', { 'wide-text': isWideText }]">
                  <div class="dialog-item dialog-input">
                    <div class="label">
                      <span>{{ $t(phoneConfig.enter.roomInfo.roomId.label) }}</span>
                    </div>
                    <el-input
                      v-model="idInputs.classId"
                      class="role-info-value"
                      :placeholder="$t(phoneConfig.enter.roomInfo.roomId.message)"
                      @dblclick="selectValue($event)"
                    />
                  </div>
                  <div
                    class="dialog-item dialog-input"
                    @click="onSwitchSelector(phoneConfig.enter.roomInfo.roleType)"
                  >
                    <div class="label">
                      <span>{{ $t(phoneConfig.enter.roomInfo.roleType.label) }}</span>
                    </div>
                    <span class="role-info-value">{{ $t(phoneConfig.enter.roomInfo.roleType.options[roomConfig.RoleType].label) }}</span>
                    <span class="phone-detail-select icon-right-arrow" />
                  </div>
                  <div
                    v-show="roomConfig.RoleType === 'student' || roomConfig.RoleType === 'supervisor'"
                    class="dialog-item dialog-input"
                  >
                    <div class="label">
                      <span>{{ $t(phoneConfig.enter.roomInfo.roleName.label) }}</span>
                    </div>
                    <el-input
                      v-model="idInputs[roleNameField]"
                      class="role-info-value"
                      :placeholder="$t(phoneConfig.enter.roomInfo.roleName.message)"
                    />
                  </div>
                </div>
                <div class="phone-card class-basic-setting">
                  <div
                    v-for="(item,index) in phoneConfig.enter.basicSetting.formConfig"
                    :key="index"
                    class="dialog-item dialog-input"
                    @click="onSwitchSelector(item, item.type)"
                  >
                    <div class="label">
                      <span>{{ $t(item.label) }}</span>
                    </div>
                    <span
                      v-if="item.type !== 'switch' && item.name !== 'PickerTime'"
                      class="info-value"
                    >{{ roomConfig[item.name] }}</span>
                    <!-- 为什么不直接用 el-switch ？？？-->
                    <span
                      v-if="item.type === 'switch'"
                      :class="'phone-detail-select icon-switch ' + (roomConfig[item.name] ? 'active' : '')"
                    />
                  </div>
                </div>
              </div>
              <div v-if="showEnterAiRoom">
                <div :class="['phone-card class-info class-enter-info', { 'wide-text': isWideText }]">
                  <div class="dialog-item dialog-input">
                    <div class="label">
                      <span>{{ $t(phoneConfig.enterAi.roomInfo.roomId.label) }}</span>
                    </div>
                    <el-input
                      v-model="idInputs.classId"
                      class="role-info-value"
                      :placeholder="$t(phoneConfig.enterAi.roomInfo.roomId.message)"
                      @dblclick="selectValue($event)"
                    />
                  </div>
                  <div
                    class="dialog-item dialog-input"
                  >
                    <div class="label">
                      <span>{{ $t(phoneConfig.enterAi.roomInfo.roleName.label) }}</span>
                    </div>
                    <el-input
                      v-model="idInputs[roleNameField]"
                      class="role-info-value"
                      :placeholder="$t(phoneConfig.enterAi.roomInfo.roleName.message)"
                    />
                  </div>
                </div>
                <div class="phone-card class-basic-setting">
                  <div
                    v-for="(item,index) in phoneConfig.enterAi.basicSetting.formConfig"
                    :key="index"
                    class="dialog-item dialog-input"
                    @click="onSwitchSelector(item, item.type)"
                  >
                    <div class="label">
                      <span>{{ $t(item.label) }}</span>
                    </div>
                    <span
                      v-if="item.type !== 'switch' && item.name !== 'PickerTime'"
                      class="info-value"
                    >{{ roomConfig[item.name] }}</span>
                    <!-- 为什么不直接用 el-switch ？？？-->
                    <span
                      v-if="item.type === 'switch'"
                      :class="'phone-detail-select icon-switch ' + (roomConfig[item.name] ? 'active' : '')"
                    />
                  </div>
                </div>
              </div>
              <div
                id="dialog-footer"
                class="active1"
              >
                <div class="phone-demo-class">
                  <label
                    class="label"
                    style="width: 100px;"
                  >{{ $t('课堂环境') }}</label>
                  <el-radio-group v-model="envRadio">
                    <el-radio label="prod">
                      {{ $t('正式环境') }}
                    </el-radio>
                    <el-radio label="test">
                      {{ $t('测试环境') }}
                    </el-radio>
                  </el-radio-group>
                </div>
                <div class="phone-demo-class">
                  <label
                    class="label"
                    style="width: 100px;"
                  >SdkAppId</label>
                  <el-input
                    v-model="sdkAppId"
                    :placeholder="$t('请输入SdkAppId')"
                  />
                </div>
                <div class="phone-demo-class">
                  <label
                    class="label"
                    style="width: 100px;"
                  >{{ $t('Uin列表') }}</label>
                  <template>
                    <el-select
                      v-model="uin"
                      class="select"
                    >
                      <el-option
                        v-for="(item,index) in uinList"
                        :key="index+'1'"
                        :label="item"
                        :value="item"
                      />
                    </el-select>
                  </template>
                </div>
              </div>
            </div>
            <!-- 手机 selector -->
            <div
              class="phone-detail-mask"
              :style="currentSelectorLayerItem ? 'display: block;' : 'display: none;'"
              @click="onSwitchSelector([])"
            />
            <div
              class="phone-detail-selector"
              :style="currentSelectorLayerItem ? 'display: block;' : 'display: none;'"
            >
              <div
                v-for="item in currentSelectorLayerOptions"
                :key="item.value"
                class="phone-detail-li"
                @click="onClickSelectorOption(item.value)"
              >
                {{ $t(item.label) }}
              </div>
              <div
                class="phone-detail-li selector-cancel"
                @click="onSwitchSelector([])"
              >
                {{ $t('取消') }}
              </div>
            </div>
          </div>
          <!-- 环境配置信息 class="active1" -->
          <div
            class="more-env"
            :class="{ active: showEnv }"
          >
            <div class="tab-body">
              <div
                class="input-type"
                @click="resetEnv"
              >
                {{ $t('更多配置') }}
              </div>
              <template v-if="showCreateRoom && !createSuccess">
                <div class="input-item">
                  <div class="label">
                    {{ $t(roomFormConfig.interactionMode.label) }}
                  </div>
                  <div class="input-area">
                    <el-radio-group v-model="roomConfig[roomFormConfig.interactionMode.name]">
                      <el-radio
                        v-for="option in roomFormConfig.interactionMode.options"
                        :key="option.value"
                        :label="option.value"
                      >
                        {{ $t(option.label) }}
                      </el-radio>
                    </el-radio-group>
                  </div>
                </div>
                <div class="input-item">
                  <div class="label">
                    {{ $t(roomFormConfig.enableRecord.label) }}:
                  </div>
                  <div class="input-area">
                    <el-switch
                      v-model="roomConfig[roomFormConfig.enableRecord.name]"
                      size="small"
                      active-color="#006cff"
                      inactive-color="#959595"
                      :active-text="getSwitchText(roomFormConfig.enableRecord, true)"
                      :active-value="getSwitchValue(roomFormConfig.enableRecord, true)"
                      :inactive-value="getSwitchValue(roomFormConfig.enableRecord, false)"
                    />
                  </div>
                </div>
                <div class="input-item">
                  <div class="label">
                    {{ $t(roomFormConfig.customRecordLayout.label) }}
                  </div>
                  <div class="input-area">
                    <el-input
                      v-model="roomConfig[roomFormConfig.customRecordLayout.name]"
                      size="small"
                    />
                  </div>
                </div>
              </template>
              <div class="input-item">
                <div class="label">
                  {{ $t('课堂环境') }}
                </div>
                <div class="input-area">
                  <el-radio-group
                    v-model="envRadio"
                    @change="getDevelopers"
                  >
                    <el-radio label="prod">
                      {{ $t('正式环境') }}
                    </el-radio>
                    <el-radio label="test">
                      {{ $t('测试环境') }}
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
              <div class="input-item">
                <div class="label">
                  {{ $t('Uin列表') }}
                </div>
                <div class="input-area">
                  <el-select
                    v-model="uin"
                    size="small"
                    class="select"
                  >
                    <el-option
                      v-for="(item,index) in uinList"
                      :key="index+'1'"
                      :label="item"
                      :value="item"
                    />
                  </el-select>
                </div>
              </div>
              <div class="input-item">
                <div class="label">
                  SdkAppId
                </div>
                <div class="input-area">
                  <el-input
                    v-model="sdkAppId"
                    size="small"
                    :placeholder="$t('请输入SdkAppId')"
                  />
                  <div class="op-wrap">
                    <div
                      class="op-item"
                      @click="changePackage('ultimate')"
                    >
                      {{ $t('旗舰版') }}
                    </div>
                    <div
                      class="op-item"
                      @click="changePackage('light')"
                    >
                      {{ $t('轻量版') }}
                    </div>
                  </div>
                </div>
              </div>
              <div class="input-item">
                <div class="label">
                  {{ $t('课堂URL') }}
                </div>
                <div class="input-area">
                  <div class="single-line-text">
                    {{ prefix }}
                  </div>
                </div>
              </div>
              <div class="input-item">
                <div class="label">
                  BuildVersion
                </div>
                <div class="input-area">
                  {{ buildVersion }}
                </div>
              </div>
              <div class="input-item">
                <div class="label">
                  {{ $t('推流方式') }}
                </div>
                <div class="input-area">
                  <el-radio-group
                    v-model="streamingMode"
                  >
                    <el-radio label="TRTC">
                      TRTC
                    </el-radio>
                    <el-radio label="RTMP">
                      RTMP
                    </el-radio>
                  </el-radio-group>
                </div>
              </div>
              <div class="input-item">
                <div class="label">
                  isDebugMode
                </div>
                <div class="input-area">
                  <el-switch
                    v-model="isDebugMode"
                    size="small"
                    active-color="#006cff"
                    inactive-color="#959595"
                  />
                </div>
              </div>
            </div>
          </div>
          <!-- 手机竖屏幕按钮 -->
          <div
            v-show="!showLogin"
            class="phone-detail-footer"
          >
            <div class="phone-detail-button-list">
              <div
                v-show="showCreateRoom && !createSuccess"
                class="phone-demo-button"
                @click="createRoom()"
              >
                <span class="btn-text">{{ $t('创建课堂') }}</span>
              </div>
              <div
                v-show="showEnterRoom || showEnterAiRoom"
                class="phone-demo-button"
                @click="onJoinClass(true)"
              >
                <span class="btn-text">{{ $t('立即进入') }}</span>
              </div>
            </div>
          </div>
        </div>
        <!-- setting end -->
      </div>
      <!-- setting-wrap end -->

      <!-- PC版右下角按钮 -->
      <div
        class="pc-area pc-link-list init-hide"
        style="display: none;"
      >
        <div
          v-for="(link, index) in loginPageConfig.links"
          v-if="link[lngRadio]"
          :key="index"
          :class="'pc-link-item ' + link.type"
        >
          <!-- TODO 适配各端 -->
          <!-- <a href="javascript:;" @click="openUrl(link[lngRadio].url)">{{link[lngRadio].text}}</a> -->
          <a
            :href="link[lngRadio].url"
            target="_blank"
          >{{ link[lngRadio].text }}</a>
        </div>
      </div>
    <!-- pc/pad/mobile end -->
    </div>
    <div class="intro-footer">
      <span>{{ $t('15分钟快速上线自有品牌的互动教学平台') }} - </span><a
        :href="`${currentLngItem.value === 'zh'?'https://console.cloud.tencent.com/lcic':'https://www.tencentcloud.com/account/login?s_url=https%3A%2F%2Fconsole.tencentcloud.com%2Flcic'}`"
        target="_blank"
      >{{ $t('免费试用') }}&gt;</a><strong>/</strong><a
        :href="`${currentLngItem.value === 'zh'?'https://cloud.tencent.com/document/product/1639/79894':'https://www.tencentcloud.com/document/product/1168/53681'}`"
        target="_blank"
      ><img src="./assets/img/footer1.svg">{{ $t('集成指南') }}</a><strong>/</strong><a class="group-qr"><img src="./assets/img/footer2.svg"> {{ $t('企微咨询') }}
        <div class="qr-img-wrapper">
          <img src="./assets/img/groupqr.png">
          <p>{{ $t('欢迎加入') }}<strong>{{ $t('实时互动-教育版') }}</strong></p>
          <p>{{ $t('技术服务交流群') }}</p>
        </div>

      </a>
    </div>
    <QrcodeDialog v-if="isShowFeedbackQr" />
  </div>
</template>

<script>
import loginConfig from './config';
import moment from 'moment';
import i18next from 'i18next';
import QrcodeDialog from '../../component/vue-component/header-component/sub-component/FeedbackQrDialog.vue';
import './assets/css/login.css';

const SDK_APPID_MAP = {
  prod: {
    ultimate: 3923193,
    light: 3454016,
  },
  test: {
    ultimate: 3520371,
    light: 3223994,
  },
};

const PREFIX_URL = (function () {
  const currentUrl = window.location.href;
  return currentUrl.substring(0, currentUrl.lastIndexOf('/'));
}());

const ELEMENT = window.ELEMENT;

export default {
  data() {
    const { sdkVersion, languageConfig, innerLayoutConfig, roomFormConfig, loginPageConfig } = loginConfig;
    const languageList = Object.values(languageConfig);

    console.log('init location.search', location.search);
    const urlParams = new URLSearchParams(location.search);
    const nativeVersion = urlParams.get('nativeversion') || '';

    const env = localStorage.getItem('envRadio') ? localStorage.getItem('envRadio') : 'prod';

    return {
      // created 时更新 platformInfo
      platformInfo: {
        isMobile: undefined,
        isWeb: undefined,
      },
      isShowFeedbackQr: false,
      descriptionText: '支持 <span>音视频连麦、互动白板、屏幕共享、云端录制和直播</span> 等功能。老师和学生可以在学习软件上预约课程，并通过该学习工具（实时互动教育版）进入课堂进行学习。',
      // isZh:
      isMobileLandscape: false, // 是否为手机横屏[未启用]
      showLanguageSelect: false, // 是否展示语言选择下拉框
      languageConfig,
      languageList,
      innerLayoutConfig,
      roomFormConfig,
      loginPageConfig,
      phoneConfig: loginPageConfig.phoneConfig,
      // eslint-disable-next-line
      buildVersion: window.__TCIC_UI_BUILD_VERSION,
      currentVersion: nativeVersion ? `${sdkVersion}@native-${nativeVersion}` : sdkVersion,
      // lngRadio: 'zh', // 移到 computed
      currentLngItem: languageList[0],
      currentPage: 'login', // login-选择，create-创建课堂，enter-进入课堂，detection-设备检测

      idInputs: {
        // initFormValues 里设默认值
        classId: '',
        className: '',
        teacherName: '',
        assistantName: '',
        studentName: '',
        trainingTeacherName: '',
      },

      createSuccess: false,
      createdRoomInfo: {
        // 创建成功后的room
        Uin: '',
        SdkAppId: 0,
        RoomId: '',
      },
      // 分享地址
      classUrl: '',

      // ---- 更多配置 start ----
      clickNum: 0,
      lastClickTime: new Date().getTime(),
      showEnv: env !== 'prod',
      envRadio: env,
      sdkAppId: this.getDefaultAppId(env),
      uin: '',
      uinList: [],
      prefix: PREFIX_URL,
      streamingMode: 'TRTC',
      isDebugMode: false,
      // ---- 更多配置 end ----

      // ---- 手机端selector浮层 start ----
      currentSelectorLayerItem: null,
      currentSelectorLayerOptions: [],
      // ---- 手机端selector浮层 end ----

      // ---- 房间列表 start ----
      // needRoomList: parseInt(urlParams.get('roomlist'), 10) > 0, // 外网后台有问题，但是封网了暂时不能发，前端加个开关。。。
      needRoomList: urlParams.get('mode', 'demo') !== 'standard', // 前端有过滤，还是可以发
      canGetRooms: false, // uin/sdkAppId 变化时更新
      roomListShowData: {
        list: [],
        message: '',
        statinfo: '', // dom属性，不会显示出来
      },
      roomListData: {
        params: {},
        state: '',
        page: 0,
        total: NaN,
        list: [],
        map: {},
        stat: {},
      },
      // ---- 房间列表 end ----

      roomConfig: {
        // create 参数说明见 https://cloud.tencent.com/document/product/1639/80942
        // roomConfig.PickerTime/roomConfig.Period 变化自动更新 roomConfig.StartTime/roomConfig.EndTime
        PickerTime: '',
        Period: 30,
        StartTime: 0,
        EndTime: 0,

        // roomConfig.RoomType 变化自动更新 roomConfig.MaxMicNumber/roomConfig.MaxMicNumberEnable/roomConfig.MaxMicNumberVisibleOptions
        RoomType: 0, // 班型，0-小班课，1-大班课
        // AudienceType: 1, // 观看类型，创建房间时计算，不用放在data里

        // roomConfig.MaxMicNumber 变化自动更新 roomConfig.ResolutionEnableOptions/roomConfig.AutoMic
        MaxMicNumber: 6, // 最大连麦人数
        MaxMicNumberEnable: true, // MaxMicNumber 是否可用
        MaxMicNumberVisibleOptions: {
          0: true,
          1: true,
          6: true,
          12: true,
          16: true,
        },

        // roomConfig.InnerLayout 变化自动更新 roomConfig.SubType/roomConfig.VideoOrientation，详见 innerLayoutConfig 配置
        InnerLayout: 'videodoc', // 教室布局，内部用
        InnerLayoutVisibleOptions: {
          videodoc: true,
          video: true,
          videoPortrait: true,
        },
        SubType: 'videodoc', // 教室布局
        VideoOrientation: 0, // 视频方向，0-横屏，1-竖屏
        // RecordLayout: 3, // 录制布局，创建房间时计算，不用放在data里

        AutoMic: 1, // 是否允许学生自动上麦
        AutoMicEnable: true, // AutoMic 是否可用

        EnableDirectControl: 0,
        AudioQuality: 0, // 音质，0-标准音质，1-高音质
        Resolution: 2, // 分辨率，1-标准，2-高清，3-全高清
        ResolutionEnableOptions: {
          1: true,
          2: true,
          3: true,
        },

        // enter
        RoleType: 'student', // 默认进入课堂时的角色类型
        RoleTypeVisibleOptions: {
          teacher: true,
          assistant: true,
          student: true,
          supervisor: false, // 判断是web后设为true
        },
        Speaker: 1,
        Mic: 1,
        Camera: 1,

        // others
        EnableRecord: 1, // 创建房间时转成 DisableRecord
        CustomRecordLayout: '', // 创建房间时用于计算 RecordLayout
        IsGradingRequiredPostClass: 0, // 是否开启课后评分
        InteractionMode: 0, // 开启专注模式。0 收看全部角色音视频(默认) 1 只看老师和助教
      },
      showMoreDetail: false, // 展开课中详情配置
      networkTestLabel: 'Network Test',
    };
  },
  components: {
    QrcodeDialog,
  },
  computed: {
    lngRadio() {
      return this.currentLngItem.value;
    },
    shareUrl() {
      return `${PREFIX_URL}/login.html?page=enter&classId=${this.createdRoomInfo.RoomId}`;
    },
    isWideText() {
      return !!this.currentLngItem.isWideText;
    },
    roleNameField() {
      let field = 'studentName';
      switch (this.roomConfig.RoleType) {
        case 'teacher': field = 'teacherName'; break;
        case 'assistant': field = 'assistantName'; break;
        case 'student': field = 'studentName'; break;
        case 'supervisor': field = 'trainingTeacherName'; break;
      }
      return field;
    },
    currentPageTitle() {
      return i18next.t(this.loginPageConfig[this.currentPage].title);
    },
    showLogin() {
      return this.currentPage === 'login';
    },
    showDetection() {
      return this.currentPage === 'detection';
    },
    showCreateRoom() {
      return this.currentPage === 'create';
    },
    showEnterRoom() {
      return this.currentPage === 'enter';
    },
    showEnterAiRoom() {
      return this.currentPage === 'enterAi';
    },
  },
  watch: {
    'roomConfig.PickerTime'(newVal, oldVal) {
      if (newVal === oldVal) {
        return;
      }
      const startMoment = moment(newVal);
      const endMoment = moment(newVal).add(this.roomConfig.Period, 'minutes');
      this.roomConfig.StartTime = startMoment.unix();
      this.roomConfig.EndTime = endMoment.unix();
    },
    'roomConfig.Period'(newVal, oldVal) {
      if (newVal === oldVal) {
        return;
      }
      // console.log(`roomConfig.Period changed, ${typeof newVal} ${newVal}`);
      const startMoment = moment(this.roomConfig.PickerTime);
      const endMoment = moment(this.roomConfig.PickerTime).add(newVal, 'minutes');
      this.roomConfig.StartTime = startMoment.unix();
      this.roomConfig.EndTime = endMoment.unix();
    },
    'roomConfig.RoomType'(newVal, oldVal) {
      if (newVal === 1) {
        // 大班课
        this.roomConfig.MaxMicNumber = 0;
        this.roomConfig.MaxMicNumberEnable = true;
        // 大班课仅支持 1v0 1v1
        this.roomConfig.MaxMicNumberVisibleOptions = {
          0: true,
          1: true,
        };
        // 大班课不支持手机竖屏开播
        this.roomConfig.InnerLayoutVisibleOptions = {
          videodoc: true,
          video: true,
          videoPortrait: false,
          videodocPortrait: true,
        };
        if (this.roomConfig.InnerLayout === 'videoPortrait') {
          this.roomConfig.InnerLayout = 'video';
        }
      } else {
        // 小班课
        this.roomConfig.MaxMicNumber = 6;
        this.roomConfig.MaxMicNumberEnable = true;
        this.roomConfig.MaxMicNumberVisibleOptions = {
          0: true,
          1: true,
          6: true,
          12: true,
          16: true,
        };
        this.roomConfig.InnerLayoutVisibleOptions = {
          videodoc: true,
          video: true,
          videoPortrait: true,
          videodocPortrait: false,
        };
      }
    },
    'roomConfig.MaxMicNumber'(newVal, oldVal) {
      const maxMicNumber = parseInt(newVal, 10);

      // 大班课，或者最大上台人数为0时，不能自动连麦
      if (this.roomConfig.RoomType === 1 || maxMicNumber === 0) {
        this.roomConfig.AutoMic = 0;
        this.roomConfig.AutoMicEnable = false;
      } else {
        this.roomConfig.AutoMic = 1;
        this.roomConfig.AutoMicEnable = true;
      }

      // 更新可选分辨率
      const validResolutions = {};
      let newResolution;
      if (maxMicNumber === 0) {
        validResolutions['2'] = true;
        newResolution = 2;
      } else if (maxMicNumber >= 1 && maxMicNumber <= 6) {
        validResolutions['1'] = true;
        validResolutions['2'] = true;
        validResolutions['3'] = true;
      } else if (maxMicNumber >= 7) {
        validResolutions['1'] = true;
        newResolution = 1;
      }
      // console.log(`roomConfig.MaxMicNumber changed, ${typeof newVal} ${newVal}, ResolutionEnableOptions`, validResolutions, 'newResolution', newResolution);
      this.roomConfig.ResolutionEnableOptions = validResolutions;
      if (newResolution && newResolution !== this.roomConfig.Resolution) {
        this.roomConfig.Resolution = newResolution;
      }
    },
    'roomConfig.InnerLayout'(newVal, oldVal) {
      const newData = this.innerLayoutConfig[newVal] || this.innerLayoutConfig.default;
      Object.assign(this.roomConfig, newData);
      console.log('roomConfig.InnerLayout changed', newVal, newData);
    },
    'roomConfig.RoleType'(newVal, oldVal) {
      // console.log('roomConfig.RoleType changed', newVal);
    },
    envRadio(newVal, oldVal) {
      localStorage.setItem('envRadio', newVal);
      this.getDevelopers();
      this.sdkAppId = this.getDefaultAppId(newVal);
    },
    uin(newVal, oldVal) {
      const SdkAppId = parseInt(this.sdkAppId, 10);
      const canGetRooms = this.needRoomList && newVal && SdkAppId >= 1000000 && SdkAppId < 10000000;
      this.canGetRooms = canGetRooms;
      if (canGetRooms) {
        this.getRooms();
      }
    },
    sdkAppId(newVal, oldVal) {
      const SdkAppId = parseInt(newVal, 10);
      const canGetRooms = this.needRoomList && this.uin && SdkAppId >= 1000000 && SdkAppId < 10000000;
      this.canGetRooms = canGetRooms;
      if (canGetRooms) {
        this.getRooms();
      }
    },
    canGetRooms(newVal, oldVal) {
      console.log('canGetRooms changed', newVal);
      if (newVal) {
        this.getRooms();
      } else {
        this.resetRoomListState();
      }
    },
  },
  created() {
    this.platformInfo = {
      isMobile: this.isMobile(),
      isWeb: this.isWeb(),
    };
  },
  mounted() {
    this.initFormValues();
    this.initLanguage();
    this.initPage();
    this.getDevelopers();
    this.listenPageEvents();
    console.log('page inited');
    const hideDoms = document.getElementsByClassName('init-hide');
    if (hideDoms) {
      for (const dom of hideDoms) {
        dom.style = 'display: block';
      }
    }
    if (location.search.indexOf('feedback') !== -1) {
      this.isShowFeedbackQr = true;
    }
  },
  methods: {
    toggleDetail() {
      console.log('showMore Detail toggle:', this.showMoreDetail);
      this.showMoreDetail = !this.showMoreDetail;
    },
    showToast(params) {
      this.$message({
        offset: 30,
        ...params,
      });
    },
    getShareUrl(roomId) {
      return `${PREFIX_URL}/login.html?page=enter&classId=${roomId}`;
    },
    isTeacherRole(roleType) {
      return roleType === 'teacher' || roleType === 'assistant';
    },
    initPage() {
      // pc 效果
      const url = location.href;
      const urlParams = new URLSearchParams(location.search);
      // url 中带有的classId优先级更高
      if (urlParams.get('classId')) {
        this.idInputs.classId = urlParams.get('classId');
        this.randomNum('student');
      }
      if (/create/i.test(url)) {
        this.gotoPage('create');
      } else if (/enter/i.test(url)) {
        this.gotoPage('enter');
      } else {
        this.gotoPage('login');
      }
    },
    isItemVisible(item) {
      if (!item.visibleName) {
        // 不限制
        return true;
      }
      return item.visibleValue !== undefined
        ? this.roomConfig[item.visibleName] === item.visibleValue
        : this.roomConfig[item.visibleName];
    },
    isItemEnable(item) {
      if (!item.enableName) {
        // 不限制
        return true;
      }
      return item.enableValue !== undefined
        ? this.roomConfig[item.enableName] === item.enableValue
        : this.roomConfig[item.enableName];
    },
    isItemOptionVisible(item, option) {
      if (!item.optionsVisibleName) {
        // 不限制
        return true;
      }
      return !!this.roomConfig[item.optionsVisibleName][option.value];
    },
    isItemOptionEnable(item, option) {
      if (!item.optionsEnableName) {
        // 不限制
        return true;
      }
      return !!this.roomConfig[item.optionsEnableName][option.value];
    },
    getSwitchValue(item, isActive) {
      const obj = item.options ? item.options[isActive ? 'active' : 'inactive'] : null;
      return obj ? obj.value : !!isActive;
    },
    getSwitchText(item, isActive) {
      const obj = item.options ? item.options[isActive ? 'active' : 'inactive'] : null;
      return obj && obj.label ? i18next.t(obj.label) : '';
    },
    getOptionLabel(item) {
      const obj = item.options ? item.options[this.roomConfig[item.name]] : this.roomConfig[item.name];
      return i18next.t(obj.label);
    },
    onSwitchSelector(item) {
      if (!this.isItemEnable(item)) {
        return;
      }

      if (item && (item.type === 'selector' || item.type === 'radioGroup') && item.options) {
        this.currentSelectorLayerItem = item;
        console.log('select', item);
        const filteredOptions = Object.values(item.options)
          .filter(option => !item.optionsVisibleName || this.roomConfig[item.optionsVisibleName][option.value]) // 过滤不可见的
          .filter(option => !item.optionsEnableName || this.roomConfig[item.optionsEnableName][option.value]); // 过滤禁用的
        this.currentSelectorLayerOptions = filteredOptions;
      } else {
        this.currentSelectorLayerItem = null;
        this.currentSelectorLayerOptions = [];
      }

      if (item && item.type === 'switch') {
        // TODO 改用 el-switch
        const oldVal = this.roomConfig[item.name];
        let newVal;
        if (typeof oldVal === 'number') {
          newVal = oldVal ? 0 : 1;
        } else {
          newVal = !oldVal;
        }
        console.log('onClickSwitch', item.name, newVal);
        this.roomConfig[item.name] = newVal;
      }
    },
    onClickSelectorOption(value) {
      if (!this.currentSelectorLayerItem) {
        return;
      }
      console.log('onClickSelectorOption', this.currentSelectorLayerItem.name, value);
      this.roomConfig[this.currentSelectorLayerItem.name] = value;
      this.onSwitchSelector([]);
    },
    selectValue(e) {
      e && e.currentTarget.select();
    },
    parseLanguage(lang) {
      let res = 'en';
      if (/zh(-\w+)?/g.test(lang)) {
        res = lang === 'zh-TW' ? 'zh-TW' : 'zh';
      } else {
        if (/ko(-\w+)?/g.test(lang)) {
          res = 'ko';
        } else if (/ja(-\w+)?/g.test(lang)) {
          res = 'ja';
        } else {
          res = 'en';
        }
      }
      return res;
    },
    initLanguage() {
      const params = new URLSearchParams(location.search);
      const lng = this.parseLanguage(params.get('lng') || window.navigator.language);
      console.log('initLanguage', lng);
      this.switchLanguage(this.languageConfig[lng]);
    },
    initFormValues() {
      // 各种 input
      const idInputsStr = localStorage.getItem('idInput');
      const randomNum = parseInt(Math.random() * 10000, 10).toString()
        .padEnd(4, '0');
      const inputValues = {
        classId: '',
        className: i18next.t('实时互动-教育版'),
        teacherName: `teacher${randomNum}`,
        assistantName: `assistant${randomNum}`,
        studentName: `student${randomNum}`,
        trainingTeacherName: `trainingTeacher${randomNum}`,
      };
      let localValues = {};
      if (idInputsStr) {
        try {
          localValues = JSON.parse(idInputsStr);
        } catch (err) {
          console.warn('parse localValues error', err);
        };
      }
      // 不能直接覆盖，空字符串时要用默认值
      // eslint-disable-next-line no-restricted-syntax
      for (const key in inputValues) {
        const value = localValues[key];
        const valueType = typeof value;
        // classId 可能存了 number 类型，兼容
        if (value && (valueType === 'string' || valueType === 'number')) {
          inputValues[key] = String(value);
        }
      }
      this.idInputs = inputValues;
      localStorage.setItem('idInput', JSON.stringify(this.idInputs));

      // 开始时间默认15min后，PickerTime 变化会自动修改 roomConfig.StartTime/roomConfig.EndTime
      const startMoment = moment().add(15, 'minutes');
      this.roomConfig.PickerTime = startMoment.utc();

      // web才有巡课
      this.roomConfig.RoleTypeVisibleOptions.supervisor = this.isWeb();

      // 从缓存中获取环境匹配 sdkAppId
      const envRadio = localStorage.getItem('envRadio');
      if (envRadio) {
        this.sdkAppId = this.getDefaultAppId(envRadio);
      }
    },
    randomNum(val) {
      const randomNum = parseInt(Math.random() * 10000, 10).toString()
        .padEnd(4, '0');
      const fieldName = `${val}Name`;
      if (fieldName in this.idInputs) {
        this.idInputs[fieldName] = `${val}${randomNum}`;
      }
      localStorage.setItem('idInput', JSON.stringify(this.idInputs));
    },
    createAiRoom() {
      this.idInputs.className = 'AI 课堂';
      this.createRoom(true);
    },
    gotoPage(val) {
      // console.log('gotoPage', val);
      if (val === 'create') {
        this.showMoreDetail = false;
      }
      if (this.createSuccess) {
        this.createSuccess = false;
      }
      this.currentPage = val;
      if (this.canGetRooms) {
        if (this.currentPage === 'login') {
          this.getRooms();
          this.startRoomListTimer();
        } else {
          this.stopRoomListTimer();
        }
      }
      // 每次进入创建页时，更新日历时间
      if (this.currentPage === 'create') {
        const startMoment = moment().add(15, 'minutes');
        this.roomConfig.PickerTime = startMoment.utc();
      }
    },
    getFormatRoomTime(params) {
      const roomInfo = params || this.roomConfig;
      if (!roomInfo.StartTime || !roomInfo.EndTime) {
        return '';
      }
      return `${moment(roomInfo.StartTime * 1000).format('YYYY-MM-DD HH:mm')} - ${moment(roomInfo.EndTime * 1000).format('HH:mm')}`;
    },
    isMobile() {
      const userAgent = navigator.userAgent;
      if (/Android|iPhone|iPad|iPod|ios|pad|tablet|Mobile/i.test(userAgent)) {
        return true;
      }
      if (/Macintosh|Mac OS X/i.test(userAgent) || /win32|wow32|win64|wow64|windows/i.test(userAgent)) {
        return false;
      }
      if (/Tablet|tv/i.test(userAgent)) {
        return false;
      }
      return false;
    },
    isWeb() {
      const userAgent = navigator.userAgent;
      if (/Electron/i.test(userAgent)) {
        return false;
      }
      if (/(phone|pad|pod|iPhone|iPod|ios|iPad|Android|Mobile|BlackBerry|IEMobile|MQQBrowser|JUC|Fennec|wOSBrowser|BrowserNG|WebOS|Symbian|Windows Phone)/i.test(userAgent)) {
        // 这么长？先保留，后续考虑简化
        return false;
      }
      return true;
    },
    isWeixin() {
      const userAgent = navigator.userAgent;
      return !!userAgent.match(/MicroMessenger/i);
    },
    listenPageEvents() {
      // window.addEventListener('resize', () => {
      //   // 页面宽高
      // });
      window.addEventListener('orientationchange', function () {
        if (window.orientation === 90 || window.orientation === -90) {
          this.isMobileLandscape = true;
        }
        if (window.orientation === 0 || window.orientation === 180) {
          this.isMobileLandscape = false;
        }
      }, false);
    },
    getFormatDate(date) {
      const d = new Date(date);
      return `${[d.getFullYear(), d.getMonth() + 1, d.getDate()].join('.')} ${[d.getHours(), d.getMinutes()].join(':')}`;
    },
    showLoading(visible) {
      document.getElementById('loading').style.display = visible ? '' : 'none';
    },
    showErrorMsg(msg) {
      document.getElementById('errorMsg').innerText = msg;
    },
    splitNumber(num) {
      return String(num).replace(/(\d)(?=(\d{3})+?$)/g, '$1 ');
    },
    onJoinClass(verifyAiRoom) {
      const reg = /\d{9}/;
      const classId = String(this.idInputs.classId).replace(/\D/g, '');
      console.log('onJoinClass', classId);
      if (!reg.test(classId)) {
        this.showToast({
          message: this.idInputs.classId ? i18next.t(this.roomFormConfig.roomId.formatErrorMessage) : i18next.t(this.roomFormConfig.roomId.message),
          type: 'warning',
        });
        return;
      }
      const roleType = this.roomConfig.RoleType;
      const roleName = this.isTeacherRole(roleType) ? '' : this.idInputs[this.roleNameField];
      if (roleType === 'student' && !this.idInputs.studentName) {
        this.showToast({
          message: i18next.t(this.roomFormConfig.studentName.message),
          type: 'warning',
        });
        return;
      }
      if (roleType === 'supervisor' && !this.idInputs.trainingTeacherName) {
        this.showToast({
          message: i18next.t(this.roomFormConfig.trainingTeacherName.message),
          type: 'warning',
        });
        return;
      }
      this.tryJoinClass({ uin: this.uin, classId, roleType, roleName, verifyAiRoom });
    },
    describeRoomInfo(uin, classId) {
      const describeRoomParams = {
        Uin: uin,
        RoomId: parseInt(classId, 10),
      };
      return this.post('/describeRoom', {
        scene: 'default',
        ...describeRoomParams,
      }).then(res => res.Response);
    },
    login(loginParams, scene = 'default') {
      return this.post('/login', {
        scene,
        ...loginParams,
      }).then(res => res.Response);
    },
    tryJoinClass({ uin, classId, roleType, roleName, verifyAiRoom }) {
      console.log('tryJoinClass', uin, classId, roleType, roleName);
      if (!uin || !classId || !roleType) {
        console.error('tryJoinClass params error');
        return;
      }
      const isTeacher = this.isTeacherRole(roleType);
      if (!isTeacher && !roleName) {
        console.error('tryJoinClass params error, roleName empty');
        return;
      }
      // 先拉 roominfo
      return this.describeRoomInfo(uin, classId)
        .then((describeRoomRes) => {
          console.log('describeRoom success', JSON.stringify(describeRoomRes));
          this.showLoading(false);
          const schoolId = describeRoomRes.SdkAppId;
          const videoOrientation = describeRoomRes.VideoOrientation;
          const liveType = describeRoomRes.LiveType;
          if (verifyAiRoom && liveType === 10) {
            this.showToast({
              message: 'AI 课堂不能通过房间号进入',
              type: 'warning',
            });
            return;
          }

          const loginParams = {
            Uin: uin,
            SdkAppId: describeRoomRes.SdkAppId,
          };
          // 带角色类型的进入房间
          switch (roleType) {
            case 'teacher':
              loginParams.Id = describeRoomRes.TeacherId;
              break;
            case 'assistant':
              loginParams.Id = describeRoomRes.Assistants[0];
              break;
            default:
              loginParams.Id = roleName;
              if (roleType === 'supervisor') {
                // 巡课的特殊标记
                loginParams.Role = 1;
              }
              break;
          }
          return this.login(loginParams)
            .then((loginRes) => {
              console.log('login success', loginRes);
              this.showLoading(false);
              this.doJoinClass({ uin, schoolId, classId, roleType, videoOrientation }, loginRes);
            })
            .catch((err) => {
              console.error('login error', err);
              this.showLoading(false);
              this.showToast({
                message: err.Message,
                type: 'warning',
              });
            });
        })
        .catch((err) => {
          console.error('describeRoom error', err);
          this.showLoading(false);
          this.showToast({
            message: err.Message,
            type: 'warning',
          });
        });
    },
    doJoinClass({ uin, schoolId, classId, roleType, init, videoOrientation }, sign) {
      console.log('doJoinClass', uin, schoolId, classId, roleType, this.roomConfig.VideoOrientation);

      const isTeacher = this.isTeacherRole(roleType);
      const urlParams = {
        schoolid: schoolId,
        classid: classId,
        userid: sign.UserId,
        token: sign.Token,
        env: this.envRadio,
        lng: this.lngRadio,
        role: this.roomConfig.RoleType === 'supervisor' ? 'supervisor' : '', // class页改成了有 role=supervisor 才检查是否巡课
        camera: this.roomConfig.Camera,
        mic: this.roomConfig.Mic,
        speaker: this.roomConfig.Speaker,
        defaultDeviceOrientation: this.isWeixin() ? 1 : (videoOrientation || this.roomConfig.VideoOrientation),
      };

      if (this.streamingMode === 'RTMP') {
        urlParams.rtmp = '1';
      }

      if (this.isDebugMode) {
        urlParams.debug = '1';
      }

      // 微信小程序
      // eslint-disable-next-line no-underscore-dangle
      if (window.__wxjs_environment === 'miniprogram' && isTeacher) {
        // 组装必传参数
        const mpDevParams = {
          ...urlParams,
          lng: this.lngRadio === 'zh' ? 'zh-CN' : this.lngRadio, // mp的lng格式不一样？
          back_url: `${PREFIX_URL}/login.html`,
        };
        const arr = [];
        for (const [key, val] of Object.entries(mpDevParams)) {
          arr.push(`${key}=${val}`);
        }
        const url = `/pages/index/interactive-teacher?${arr.join('&')}`;
        window.wx.miniProgram.redirectTo({ url });
        return;
      }

      const env = this.envRadio;
      localStorage.setItem('idInput', JSON.stringify(this.idInputs));
      if (typeof window.joinClassBySign === 'function') {
        // 【TODO】 rm env
        delete urlParams.env;
        // 桌面端适配
        window.joinClassBySign(urlParams);
      } else if (window.loginNative && typeof window.loginNative.joinClassBySign === 'function') {
        // Android端适配
        window.loginNative.joinClass(JSON.stringify(urlParams));
      } else if (typeof window.electronJoinClass === 'function') {
        // 兼容老版本
        window.electronJoinClass(
          schoolId,
          classId,
          sign.UserId,
          sign.Token,
          env,
          this.lngRadio,
        );
      } else {
        const url = this.generateWebUrl(urlParams);
        // console.log('goto url', url);
        if (this.isWeixin() && typeof location.replace === 'function') {
          location.replace(url);
        } else {
          location.href = url;
        }
      }
    },
    generateWebUrl(urlParams) {
      const env = this.envRadio;
      const matches = location.pathname.match(/[0-9]+(.[0-9])+/);
      let version = '';
      if (matches) {
        version = `${matches[0]}/`;
      }
      const webParams = {
        ...urlParams,
        back_url: `${PREFIX_URL}/login.html?feedback`,
      };
      const envMatches = location.host.match(/^(test|dev|preview)-/gi);
      if (envMatches || env !== 'prod') {
        webParams.env = env;
      }
      const arr = [];
      for (const [key, val] of Object.entries(webParams)) {
        arr.push(`${key}=${val}`);
      }
      const url = `${PREFIX_URL}/index.html?${arr.join('&')}`;
      return url;
    },
    openUrl(url) {
      // TODO 适配各端
    },
    copyClassUrl(url) {
      console.log('copyurl', url);
      const { res, removeInput } = this.copyText(url);
      if (res) {
        this.showToast({
          message: i18next.t('复制成功'),
          type: 'success',
        });
      } else {
        this.showToast({
          message: i18next.t('复制失败'),
          type: 'warning',
        });
      }
      removeInput();
    },
    createRoom(isAiRoom) {
      if (!this.idInputs.className) {
        this.showToast({
          message: i18next.t(this.roomFormConfig.roomName.message),
          type: 'warning',
        });
        return;
      }
      // if (!this.idInputs.teacherName) {
      //   this.showToast({
      //     message: i18next.t(this.roomFormConfig.teacherName.message),
      //     type: 'warning',
      //   });
      //   return;
      // }
      // if (!this.idInputs.assistantName) {
      //   this.showToast({
      //     message: i18next.t(this.roomFormConfig.assistantName.message),
      //     type: 'warning',
      //   });
      //   return;
      // }
      if (!this.roomConfig.StartTime) {
        this.showToast({
          message: i18next.t(this.roomFormConfig.startTime.message),
          type: 'warning',
        });
        return;
      }

      // 录制模板，枚举值参考 https://cloud.tencent.com/document/product/1639/89744#dbdb018b-d36f-488b-b830-deb6689c9c64
      let recordLayout = this.roomConfig.SubType === 'video' ? 0 : 3;
      if (this.roomConfig.CustomRecordLayout) {
        // 另外制定了 CustomRecordLayout
        recordLayout = parseInt(this.roomConfig.CustomRecordLayout, 10);
      }

      // 观看类型，1-RTC观看, 2-CDN观看
      const audienceType = 1;
      // if (this.roomConfig.RoomType === 1) {
      //   // 大班课CND流用这个
      //   audienceType = 2;
      // }

      const createParams = {
        Name: this.idInputs.className,
        // TeacherId: this.idInputs.teacherName,
        Assistants: [],
        StartTime: this.roomConfig.StartTime,
        EndTime: this.roomConfig.EndTime,
        RoomType: this.roomConfig.RoomType,
        MaxMicNumber: isAiRoom ? 1 : parseInt(this.roomConfig.MaxMicNumber, 10), // 最大连麦人数，不包括老师，[0, 16]
        // RTCAudienceNumber: parseInt(this.roomConfig.MaxMicNumber, 10) + 1, // rtc互动房间人数需要加上老师
        RTCAudienceNumber: 50, // 不能只加老师，还要包括其他不上台但是要拉rtc流的人，否则超出的用户只能拉快直播流
        SubType: isAiRoom ? 'audioOnly' : this.roomConfig.SubType,
        VideoOrientation: this.roomConfig.VideoOrientation,
        RecordLayout: recordLayout,
        AudienceType: audienceType,
        AutoMic: this.roomConfig.AutoMic,
        EnableDirectControl: this.roomConfig.EnableDirectControl,
        AudioQuality: parseInt(this.roomConfig.AudioQuality, 10),
        Resolution: parseInt(this.roomConfig.Resolution, 10),
        InteractionMode: parseInt(this.roomConfig.InteractionMode, 10),
        IsGradingRequiredPostClass: parseInt(this.roomConfig.IsGradingRequiredPostClass, 10),
        DisableRecord: isAiRoom ? 1 : (this.roomConfig.EnableRecord ? 0 : 1),
        Uin: this.uin,
        SdkAppId: parseInt(this.sdkAppId, 10),
      };
      if (isAiRoom) {
        createParams.LiveType = 10;
        createParams.StartTime = moment().add(0.5, 'minutes')
.unix();
        createParams.EndTime = createParams.StartTime + 3 * 60;
      }

      if (!isAiRoom && this.idInputs.teacherName) {
        createParams.TeacherId = this.idInputs.teacherName;
      }

      if (!isAiRoom && this.idInputs.assistantName) {
        createParams.Assistants = [this.idInputs.assistantName];
      }

      this.post('/createRoom', {
        scene: 'default',
        ...createParams,
      })
        .then((res) => {
          console.log('createRoom success', res);
          this.showLoading(false);
          this.createdRoomInfo = {
            Uin: createParams.Uin,
            SdkAppId: createParams.SdkAppId,
            RoomId: res.Response.RoomId,
          };
          this.idInputs.classId = res.Response.RoomId;
          localStorage.setItem('idInput', JSON.stringify(this.idInputs));
          this.roomConfig.RoleType = isAiRoom ? 'student' : 'teacher';
          this.createSuccess = true;
          // 手机新版默认用创建角色的教师身份进入课堂，移动端小屏幕就直接进入课堂
          if (this.isMobile() && Math.min(screen.width, screen.height) <= 500) {
            this.onJoinClass(!isAiRoom);
          }
        })
        .catch((err) => {
          console.error('createRoom error', err);
          this.showLoading(false);
          this.showToast({
            message: err.Message,
            type: 'warning',
          });
        });
    },
    copyAllInfo(room) {
      let classTips = '';
      let classInfo = {};
      if (room) {
        classInfo = {
          [i18next.t('上课时间')]: this.getFormatRoomTime({ StartTime: room.RealStartTime || room.StartTime, EndTime: room.RealEndTime || room.EndTime }),
          [i18next.t('课堂ID')]: this.splitNumber(room.RoomId),
          [i18next.t('课堂地址')]: this.getShareUrl(room.RoomId),
        };
      } else {
        classInfo = {
          [i18next.t('上课时间')]: this.getFormatRoomTime(),
          [i18next.t('课堂ID')]: this.splitNumber(this.createdRoomInfo.RoomId),
          [i18next.t('课堂地址')]: this.shareUrl,
        };
      }

      Object.keys(classInfo).forEach((key) => {
        classTips += `${key}: ${classInfo[key]}\n`;
      });
      const { res, removeInput } = this.copyText(classTips, 'textarea');
      if (res) {
        this.showToast({
          message: i18next.t('复制成功'),
          type: 'success',
        });
      } else {
        this.showToast({
          message: i18next.t('复制失败'),
          type: 'warning',
        });
      }
      removeInput();
    },
    copyText(value, type = 'input') {
      const input = document.createElement(type);
      input.setAttribute('readonly', 'readonly');
      input.readonly = 'readonly';
      input.value = value;
      document.body.appendChild(input);
      input.select();
      const res = document.execCommand('copy');
      return { res, removeInput: () => document.body.removeChild(input) };
    },
    copyRoomId(roomId) {
      console.log('copyRoomId', roomId);

      this.idInputs.classId = roomId;
      localStorage.setItem('idInput', JSON.stringify(this.idInputs));

      const input = document.createElement('input');
      input.setAttribute('readonly', 'readonly');
      input.setAttribute('value', roomId);
      document.body.appendChild(input);
      input.select();
      const res = document.execCommand('copy');
      if (res) {
        this.showToast({
          message: i18next.t('复制成功'),
          type: 'success',
        });
      } else {
        this.showToast({
          message: i18next.t('复制失败'),
          type: 'warning',
        });
      }
      document.body.removeChild(input);
    },
    async post(url, data) {
      const host = 'https://tcic-demo-api.qcloudclass.com';
      // eslint-disable-next-line no-underscore-dangle
      const _that = this;
      return new Promise((resolve, reject) => {
        const httpReq = new XMLHttpRequest();
        httpReq.open('POST', host + url, true);
        httpReq.setRequestHeader('Content-type', 'application/json');
        httpReq.send(JSON.stringify(Object.assign(data, { env: this.envRadio })));
        httpReq.onreadystatechange = function () {
          if (httpReq.readyState === 4) {
            if (httpReq.status === 200) {
              const rsp = JSON.parse(httpReq.responseText);
              if (rsp.Code) {
                const msg = i18next.t(rsp.Code);
                if (msg && msg !== rsp.Code) {
                  console.log('process server message', rsp.Message, msg);
                  rsp.Message = msg;
                }
                reject(rsp);
              } else {
                resolve(rsp);
              }
            } else {
              _that.showToast({
                message: `${i18next.t('请求失败')}: ${httpReq.status}`,
                type: 'warning',
              });
              reject(new Error(i18next.t('请求失败')));
            }
          }
        };
      });
    },
    clickNumClick() {
      const currentTime = new Date().getTime();
      // 计算两次相连的点击时间间隔
      this.clickNum = (currentTime - this.lastClickTime) < 200 ? this.clickNum + 1 : 1;
      this.lastClickTime = new Date().getTime();
      if (this.clickNum > 4) {
        console.log('点击超过5次了');
        this.showEnv = true;
      }
    },
    resetEnv() {
      this.clickNum = 0;
      this.showEnv = true;
    },
    changeClassId() {
      localStorage.setItem('idInput', JSON.stringify(this.idInputs));
    },
    switchLanguage(lngItem) {
      this.currentLngItem = lngItem;
      this.showLanguageSelect = false;

      console.log('currentLngItem', lngItem.value, lngItem.label);
      i18next.changeLanguage(lngItem.value);
      const elementLang = lngItem.elementLang || lngItem.value;
      ELEMENT.locale(ELEMENT.lang[elementLang] || ELEMENT.lang.en);
      this.idInputs.className = i18next.t('实时互动-教育版');
      localStorage.setItem('idInput', JSON.stringify(this.idInputs));
      // lng参数加到url里，客户端用
      const urlParams = new URLSearchParams(location.search);
      urlParams.set('lng', lngItem.value);
      window.history.replaceState({ lng: lngItem.value }, '', `?${urlParams.toString()}`);
    },
    getDefaultAppId(env, pkgType = 'ultimate') {
      const cfg = SDK_APPID_MAP[env];
      if (!cfg) {
        return '';
      }
      return cfg[pkgType || 'ultimate'] || '';
    },
    changePackage(pkgType) {
      this.sdkAppId = this.getDefaultAppId(this.envRadio, pkgType);
    },
    getDevelopers() {
      const params =  { Env: this.envRadio };
      this.uinList = [];
      this.uin = '';
      this.post('/getDevelopers', params)
        .then((res) => {
          this.uinList = res.Uin || [];
          const uin = this.uinList.length > 0 ? this.uinList[this.uinList.length - 1] : '';
          if (uin !== this.uin) {
            console.log('getDevelopers success, now uin', uin);
            this.uin = uin;
          }
        })
        .catch((err) => {
          console.log('getDevelopers error', err);
        });
    },
    getRooms() {
      if (!this.canGetRooms) {
        console.log('getRooms but params invalid', this.uin, this.sdkAppId);
        this.resetRoomListState();
        return;
      }
      if (this.currentPage !== 'login') {
        console.log('getRooms but currentPage', this.currentPage);
        this.stopRoomListTimer();
        return;
      }
      const now = Date.now();
      const params = {
        Uin: this.uin,
        SdkAppId: parseInt(this.sdkAppId, 10),
        StartTime: moment(now).add(-2, 'hours')
          .unix(),
        EndTime: moment(now).add(2, 'hours')
          .unix(),
        Limit: 50,
        Page: 1,
      };
      if (params.Uin !== this.roomListData.params.Uin || params.SdkAppId !== this.roomListData.params.SdkAppId) {
        // 数据源变了，清空
        console.log('getRooms', params.Uin, params.SdkAppId);
        this.roomListShowData = {
          list: [],
          message: '',
          statinfo: '',
        };
        this.stopRoomListTimer();
      }
      if (params.Uin === this.roomListData.params.Uin && params.SdkAppId === this.roomListData.params.SdkAppId && this.roomListData.state === 'loading') {
        // 上一个请求还没结束
        return;
      }
      this.startRoomListTimer();
      const newData = {
        params,
        state: 'loading',
        page: 1,
        total: NaN,
        list: [],
        map: {},
        stat: {},
      };
      this.roomListData.state = 'cancel';
      this.roomListData = newData;
      this.getRoomsByPage(newData);
    },
    getRoomsByPage(roomListData) {
      if (roomListData.state !== 'loading') {
        return;
      }
      roomListData.page = roomListData.page || 1;
      this.post('/getRooms', {
        ...roomListData.params,
        Page: roomListData.page,
      })
        .then(({ Response }) => {
          // console.log('getRoomsByPage success', roomListData.page, Response);
          roomListData.total = Response.Total;
          const validList = [];
          const statusOptions = this.loginPageConfig.login.roomList.roomStatus.options;
          Response.Rooms?.forEach((item) => {
            if (!roomListData.map[item.RoomId]) {
              item.Uin = roomListData.params.Uin;
              if (item.RecordUrl === undefined && item.ReplayUrl !== undefined) {
                // 新的是 RecordUrl，兼容旧字段名
                item.RecordUrl = item.ReplayUrl;
              }
              const sortPriority = (statusOptions[item.Status] || {}).sortPriority || 'xx'; // 没配优先级的x开头，排在后面
              const startTime = item.RealStartTime || item.StartTime;
              item.sortValue = [sortPriority, item.Status, startTime].join('_') ;
              roomListData.map[item.RoomId] = item;
              if (!roomListData.stat[item.Status]) {
                roomListData.stat[item.Status] = 1;
              } else {
                // eslint-disable-next-line no-plusplus
                roomListData.stat[item.Status]++;
              }
              validList.push(item);
            } else {
              console.warn('room existed', roomListData.page, item.RoomId, item);
            }
          });
          if (validList.length > 0) {
            roomListData.list = roomListData.list.concat(validList);
          }
          if (roomListData.list.length >= roomListData.total || !validList.length) {
            // 拉完了
            // console.log('getRooms success, total', roomListData.total, roomListData.stat);
            roomListData.state = 'success';
            if (this.roomListData === roomListData) {
              this.roomListShowData.list = roomListData.list.filter(i => i.LiveType !== 10).sort((a, b) => {
                if (a.sortValue < b.sortValue) {
                  return -1;
                }
                if (a.sortValue > b.sortValue) {
                  return 1;
                }
                return 0;
              });
              this.roomListShowData.message = roomListData.list.length > 0 ? '' : i18next.t('暂无课堂，请先创建课堂');
              this.roomListShowData.statinfo = Object.entries(roomListData.stat).map(([status, count]) => `${status}:${count}`)
                .join(',');
            }
          } else {
            // 还有
            roomListData.page += 1;
            this.getRoomsByPage(roomListData);
          }
        })
        .catch((err) => {
          console.warn('getRoomsByPage error', roomListData.page, err);
          roomListData.state = 'error';
          if (this.roomListData === roomListData) {
            this.roomListShowData.list = [];
            this.roomListShowData.message = i18next.t('获取课堂列表失败，点击重试');
            this.roomListShowData.statinfo = '';
            this.stopRoomListTimer();
          }
        });
    },
    startRoomListTimer() {
      if (this.roomListTimer) {
        return;
      }
      this.roomListTimer = setInterval(() => {
        this.getRooms();
      }, 5000);
    },
    stopRoomListTimer() {
      if (!this.roomListTimer) {
        return;
      }
      clearInterval(this.roomListTimer);
      this.roomListTimer = null;
    },
    resetRoomListState() {
      if (this.roomListData.state) {
        this.roomListData.state = 'cancel';
      }
      this.roomListShowData = {
        list: [],
        message: '',
        statinfo: '',
      };
      this.roomListData = {
        params: {},
        state: '',
        page: 0,
        total: NaN,
        list: [],
        map: {},
        stat: {},
      };
      this.stopRoomListTimer();
    },
    enterRoomFromRoomList(room) {
      console.log('enterRoomFromRoomList', room);

      this.idInputs.classId = room.RoomId;
      if (!this.idInputs.studentName) {
        this.randomNum('student');
      }
      localStorage.setItem('idInput', JSON.stringify(this.idInputs));

      this.tryJoinClass({
        uin: this.roomListData.params.Uin, // 不一定是 this.uin
        classId: room.RoomId,
        roleType: 'student',
        roleName: this.idInputs.studentName,
      });
    },
    // 新开页面，直接用 a 标签
    // viewPlaybackFromRoomList(room) {
    //   console.log('viewPlaybackFromRoomList', room);
    //   if (!room.RecordUrl) {
    //     console.log('viewPlaybackFromRoomList, no RecordUrl');
    //     return;
    //   }
    //   location.href = room.RecordUrl;
    // },
    onClickEmptyRoomList() {
      console.log('onClickEmptyRoomList');
      if (this.roomListShowData.list.length) {
        return;
      }
      if (this.roomListData.state === 'error') {
        this.getRooms();
      } else {
        this.gotoPage('create');
      }
    },
  },
};
</script>

