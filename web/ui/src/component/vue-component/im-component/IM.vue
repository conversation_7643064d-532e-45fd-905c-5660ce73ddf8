<template>
  <div
    :class="['im-component', {
      'mobile': isMobile
    }]"
  >
    <div id="im-area-pinned-on-top" class="im-custom-pinned-slot" data-slot="im-pinned"></div>
    <div
      class="msg-scroll-wrapper"
    >
      <ul
        ref="msg-scroll"
        class="im-component-body"
        :class="{
          'show-reply': !!replyMsg,
          'no-bottom-padding': !!noBottomPadding
        }"
      >
        <p
          v-if="inloading"
          class="loading-text"
        >
          {{ $t('加载中...') }}
        </p>
        <MsgComponent
          v-for="(msg, index) in showMsgList"
          :key="`${msg.key}`"
          :msg="msg"
          :index="index"
          :is-teacher="isTeacher"
          :is-assistant="isAssistant"
          :is-supervisor="isSupervisor"
          :enable-msg-control="enableMsgControl"
          :per="imgPercent"
          @set-reply-msg="setReplyMsg"
          @delete-msg="deleteMsg"
          @silence-msg="silenceMsg"
          @resend-img-msg="reSendImgMsg"
          @open-preview-img="openPreviewImg"
          @close-preview-img="closePreviewImg"
          @img-loaded="onImageLoaded"
        />
      </ul>

      <NewMessagesTip
        class="new-msg-tip-wrapper"
        :class="{
          'show-reply': !!replyMsg,
          'no-bottom-padding': !!noBottomPadding
        }"
        :unread-msg-count="unreadMsgCount"
        @click="handleClickNewMessagesTip"
      />
    </div>

    <template v-if="isMobile">
      <QuickReply
        v-if="isShowQuickReply"
        class="im-component-quick-reply"
        :style="{
          'margin-bottom': showIMFooter ? (!!replyMsg ? '124px' : '93px') : '0px',
        }"
        :words-arr="wordsArr"
        @send-quick-msg="onSendQuickMsg"
      />
      <IMFooter
        v-if="showIMFooter"
        ref="imFooter"
      />
    </template>
    <template v-else>
      <el-divider class="im-component-divider" />
      <div
        ref="footer"
        class="im-component-footer"
      >
        <QuickReply
          v-if="isShowQuickReply"
          :words-arr="wordsArr"
          class="im-component-quick-reply"
          @send-quick-msg="onSendQuickMsg"
        />
        <div class="im-component-top-tools">
          <div class="im-component-tools-left">
            <ReceiverSelector
              v-if="showReceiverSelector"
              :disabled="replyMsg && !!replyMsg.dataExt?.IsPrivateMsg"
              :current-receiver="currentPrivateChatReceiver"
              :private-chat-receiver-list="privateChatReceiverList"
              :has-more="hasMore"
              @update:currentReceiver="handleReceiverUpdate"
              @update:keyword="setSearchKeywordForPrivateReceiverList"
              @update:loadMore="loadMoreCompleteMemberList"
            />
          </div>
          <div class="im-component-tools-right">
            <ChatSetting
              v-if="(isTeacher || isAssistant || isSupervisor)"
              :settings-list="silenceModeList"
              :silence-mode="currentSilenceMode"
              @update:silenceMode="handleSilenceModeUpdate"
            />
            <TranslatorSelector
              v-if="showTranslator"
              :class="{ 'small-screen': isSmallScreen }"
            />
            <a
              :class="['im-component-img-tool',
                       {'small-screen': isSmallScreen},
                       // fileSelected ? 'file-selected' : 'file'
              ]"
              @click.capture="onInputFile"
            >
              <span class="image-icon">
                <IconImg />
              </span>
              <input
                ref="imagePicker"
                :key="imagePickerIndex"
                type="file"
                :title="$t('发送图片')"
                :accept="acceptExt"
                @change="handleChange"
              >
              <span class="file-text">{{ $t('图片') }}</span>
            </a>
            <a
              v-if="supportFileMessage"
              :class="['im-component-img-tool',
                       {'small-screen': isSmallScreen},
                       // fileSelected ? 'file-selected' : 'file'
              ]"
              @click.capture="onInputAllKindsOfFile"
            >
              <span class="file-icon">
                <IconFile />
              </span>
              <input
                ref="filePicker"
                :key="filePickerIndex"
                type="file"
                :title="$t('发送文件')"
                @change="handleSendingFileChange"
              >
              <span class="file-text">{{ $t('文件') }}</span>
            </a>
            <el-popover
              v-if="showEmojiButton"
              ref="popover"
              popper-class="im-component-meme-popover"
              placement="left"
              trigger="click"
              :disabled="isSupervisor"
              @show="showEmoji"
              @hide="hideEmoji"
            >
              <div
                slot="reference"
                :class="['im-component-img-tool',
                         {'small-screen': isSmallScreen},
                         // fileSelected ? 'file-selected' : 'file'
                ]"
                @click.capture="onInputFile"
              >
                <span class="emoji-icon">
                  <IconEmoji />
                </span>
                <span
                  v-if="false"
                  class="file-text"
                >{{ $t('表情') }}</span>
              </div>
              <Emoji
                ref="emoji"
                class="inComponent"
                :is-closed="emojiClosed"
                @send-emoji="sendEmoji"
              />
            </el-popover>
          </div>
        </div>
        <IMInputReply
          v-if="replyMsg"
          :msg="replyMsg"
          @click-close="setReplyMsg(null)"
        />
        <InputElement
          v-show="!imgSend && !fileSend && !showEditor"
          id="imgInput"
          ref="input"
          :class="['im-component-input inComponent', {
            'is-reply': !!replyMsg
          }]"
          :textarea="true"
          @enter="textInputEnter"
          @blur="textInputBlur"
          @change="textChange"
          @click="textInputClick"
          @paste-file="onInputPaste"
        />
        <div
          v-show="imgSend || fileSend || showEditor"
          id="editor"
          ref="editor"
          class="im-component-img-input inComponent"
          contenteditable="true"
          @paste.prevent="onPaste"
          @keydown.enter.prevent="textInputEnter"
          @keyup="imgTextChange"
        />
        <div
          v-if="showToast"
          class="im-toast"
        >
          {{ $t('请先发送图片再编辑文字消息') }}
        </div>
        <div class="im-component-bottom-tools">
          <div class="im-component-switch-wrap">
            <div
              v-if="showJoinExitSwitch"
              class="im-component-msg-switch"
            >
              <el-switch
                :value="showJoinExit"
                @change="switchShowJoinExit"
              />
              <span>{{ showJoinExit ? $t('显示成员进出情况') : $t('不显示成员进出情况') }}</span>
            </div>
            <div
              v-if="showHandsUpSwitch"
              class="im-component-msg-switch"
            >
              <el-switch
                v-model="showHandsUp"
                @change="switchShowHandsUpMsg"
              />
              <span>{{ showHandsUp ? $t('显示成员举手') : $t('不显示成员举手') }}</span>
            </div>
          </div>
          <button
            class="im-component-send"
            :disabled="inputEmpty || isSupervisor"
            @click="textInputEnter"
          >
            {{ $t('发送') }}
          </button>
        </div>
      </div>
    </template>
  </div>
</template>

<script>
import i18next from 'i18next';
import store from 'store';
import Constant from '@/util/Constant';
import ImageCache from '../image-preview-component/ImageCache.js';
import IMFooter from './IMFooter.vue';
import { MsgImageState } from './MsgBase';
import QuickReply from './QuickReply.vue';
import { IMBaseComponent } from './IMBase';
import MsgComponent from './Msg.vue';
import Emoji from './Emoji.vue';
import IconEmoji from './assets/svg-component/emoji.svg';
import IconImg from '../introduction-discuss-component/assets/svg-component/image.svg';
import IconFile from '../introduction-discuss-component/assets/svg-component/file.svg';
import TranslatorSelector from './TranslatorSelector';
import InputElement from './InputElement.vue';
import IMInputReply from './IMInputReply.vue';
import ReceiverSelector from '@/component/vue-component/im-component/ReceiverSelector.vue';
import ChatSetting from '@/component/vue-component/im-component/ChatSetting.vue';
import NewMessagesTip from '@/component/vue-component/im-component/NewMessagesTip.vue';

const VALID_IMG_TYPES = ['JPG', 'JPEG', 'PNG', 'GIF', 'BMP'];

export default {
  components: {
    NewMessagesTip,
    ChatSetting,
    ReceiverSelector,
    MsgComponent,
    Emoji,
    IconImg,
    IconFile,
    IconEmoji,
    TranslatorSelector,
    IMFooter,
    InputElement,
    IMInputReply,
    QuickReply,
  },
  extends: IMBaseComponent,
  props: {
    needCameraFoot: {
      type: Boolean,
      default: false,
    },
    showIMFooter: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      showEmojiButton: true,      // 是否显示表情按钮
      showImageButton: true,     // 是否显示图片按钮
      showJoinExitSwitch: false,  // 是否显示进出房切换开关
      showJoinExit: true,         // 当前是否启用了显示进出房消息
      showHandsUpSwitch: false,  // 是否显示学生举手切换开关
      showHandsUp: true,          // 是否显示学生举手
      enableMsgControl: false,    // 显示单条消息的控制栏
      emojiSelected: false,       // 表情按钮处于选中状态
      imageSelected: false,       // 图片按钮处于选中状态
      fileSelected: false,       // 图片按钮处于选中状态
      inputEmpty: true, // 输入内容的长度 >0 false
      emojiClosed: false,
      isLiveClass: false,
      isOneOnOneClass: false, // 是否是一对一课堂
      canMemberStageUp: true,
      acceptExt: '',
      teacherId: null,
      studentShowJoinExitMsg: true, // 学生是否显示进退房信息
      showJoinExitMsg: true, // 是否显示进退房信息
      isStudent: false, // 是否是学生
      imgFile: '',
      imgSend: false,
      fileSend: false,
      showEditor: false,
      imgInputText: '',
      file: null,
      pasteFile: null,
      imgUrl: '',
      imagePickerIndex: 0,
      filePickerIndex: 0,
      isSupervisor: false,
      isTeacher: false,
      isAssistant: false,
      isTeacherScreenShareOnElectron: false,  // 桌面端老师屏幕分享
      showToast: false,
      lastScrollHeight: 0,
      lastModifyTime: -1,
      scrollEndTimer: null,
      inloading: false,
      tipsHandUp: i18next.t('举手'),
      showTranslator: false,
      roleInfo: {},
      roomInfo: {},
      inputShowHandUp: true,
      isIOS: false,               // 是否iOS设备
      isAndroid: false,           // 是否安卓设备isMicEnable
      isClassStart: false,
      isStageUp: false,
      isPortrait: false,
      wordsArr: TCIC.SDK.instance.getQuickIMWords(),
      supportFileMessage: false,
      unreadMsgCount: 0,
    };
  },
  computed: {
    canMemberHandup() {
      // 举手可用
      return !this.isLiveClass && this.canMemberStageUp;
    },
    isShowQuickReply() {
      return this.isStudent && this.wordsArr.length !== 0;
    },
    noBottomPadding() {
      return this.isShowQuickReply || !this.isMobile;
    },
    showReceiverSelector() {
      if (!this.supportPrivateChat) {
        return false;
      }
      if (this.isStudent && !this.isAssistant) {
        return this.currentSilenceMode === 0 || this.currentSilenceMode === 2;
      }
        return true;
    },
  },
  watch: {
    msgList() {
      const msgScroll = this.$refs['msg-scroll'];
      const needScroll = msgScroll.scrollTop + msgScroll.clientHeight + 1 >= msgScroll.scrollHeight;
      this.$nextTick(() => {
        if (needScroll) {
          msgScroll.scrollTop = msgScroll.scrollHeight;
        }
      });
    },
    showToast() {
      this.disappear();
    },
  },

  mounted() {
    this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
    this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;

    this.isLiveClass = TCIC.SDK.instance.isLiveClass();
    this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
    this.canMemberStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;

    const showJoinExit = store.get('showJoinExit');
    if (typeof showJoinExit === 'boolean') {
      TCIC.SDK.instance.setState(TCIC.TMainState.Join_Quit_Tips, showJoinExit);
    }
    const showHandsUp = store.get('showHandsUp');
    if (typeof showHandsUp === 'boolean') {
      this.showHandsUp = showHandsUp;
      TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, this.showHandsUp);
    }
    if (!this.canMemberHandup) {
      this.showHandsUp = false;
      TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, this.showHandsUp);
    }

    this.makeSureClassJoined(() => {
      this.showJoinExitSwitch = TCIC.SDK.instance.isFeatureAvailable('JoinExitSwitch');
      this.showHandsUpSwitch = TCIC.SDK.instance.isFeatureAvailable('HandsUpSwitch');
      this.enableMsgControl = TCIC.SDK.instance.isFeatureAvailable('MsgControl');
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
      this.canMemberStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;
      // 0411 所有课程都不显示进出开关，不显示举手开关
      this.showJoinExitSwitch = false;
      this.showHandsUpSwitch = false;
      // 直播课或1v0不显示举手消息开关，也不显示举手消息
      if (!this.canMemberHandup) {
        this.showHandsUpSwitch = false;
        this.showHandsUp = false;
        TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, this.showHandsUp);
      }
      this.showTranslator = TCIC.SDK.instance.isFeatureAvailable('IMTranslator') && TCIC.SDK.instance.isOneOnOneClass();
      this.studentShowJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('StudentShowJoinExitMsg');
      this.showJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('ShowJoinExitMsg');
      this.teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
      this.isStudent = !TCIC.SDK.instance.isTeacher() && !TCIC.SDK.instance.isSupervisor();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.$nextTick(() => {
        this.initEvents();
      });
    });

    this.addLifecycleTCICEventListener(TCIC.TIMEvent.Set_Quick_IM_Words, (wordsArr) => {
      this.wordsArr = wordsArr;
    });

    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Recv_IM_Msgs, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      console.log('handleMsgs', msgs, this.isVisible);
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      this.handleMsgs(msgs);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Recv_IM_Msgs_Only_Message_List, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      console.log('handleMsgs', msgs, this.isVisible);
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      this.handleMsgs(msgs);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Delete_IM_Msgs, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      this.deleteMsgs(msgs);
    });
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Update_IM_Msgs, (msgs) => {
      // 当前组件不可见时，直接返回，减少性能消耗
      if (!Array.isArray(msgs) || !this.isVisible) {
        return;
      }
      // 更新至最新数据
      this.msgList = [];
      this.showMsgList = [];
      this.handleMsgs(TCIC.SDK.instance.getIMMsgList());
    });
    this.addLifecycleTCICStateListener(Constant.TStateImgSendStatus, (postMsg) => {
      if (postMsg !== null && typeof postMsg === 'object') {
        this.handleImgSendState(postMsg, this.pasteFile !== null ? this.pasteFile : this.file);

        if (postMsg.imgState === MsgImageState.Success || postMsg.imgState === MsgImageState.Fail) {
          this.pasteFile = null;
          const that = this;
          ImageCache.getImgBlobByXHR(this.imgUrl).then((blob) => {
            // revokeObjectURL前，从本地文件缓存base64
            const index = this.msgList.findIndex(item => item.localImgSeq === postMsg.currentID);
            if (index >= 0) {
              const msg = that.msgList[index];
              ImageCache.cacheImage(msg.preview1 || msg.preview2, blob);
            }
            window.URL.revokeObjectURL(that.imgUrl);
          }, () => {
            window.URL.revokeObjectURL(that.imgUrl);
          });
        }
      }
    });
    this.addLifecycleTCICStateListener(Constant.TStateFileSendStatus, (postMsg) => {
      if (postMsg !== null && typeof postMsg === 'object') {
        this.handleImgSendState(postMsg, this.pasteFile !== null ? this.pasteFile : this.file);

        if (postMsg.imgState === MsgImageState.Success || postMsg.imgState === MsgImageState.Fail) {
          this.pasteFile = null;
          const that = this;
          ImageCache.getImgBlobByXHR(this.imgUrl).then((blob) => {
            // revokeObjectURL前，从本地文件缓存base64
            const index = this.msgList.findIndex(item => item.localImgSeq === postMsg.currentID);
            if (index >= 0) {
              const msg = that.msgList[index];
              ImageCache.cacheImage(msg.preview1 || msg.preview2, blob);
            }
            window.URL.revokeObjectURL(that.imgUrl);
          }, () => {
            window.URL.revokeObjectURL(that.imgUrl);
          });
        }
      }
    });
    // 屏幕分享状态变化
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, this.onScreenShareUpdate);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      if (status > TCIC.TClassStatus.Not_Start) {
        this.isClassStart = true;
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (val) => {
      this.isStageUp = val;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    /**
     * 如果有其它地方修改了配置，则同步响应，
     * @keroliang
     * 之前有个客户需求是需要在自定义 JS 里面调用 setState 来覆盖 Join_Quit_Tips 的默认值
     *  目前的业务代码里面应该没有其他地方会改这个状态
    *  后来那位客户放弃这个需求了，但考虑到这是一个良性的 bugfix 改动，所以相应的变更有保留下来
     */
    this.addLifecycleTCICStateListener(TCIC.TMainState.Join_Quit_Tips, (showJoinExit) => {
      this.showJoinExit = showJoinExit;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Hand_Up_Tips, (val) => {
      this.showHandsUp = val;
    });

    this.supportFileMessage = TCIC.SDK.instance.isFeatureAvailable('FileMessage');
  },
  beforeDestroy() {
    this.uninitEvents();
  },

  methods: {
    handleReceiverUpdate(receiver) {
      this.currentPrivateChatReceiver = receiver;
    },
    switchShowJoinExit(value) {
      console.log(':::', value);
      TCIC.SDK.instance.setState(TCIC.TMainState.Join_Quit_Tips, value);
      store.set('showJoinExit', value);
    },
    switchShowHandsUpMsg(value) {
      console.log(':::', value);
      TCIC.SDK.instance.setState(TCIC.TMainState.Hand_Up_Tips, value);
      store.set('showHandsUp', value);
    },

    leaveRoom() {
      // 请求离开课堂
      TCIC.SDK.instance.leaveClass();
    },
    // Toast消失
    disappear() {
      if (this.showToast) {
        setTimeout(() => {
          this.showToast = false;
        }, 3000);
      }
    },
    onInputAllKindsOfFile(event) {
      const isSilenceAll = this.currentSilenceMode === 3;
      const chatEnable = TCIC.SDK.instance.getState(TCIC.TMainState.Chat_Permission, true);
      if (isSilenceAll && this.isStudent) {
        window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }));
        event.preventDefault();
        return;
      }

      if (!chatEnable) {
        window.showToast(i18next.t('你已被禁言'));
        event.preventDefault();
        return;
      }
      if (!this.inputEmpty && !this.imgSend && !this.fileSend) {
        window.showToast(i18next.t('请先发送文字消息，再发送文件'));
        event.preventDefault();
      } else if (this.imgSending || this.fileSending) {
        window.showToast(i18next.t('请等待当前消息上传完成'));
        event.preventDefault();
        return;
      }
    },
    onInputFile(event) {
      const isSilenceAll = this.currentSilenceMode === 3;
      const chatEnable = TCIC.SDK.instance.getState(TCIC.TMainState.Chat_Permission, true);
      if (isSilenceAll && this.isStudent) {
        window.showToast(i18next.t('{{arg_0}}已设置全员禁言', { arg_0: this.roleInfo.teacher }));
        event.preventDefault();
        return;
      }

      if (!chatEnable) {
        window.showToast(i18next.t('你已被禁言'));
        event.preventDefault();
        return;
      }
      if (!this.inputEmpty && !this.imgSend && !this.fileSend) {
        window.showToast(i18next.t('请先发送文字消息，再发送图片'));
        event.preventDefault();
      } else if (this.imgSending || this.fileSending) {
        window.showToast(i18next.t('请等待当前消息上传完成'));
        event.preventDefault();
        return;
      }
    },

    onScreenShareUpdate(flag) {
      const vodPlay = TCIC.SDK.instance.getState(TCIC.TMainState.Vod_Play, 2);
      const isSubCameraStarted = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
      const isVodPlayOnElectron = TCIC.SDK.instance.isTeacherOrAssistant()
        && TCIC.SDK.instance.isElectron()
        && (vodPlay < 2);
      this.isTeacherScreenShareOnElectron = TCIC.SDK.instance.isTeacher()
        && TCIC.SDK.instance.isElectron()
        && (flag < 2)
        && !isSubCameraStarted
        && !isVodPlayOnElectron; // 老师在electron下屏幕分享
    },

    onComponentVisibilityChange(visible) {
      console.log(`IM.vue onComponentVisibilityChange: ${visible}......`);
      this.isVisible = visible;
      if (visible) {
        // 显示出来后直接标记所有消息为已读
        TCIC.SDK.instance.markAllMessagesAsRead();
        // 更新至最新数据
        this.msgList = [];
        this.showMsgList = [];
        this.handleMsgs(TCIC.SDK.instance.getIMMsgList());

        // 滑动到底部
        this.scrollToEnd();
      } else {
        if (!this.isMobile && this.$refs.popover) {
          // 隐藏时关掉表情弹窗
          this.$refs.popover.doClose();
          this.emojiClosed = true;
        }
      }
    },

    onRecvMsg(msg) {
      if (this.isVisible) {
        // 组件可见时直接标记收到的消息为已读
        TCIC.SDK.instance.markMessageAsRead(msg.seq);
      }
    },

    async textInputEnter() {
      // 巡课角色不允许发送消息
      if (this.isSupervisor) {
        return;
      }

      if (this.imgSend) {
        const judgeImageSizeRes = await this.judgeImageSize();
        if (!judgeImageSizeRes) {
          return;
        }
        if (this.pasteFile !== null) {
          this.sendImgMsg(this.pasteFile, this.imgUrl, null);
        } else {
          this.sendImgMsg(this.file, this.imgUrl, null);
        }
        this.$refs.editor.innerHTML = '';
        this.$refs.imagePicker.value = null;
        this.imgSend = false;
        this.inputEmpty = true;
        this.$nextTick(() => {
          this.$refs.input.setText(this.imgInputText);
          if (this.$refs.input.inputText.length > 0) {
            this.inputEmpty = false;
          }
        });
        return;
      }

      if (this.fileSend) {
        if (!this.judgeFileSize()) {
          return;
        }
        if (this.pasteFile !== null) {
          this.sendFileMsg(this.pasteFile, this.imgUrl, null);
        } else {
          this.sendFileMsg(this.file, this.imgUrl, null);
        }
        this.$refs.editor.innerHTML = '';
        this.$refs.filePicker.value = null;
        this.fileSend = false;
        this.inputEmpty = true;
        this.$nextTick(() => {
          this.$refs.input.setText(this.imgInputText);
          if (this.$refs.input.inputText.length > 0) {
            this.inputEmpty = false;
          }
        });
        return;
      }

      this.sendMsg(this.$refs.input.getPureText())
          .then(() => {
            this.inputEmpty = true;
            this.$refs.input.clearText();
          });
    },

    textChange() {
      this.inputText = this.$refs.input.inputText;
      if (this.inputText.length > 0) {
        this.inputEmpty = false;
      } else {
        this.inputEmpty = true;
      }
    },

    textInputBlur() {
      setTimeout(() => {
        const scrollHeight = document.documentElement.scrollTop || document.body.scrollTop || 0;
        window.scrollTo(0, Math.max(scrollHeight, 0));
        console.log('inputBlur chatroom', scrollHeight);
      }, 100);
    },

    textInputClick() {
      if (this.isMobile && !this.isWeb) {
        TCIC.SDK.instance.getComponent('mobile-im-input-bar-component')
          .getVueInstance()
          .showTextInput(this);
      }
    },

    emojiInputClick() {
    },

    sendEmoji(emojiText) {
      // 巡课角色不允许发送消息
      if (this.isSupervisor) {
        return;
      }

      this.sendMsg(emojiText);
      this.closeEmoji();
    },

    closeEmoji() {
      if (this.$refs.popover) {
        this.$refs.popover.doClose();
        this.emojiClosed = true;
      }
    },

    showEmoji() {
      this.emojiSelected = true;
      this.emojiClosed = false;
    },
    hideEmoji() {
      this.emojiSelected = false;
      this.emojiClosed = true;
    },
    handleClickNewMessagesTip() {
      this.unreadMsgCount = 0;
      this.scrollToEnd();
    },
    scrollToEnd() {
      const msgScroll = this.$refs['msg-scroll'];
      msgScroll.scrollTop = 10000000;
    },

    getAllKindOfFile(allKindOfFile) {
      console.log('sendFileMsg, inputRes', allKindOfFile);
      TCIC.SDK.instance.reportLog(
          'sendImgMsg',
          `getImageFile, inputRes.files.length ${allKindOfFile?.files?.length}, inputRes.name ${allKindOfFile?.name}, inputRes.size ${allKindOfFile?.size}`,
      );
      // 统一格式
      if (allKindOfFile?.files) {
        if (allKindOfFile?.files.length > 0) {
          this.file = allKindOfFile;
        } else if (allKindOfFile.name) {
          this.file = {
            files: [allKindOfFile],
          };
        } else {
          this.file = allKindOfFile;
        }
      } else {
        this.file = {
          files: [allKindOfFile],
        };
      }
      const singleFile = this.file.files[0];
      console.log('sendFileMsg, singleFile', singleFile);
      TCIC.SDK.instance.reportLog(
          'sendFileMsg',
          singleFile ? `singleFile, name ${singleFile?.name}, type ${singleFile?.type}, size ${singleFile?.size}` : 'singleFile, no file',
      );
      if (!singleFile) {
        // 没选文件不用提示
        return false;
      }
      // 加个校验，以防 accept 没生效
      const tmpIndex = singleFile.name.lastIndexOf('.');
      const fileType = tmpIndex >= 0 ? singleFile.name.slice(tmpIndex + 1) : '';
      const bytes = singleFile?.size;
      if (!bytes) {
        window.showToast(i18next.t('选取的文件不能为空'), 'error');
        return false;
      }
      const size = TCIC.SDK.instance.isMac()
          ? (bytes / 1000 / 1000).toFixed(2)
          : (bytes / 1024 / 1024).toFixed(2);
      if (size >= 100) {
        window.showToast(i18next.t('选取的文件大小不能超过100M'), 'error');
        return false;
      }
      try {
        window.URL.revokeObjectURL(this.imgUrl);
      } catch (err) {
        // 注意这里不用 return false
        TCIC.SDK.instance.reportLog('sendFileMsg', `revokeObjectURL error, ${err?.message}`);
      }
      try {
        this.imgUrl = window.URL.createObjectURL(singleFile);
      } catch (err) {
        TCIC.SDK.instance.reportLog('sendFileMsg', `createObjectURL error, ${err?.message}`);
        return false;
      }
      return true;
    },
    async getImageFile(imgFile) {
      console.log('sendImgMsg, inputRes', imgFile);
      TCIC.SDK.instance.reportLog(
          'sendImgMsg',
          `getImageFile, inputRes.files.length ${imgFile?.files?.length}, inputRes.name ${imgFile?.name}, inputRes.size ${imgFile?.size}`,
      );
      // 统一格式
      if (imgFile?.files) {
        if (imgFile?.files.length > 0) {
          this.file = imgFile;
        } else if (imgFile.name) {
          this.file = {
            files: [imgFile],
          };
        } else {
          this.file = imgFile;
        }
      } else {
        this.file = {
          files: [imgFile],
        };
      }
      const singleFile = this.file.files[0];
      console.log('sendImgMsg, singleFile', singleFile);
      TCIC.SDK.instance.reportLog(
          'sendImgMsg',
          singleFile ? `singleFile, name ${singleFile?.name}, type ${singleFile?.type}, size ${singleFile?.size}` : 'singleFile, no file',
      );
      if (!singleFile) {
        // 没选文件不用提示
        return false;
      }
      // 加个校验，以防 accept 没生效
      const tmpIndex = singleFile.name.lastIndexOf('.');
      const fileType = tmpIndex >= 0 ? singleFile.name.slice(tmpIndex + 1) : '';
      if (VALID_IMG_TYPES.indexOf(fileType.toUpperCase()) < 0) {
        window.showToast(i18next.t('仅支持 JPG、PNG、GIF、BMP 格式的图片文件'), 'error');
        return false;
      }
      const bytes = singleFile?.size;
      if (!bytes) {
        window.showToast(i18next.t('选取的图片文件不能为空'), 'error');
        return false;
      }
      const size = TCIC.SDK.instance.isMac()
          ? (bytes / 1000 / 1000).toFixed(2)
          : (bytes / 1024 / 1024).toFixed(2);
      if (size >= 20) {
        window.showToast(i18next.t('选取的图片文件大小不能超过20M'), 'error');
        return false;
      }

      const isValidImage = await this.validateImageByLoading(singleFile);
      if (!isValidImage) {
        return false;
      }
      try {
        window.URL.revokeObjectURL(this.imgUrl);
      } catch (err) {
        // 注意这里不用 return false
        TCIC.SDK.instance.reportLog('sendImgMsg', `revokeObjectURL error, ${err?.message}`);
      }
      try {
        this.imgUrl = window.URL.createObjectURL(singleFile);
      } catch (err) {
        TCIC.SDK.instance.reportLog('sendImgMsg', `createObjectURL error, ${err?.message}`);
        return false;
      }
      return true;
    },

    async handleChange() {
      if (this.$refs.editor !== undefined
          && this.$refs.editor.childNodes.length === 1) {
        const myNode = document.getElementById('editor');
        myNode.removeChild(myNode.firstChild);
      }

      const checkRes = await this.getImageFile(this.$refs.imagePicker);
      if (!checkRes) {
        return false;
      }

      this.imgInputText = this.$refs.input.inputText;
      this.imgSend = true;
      // 显示到输入框
      this.$nextTick(() => {
        this.$refs.editor.focus();
        setTimeout(() => {
          this.$refs.editor.blur();
        }, 500);
      });
      this.chooseImg(this.file.files[0]);
      this.inputEmpty = false;
      this.imagePickerIndex += 1;
      this.pasteFile = null;  // 清空复制文件
    },
    handleSendingFileChange() {
      if (this.$refs.editor !== undefined
          && this.$refs.editor.childNodes.length === 1) {
        const myNode = document.getElementById('editor');
        myNode.removeChild(myNode.firstChild);
      }

      const checkRes = this.getAllKindOfFile(this.$refs.filePicker);
      if (!checkRes) {
        return false;
      }

      this.imgInputText = this.$refs.input.inputText;
      this.fileSend = true;
      // 显示到输入框
      this.$nextTick(() => {
        this.$refs.editor.focus();
        setTimeout(() => {
          this.$refs.editor.blur();
        }, 500);
      });
      this.chooseFile(this.file.files[0]);
      this.inputEmpty = false;
      this.imagePickerIndex += 1;
      this.pasteFile = null;  // 清空复制文件.
    },
    insertHTML(dom) {
      if (window.getSelection) {
        const sel = window.getSelection();
        if (sel.containsNode(this.$refs.footer, true)) {
          if (sel.rangeCount) {
            const range = sel.getRangeAt(0);
            range.collapse(true);
            range.insertNode(dom);
            range.setStartAfter(dom);
            range.collapse(true);
            sel.removeAllRanges();
            sel.addRange(range);
          }
        } else {
          console.error('paste selection not contains footer');
        }
      }
    },
    chooseImg(file) {
      const reader = new FileReader();
      const that = this;
      reader.onload = function () {
        const result = this.result;
        const imgRegx = /^data:image/;
        if (imgRegx.test(result)) {
          const image = document.createElement('img');
          image.src = result;
          image.className = 'input-image-preview';
          image.onclick = () => {
            TCIC.SDK.instance.getComponent('image-preview').getVueInstance()
              .showWithUrl(that.imgUrl);
          };
          that.insertHTML(image);
        }
      };
      reader.readAsDataURL(file);
    },

    chooseFile(file) {
      const reader = new FileReader();
      const that = this;
      reader.onload = function () {
        const fileDiv = document.createElement('div');
        fileDiv.className = 'file-preview';
        fileDiv.setAttribute('contenteditable', 'false'); // 禁止编辑

        const fileName = document.createElement('span');
        fileName.className = 'file-name';
        fileName.textContent = file.name;
        fileName.setAttribute('contenteditable', 'false'); // 禁止编辑

        fileDiv.appendChild(fileName);

        that.fileSend = true;
        that.showEditor = false;
        that.insertHTML(fileDiv);

        fileDiv.addEventListener('keydown', (event) => {
          if (event.key === 'Backspace' || event.key === 'Delete') {
            event.preventDefault();
            fileDiv.remove();
          }
        });
      };
      reader.readAsDataURL(file);
    },


    imgTextChange() {
      if (this.$refs.editor.childNodes.length > 1) {
        this.showToast = true;
        // window.showToast(i18next.t('请先发送图片再编辑文字消息'));
        while (this.$refs.editor.childNodes.length > 1) {
          const myNode = document.getElementById('editor');
          myNode.removeChild(myNode.lastChild);
        }
        this.$refs.imagePicker.value = null;
        this.$refs.filePicker.value = null;
        return;
      }

      if (this.imgSend && this.$refs.editor.innerHTML.length === 0) {
        this.imgSend = false;
        this.inputEmpty = true;   // 删除图片时需要重置状态
        this.$refs.imagePicker.value = null;
      }

      this.$nextTick(() => {
        if (this.fileSend && this.$refs.editor.innerHTML.length === 0) {
          this.fileSend = false;
          this.inputEmpty = true;
          this.$refs.filePicker.value = null;
        }
      });
    },

    onImageLoaded() {
      // needScroll：当用户在看上面的消息的时候 不要滚动到底部
      // 判断如果 当前离底部只有一个文字消息 或 图片消息的时候 自动滚动到地步（需优化）
      const msgScroll = this.$refs['msg-scroll'];
      msgScroll.scrollTop = msgScroll.scrollHeight;
    },

    async judgeImageSize() {
      // 判断图片大小
      let sizeFile = null;
      if (this.pasteFile !== null) {
        sizeFile = this.pasteFile;
      } else {
        sizeFile = this.file.files[0];
      }
      const size = TCIC.SDK.instance.isMac()
          ? (sizeFile.size / 1000 / 1000).toFixed(2)
          : (sizeFile.size / 1024 / 1024).toFixed(2);
      if (size >= 20) {
        window.showToast(i18next.t('图片文件大小不超过20M'), 'error');
        return false;
      }
      const isValidImage = await this.validateImageByLoading(sizeFile);
      return isValidImage;
    },
    judgeFileSize() {
      // 判断图片大小
      let sizeFile = null;
      if (this.pasteFile !== null) {
        sizeFile = this.pasteFile;
      } else {
        sizeFile = this.file.files[0];
      }
      const size = TCIC.SDK.instance.isMac()
          ? (sizeFile.size / 1000 / 1000).toFixed(2)
          : (sizeFile.size / 1024 / 1024).toFixed(2);
      if (size >= 100) {
        window.showToast(i18next.t('选取的文件大小不能超过100M'), 'error');
        return false;
      }
      return true;
    },
    onInputPaste(event) {
      this.imgInputText = this.$refs.input.inputText;
      this.imgSend = true;
      this.inputEmpty = false;
      this.$nextTick(() => {
        this.$refs.editor.focus();
        this.onPaste(event);
      });
    },
    onPaste(e) {
      this.pasteFile = null;
      this.doPaste(e);
    },
    doPaste(e) {
      if (!(e.clipboardData && e.clipboardData.items)) {
        return;
      }
      if (this.imgSending || this.fileSending) {
        window.showToast(i18next.t('请等待当前消息上传完成'));
        event.preventDefault();
        return;
      }

      return new Promise(async (resolve, reject) => {
        for (let i = 0, len = e.clipboardData.items.length; i < len; i++) {
          const item = e.clipboardData.items[i];

          if (item.kind === 'string') {
            item.getAsString((str) => {
              resolve(str);
            });
          } else if (item.kind === 'file') {
            this.pasteFile = item.getAsFile();

            if (this.pasteFile !== null && this.pasteFile !== undefined) {
              const fileType = this.pasteFile.type.split('/')[0];
              window.URL.revokeObjectURL(this.imgUrl);
              this.imgUrl = window.URL.createObjectURL(this.pasteFile);

              const isImage = fileType === 'image';
              const isValidImage = isImage && await this.validateImageByLoading(this.pasteFile);

              if (isValidImage) {
                this.imgSend = true;
                this.fileSend = false;
                this.chooseImg(this.pasteFile);
              } else {
                if (this.supportFileMessage) {
                  this.showEditor = true;
                  this.imgSend = false;
                  this.chooseFile(this.pasteFile);
                } else {
                  window.showToast(i18next.t('仅支持 JPG、PNG、GIF、BMP 格式的图片文件'), { type: 'error' });
                  reject(new Error(i18next.t('仅支持 JPG、PNG、GIF、BMP 格式的图片文件')));
                }
              }

              this.imagePickerIndex += 1;
            }
          } else {
            reject(new Error(i18next.t('复制失败')));
          }
        }
      });
    },
    handleMsgs(msgs) {
      console.log('handleMsgs', msgs);
      if (!Array.isArray(msgs)) {
        return;
      }
      const currentUserId = TCIC.SDK.instance.getUserId();
      const filterList = msgs.filter((msg) => {
        // 学生 开启了显示进退房黑名单开关后
        // 不显示进退房Tips
        if (!this.showJoinExitMsg || (this.isStudent && !this.studentShowJoinExitMsg)) {
          return msg.msgType !== TCIC.TIMMsgType.JoinTips && msg.msgType !== TCIC.TIMMsgType.QuitTips;
        }
        return (!msg.dataExt?.IsPrivateMsg || (msg.dataExt?.PrivateInfo?.From?.ID === currentUserId || msg.dataExt?.PrivateInfo?.To?.ID === currentUserId));
      });
      if (filterList.length === 0) {
        return;
      }

      const scrollDom = this.$refs['msg-scroll'];
      const isAtBottom = scrollDom.scrollTop + scrollDom.clientHeight >= scrollDom.scrollHeight - 100;
      const navigateToBottom = isAtBottom || (msgs.length === 1 && msgs[0].from === currentUserId);

      this.appendMsgs(filterList);

      this.$nextTick(() => {
        if (navigateToBottom) {
          this.scrollToEnd();
        } else {
          this.unreadMsgCount += filterList.length;
        }
      });
    },
    initEvents() {
      const scrollDom = this.$refs['msg-scroll'];
      if (scrollDom) {
        scrollDom.addEventListener('scroll', this.handleScroll);
      }
    },
    uninitEvents() {
      const scrollDom = this.$refs['msg-scroll'];
      if (scrollDom) {
        scrollDom.removeEventListener('scroll', this.handleScroll);
      }
    },
    handleScroll() {
      const scrollDom = this.$refs['msg-scroll'];
      if (scrollDom && this.msgList.length > this.pageSize) {
        // 需要滚动的情况下, 距离顶部只有不到200了, 去触发加载更早的消息
        if (scrollDom.scrollHeight > scrollDom.clientHeight && scrollDom.scrollTop <= 200) {
          if (TCIC.SDK.instance.isIOS()) {
            this.inloading = this.showMsgList.length < this.msgList.length;
            // iOS需要用100ms定时器监听到滑动结束后，才处理加载事件
            if (this.scrollEndTimer) {
              clearTimeout(this.scrollEndTimer);
              this.scrollEndTimer = null;
            }
            this.scrollEndTimer = setTimeout(() => {
              this.handleScrollToTop();
            }, 100);
          } else {
            window.requestAnimationFrame(() => {
              this.handleScrollToTop();
            });
          }
        } else if (scrollDom.scrollTop + scrollDom.clientHeight >= scrollDom.scrollHeight - 10) {
          // 滚动到底部, 因为上面不可见高度+可见区域比整体内容总高度-10还大, 因此证明滚到底部了
          if (TCIC.SDK.instance.isIOS()) {
            if (this.scrollEndTimer) {
              clearTimeout(this.scrollEndTimer);
            }
            this.scrollEndTimer = setTimeout(() => {
              this.unreadMsgCount = 0;
              this.handleScrollBottom();
            }, 100);
          } else {
            window.requestAnimationFrame(() => {
              this.unreadMsgCount = 0;
              this.handleScrollBottom();
            });
          }
        }
        this.updateLastScrollHeight();
      } else if (scrollDom.scrollTop + scrollDom.clientHeight >= scrollDom.scrollHeight - 40) {
        this.scrollEndTimer = setTimeout(() => {
          this.unreadMsgCount = 0;
        }, 100);
      }
    },
    handleScrollToTop() {
      this.scrollEndTimer = null;
      const now = new Date().getTime();
      const canModify = now - this.lastModifyTime > 500;
      // 滑动到顶部时加载上一页，500ms内(取决于滑动窗口高度和滑动速度)只进行一次
      if (canModify) {
        this.showMsgCount += this.pageSize;
        this.lastModifyTime = now;
        if (this.msgList.length > this.showMsgCount) {
          // 提取最后this.showMsgCount个元素
          this.showMsgList = this.msgList.slice(-this.showMsgCount);
        } else {
          this.showMsgList = this.msgList;
          this.inloading = false;
        }
        this.$nextTick(() => {
          this.updateLastScrollHeight();
        });
      }
    },
    handleScrollBottom() {
      // 滑动到底部后，showMsgCount缩小至最后一页
      const now = new Date().getTime();
      const canModify = now - this.lastModifyTime > 500;
      if (canModify && this.showMsgList.length > this.pageSize) {
        this.showMsgCount = this.pageSize;
        this.lastModifyTime = now;
        if (this.msgList.length > this.showMsgCount) {
          this.showMsgList = this.msgList.slice(-this.showMsgCount);
        } else {
          this.showMsgList = this.msgList;
        }
        this.$nextTick(() => {
          this.scrollToEnd();
        });
      }
    },
    updateLastScrollHeight() {
      const scrollDom = this.$refs['msg-scroll'];
      if (scrollDom) {
        if (scrollDom.scrollHeight > this.lastScrollHeight && scrollDom.scrollTop <= 200) {
          const addH = scrollDom.scrollHeight - this.lastScrollHeight;
          if (addH >= 200) {
            scrollDom.scrollTop += addH;
            this.inloading = false;
          }
        }
        this.lastScrollHeight = scrollDom.scrollHeight;
      }
    },
    // 发送快捷消息
    onSendQuickMsg(words) {
      // 只有学生才能发送快捷消息
      if (!this.isStudent) {
        return;
      }
      this.sendMsg(words);
    },
  },
};
</script>

<style lang="less">
@import './IMInput.less';

.im-component {
  width: 100%;
  height: 100%;
  transition: 0.5s all;
  display: flex;
  flex-direction: column;
  background-color: transparent;
  padding: 0;

  &.mobile {
    .im-component-footer {
      margin-bottom: 10px;
      width: 100%;

      .im-component-top-tools-line-first {
        display: flex;
        justify-content: space-between;
        padding: 10px 10px 0 10px;
        background-color: rgba(0, 0, 0, .3);

        .chat-setting-icon{
          width: 20px;
          height: 20px;
        }
        .im-component-top-tools-line-first-right{
          flex-grow: 1;
          display: flex;
          flex-direction: row;
          justify-content: end;
          align-items: center;

          .im-component-img-tool {
            width: 20px;
            height: 20px;
            background-size: 20px 20px;
            cursor: pointer;
            position: relative;
            &.image.small-screen{
              margin: 0;
              width: 27px;
              height: 27px;
              background-size: 70% 70%;
            }
          }
        }
      }

      &.small-screen {
        margin: 10px 0 0 0;
        width: 100%;
        height: 108px;
        position:fixed;
        bottom:0;
        z-index: 1;
        .im-component-input-mobile-smallscreen {
          text-align: left;
        }
        .im-component-top-tools {
          height: 55px;
          padding: 10px;
          .im-component-tools-chat {
            flex: 1;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100%;
            border-radius: 10px;
          }
        }
        .im-component-top-tools-line-first {
          display: flex;
          justify-content: space-between;
          padding: 10px 10px 0 10px;
          background-color: rgba(0, 0, 0, .3);
          .im-component-top-tools-line-first-right{
            flex-grow: 1;
            display: flex;
            flex-direction: row;
            justify-content: end;
            align-items: center;

            .im-component-img-tool {
              width: 20px;
              height: 20px;
              background-size: 20px 20px;
              cursor: pointer;
              position: relative;
              &.image.small-screen{
                margin: 0;
                width: 27px;
                height: 27px;
                background-size: 70% 70%;
              }
            }
          }
        }
      }

      &.small-screen-low{
        height: 93px;
      }

      &.show-reply {
        height: 139px;
      }

      &.small-screen-low.show-reply.small-screen {
        height: 124px;
      }

      .im-component-tool {
        margin-right: 4px;
      }
      &.small-screen{
        .im-component-top-tools{
          margin: 0;
        }
      }
      .im-component-top-tools {
        padding: 10px 0;
        display: flex;
        align-items: center;
        justify-content: space-between;
        background-color: rgba(0, 0, 0, .3);
        border-radius: 2px;

        .im-component-tools-left {
          display: flex;
          align-items: center;
          flex-direction: row;
          padding-left: 8px;

          .im-component-tool {
            margin-right: 8px;

            &.small-screen {
              width: 20px;
              height: 20px;
              background-size: 20px 20px;
            }
          }
        }
        .im-component-img-tool {
          width: 20px;
          height: 20px;
          background-size: 20px 20px;
          cursor: pointer;
          position: relative;
          &.image.small-screen{
            width:35px;
            height:35px;
            background-size: 70% 70%;
          }
        }

        .im-component-img-tool input{
          width: 20px;
          height: 20px;
          opacity: 0;
          background-size: 20px 20px;
          cursor: pointer;
        }

        .im-component-img-tool-mobile{
          width: 32px !important;
          height: 32px !important;
          background-size: 32px 32px !important;
          cursor: pointer;
        }

        .im-component-img-tool-mobile input{
          width: 32px !important;
          height: 32px !important;
          opacity: 0;
          background-size: 32px 32px !important;
          cursor: pointer;
        }

        .im-component-tools-right {
          display: flex;
          align-items: center;
          flex-direction: row-reverse;
          padding-right: 0;
          &.small-screen {
            height: 100%;
          }
          .im-component-tool {
            margin-left: 8px;
          }
        }


      }

      .im-component-input-bar {
        flex: 1;
        display: flex;
        flex-direction: row;
        align-items: center;
        justify-content: space-between;
        height: 40px;
        // padding: 4px 4px;
        background-color: #515455;
        border-radius: 2px;

        .im-component-input-mobile {
          flex: 1;
          text-align: center;
          height: 36px;
          margin: 0 8px;
          text-indent: 28px;
          width: 100%;
        }

        .im-component-tool {
          width: 28px;
          height: 28px;
          background-size: 28px 28px;
        }
      }
    }

    .im-component-body {
      width: 100%;
    }
  }

  .im-component-input-mobile {
    resize: none;
    outline: none;
    border: none;
    background: none;
    width: 100%;
    font-size: 20px;
    font-weight: 400;
    color: #ffffff;
    padding-left: 12px;
  }

  .im-component-input-mobile-smallscreen {
    resize: none;
    outline: none;
    border: none;
    background: none;
    width: 100%;
    font-weight: 400;
    color: #8A9099;
    text-align: center;
  }

  .msg-scroll-wrapper {
    position: relative;
    flex: 1;
    overflow: auto;
    width: 100%;
    display: flex;
    flex-direction: column;
  }

  .im-component-body {
    flex: 1;
    overflow: auto;
    padding: 12px 10px 95px 10px;
    display: block;
    .loading-text {
      text-align: center;
      color: #fff;
      line-height: 24px;
    }
    &.show-reply{
      padding: 12px 10px 126px 10px;
    }
    &.no-bottom-padding{
      padding: 12px 10px 10px 10px;
    }
  }

  .new-msg-tip-wrapper {
    position: absolute;
    right: 16px;
    bottom: 98px;

    &.show-reply{
      bottom: 130px;
    }

    &.no-bottom-padding{
      bottom: 16px;
    }
  }

  .im-component-divider {
    margin: 0;
    background-color: rgba(184, 184, 184, 0.1);
  }

  .im-component-footer {
    padding: 0;
    background: var(--foot-color, #00000000);

    .im-component-top-tools {
      display: flex;
      align-items: center;
      justify-content: space-between;

      .im-component-tools-left {
        display: flex;
        align-items: center;
        flex-direction: row;
        padding-left: 16px;

        .im-component-tool {
          margin-right: 8px;
        }
      }

      .im-component-tools-right {
        display: flex;
        align-items: center;
        flex-direction: row-reverse;
        padding-right: 16px;

        .im-component-tool {
          margin-left: 8px;
        }
      }
    }

    .im-component-tool {
      width: 24px;
      background-repeat: no-repeat;
      background-position: 50% 50%;
      cursor: pointer;
      &:hover  {
        --icon-color: #ffffff;
      }

      &.is-disabled {
        opacity: 0.3 !important;
        cursor: not-allowed !important;
      }

      /* &:hover {
        border-radius: 2px;
        border: 1px solid #306FF6;
      }

      &:active {
        border-radius: 2px;
        border: 1px solid #306FF6;
      } */
    }

    .meme {
      background-image: url('./assets/ic_emoji.svg');
      &.small-screen{
        background-image: url('./assets/smile.svg');
      }
      opacity: .9;

      &:hover, &:active {
        border: none;
        opacity: 1;
      }
      &.small-screen {
        background-image: url('./assets/ic-smile.svg');
      }
    }

    .meme-selected {
      background-image: url('./assets/ic_emoji.svg');
      &.small-screen {
        background-image: url('./assets/ic-smile.svg');
      }
    }

    .image {
      &.small-screen{
        background-image: url('./assets/plus.svg');
        margin-left: 10px;
        border-radius: 50%;
      }
      &:hover {
        border-radius: 2px;
        border: 1px solid #c0c4cc57;
      }

      background-image: url('./assets/image.svg');

      &:hover {
        background-image: url('./assets/image_hover.svg');
        border: none
      }

    }

    .im-component-img-tool {
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: auto;
      height: 32px;
      padding: 2px 4px;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s;
      position: relative;

      &:hover {
        background-color: #006EFF;
        --icon-color: #ffffff;
      }

      input {
        position: absolute;
        width: 100%;
        height: 100%;
        opacity: 0;
        cursor: pointer;
      }
    }

    .file-icon {
      width: 16px;
      height: 16px;
      margin: 0;
      background-image: url('./assets/file.svg');
      background-repeat: no-repeat;
      background-size: contain;
    }

    .image-icon,.emoji-icon, .file-icon {
      width: 16px;
      height: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 0;
    }

    .emoji-icon {
      width: 16px;
      height: 16px;
      margin: 0;
      background-repeat: no-repeat;
      background-size: contain;
    }

    .file-text {
      font-size: 12px;
      color: #C5CCDB;
      margin-left: 8px;
      display: none;
      @media (max-width: 1200px) {
        display: none;
      }
    }

    .image-selected {
      &:hover {
        border-radius: 2px;
        border: 1px solid #c0c4cc57;
      }

      background-image: url('./assets/image.svg');

      &:hover {
        background-image: url('./assets/image_hover.svg');
        border: none
      }
    }

    .silence {
      &.small-screen{
        display: none;
      }
      &:hover {
        border-radius: 2px;
        border: 1px solid;
        border-color: #c0c4cc57;
      }

      background-image: url('./assets/ic_msg_disable.svg');
      background-size: 95%;

      &:hover {
        background-image: url('./assets/ic_msg_disable_hover.svg');
        border: none
      }

    }


    .silence-selected {
      &.small-screen {
        display: none;
      }
      &:hover {
        border-radius: 2px;
        border: 1px solid;
        border-color: #c0c4cc57;
      }

      background-image: url('./assets/ic_msg_enable.svg');
      background-size: 95%;

      &:hover {
        background-image: url('./assets/ic_msg_enable_hover.svg');
        border: none
      }
    }

    .im-toast {
      width: 51%;
      position: absolute;
      margin-left: 200px;
      margin-top: -50px;
      background: black;
      padding: 10px;
      border-radius: 5px;
      transform: translate(-50%, -50%);
      animation: show-toast .3s;
      color: white;
      overflow: hidden;
      display: flex;
      align-items: center;
    }
    .im-component-img-input{
      resize: none;
      outline: none;
      border: none;
      background: none;
      width: 100%;
      height: 88px;
      font-size: 20px;
      font-weight: 400;
      color: #ffffff;
      line-height: 28px;
      margin-top: 0;
      padding-left: 16px;
      padding-right: 16px;
      line-break: anywhere;
    }
    .im-component-input {
      position: relative;
      resize: none;
      outline: none;
      border: none;
      background: none;
      width: 100%;
      height: 88px;
      font-size: 16px;
      font-weight: 400;
      color: var(--text-color, #ffffff);
      line-height: 28px;
      margin-top: 0;
      padding-left: 16px;
      padding-right: 16px;
      word-break: break-word;
    }
    .im-component-quick-reply {
      padding: 10px 15px;
    }

    .im-component-reply {
      padding: 0 16px;
    }

    .im-component-bottom-tools {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 10px 15px;

      .im-component-switch-wrap {
        flex: 1;

        .im-component-msg-switch {
          margin-bottom: 15px;

          &:last-child {
            margin-bottom: 0;
          }
        }

        span {
          color: #eee;
          vertical-align: middle;
        }
      }

      .im-component-send-disable {
        width: 48px;
        height: 32px;
        background: #006EFF;
        border-radius: 4px;
        opacity: 0.3;
        cursor: not-allowed;
      }

      .im-component-send {
        width: 48px;
        height: 32px;
        background: #006EFF;
        border-radius: 4px;
        font-size: 14px;
        font-weight: 400;
        color: #FFFFFF;
        line-height: 20px;
        &:disabled {
          width: 48px;
          height: 32px;
          background: #006EFF;
          border-radius: 4px;
          opacity: 0.3;
          cursor: not-allowed;
        }
      }
    }
  }

  .input-image-preview {
    max-height:56px;
    max-width:100px;
    cursor:pointer;
    &:hover {
      opacity: .85;
    }
  }

  .file-preview {
    display: inline-block;
    background-color: rgba(255, 255, 255, 0.1);
    color: white;
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 14px;
    margin: 8px 0;
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    contenteditable: false !important;
  }

  .file-name {
    pointer-events: none;
    user-select: none;
    contenteditable: false !important;
  }

}

.im-component-meme-popover {
  margin-right: -120px !important;
  margin-top: -290px !important;
  padding: 0px !important;
  width: 324px;
  height: 77px;
  transform: scale(0.8);
  border-radius: 10px !important;
  border-color: rgba(184, 184, 184, 0.08) !important;
  //border: 1px solid rgba(184, 184, 184, 0.08);
  background-color: rgb(28, 33, 49);

  .popper__arrow {
    display: none !important;
  }
}

.im-component-meme-popover-smallscreen {
  padding: 0px !important;
  width: 113px;
  height: 38px;
  border-radius: 2px !important;
  border-color: rgb(28, 33, 49) !important;
  background-color: rgb(28, 33, 49) !important;

  .popper__arrow {
    display: none !important;
  }
}

.im-component-meme-popover-mobile {
  margin-right: -110px !important;
  margin-top: -130px !important;
  padding: 0px !important;
  width: 324px;
  height: 77px;
  border-radius: 2px !important;
  border-color: rgb(28, 33, 49) !important;
  background-color: rgb(28, 33, 49) !important;

  .popper__arrow {
    display: none !important;
  }
}
</style>
