<template>
  <div
    :class="['quick-im-component', {
      'small-screen': isSmallScreen
    }]"
  >
    <div
      v-if="quickIMAvailable"
      class="quick-im-component-tools"
      :class="{
        'small-screen': isSmallScreen,
        'disabled': chatDisabled || isSupervisor,
        'record-mode': isRecordMode,
      }"
    >
      <div
        v-show="!hasSideIM"
        :class="['quick-im-component-tools-toggle',
                 isMobile? '' : 'quick-im-component-tools-toggle__blur',
                 active? 'active': 'inactive']"
        :style="isMobile ? { background: 'none' } : {}"
      >
        <IconBulletOpen
          v-if="active"
          :style="isMobile ? { marginTop: '8px' } : {}"
          @click="toggleActive"
        />
        <IconBulletClose
          v-else
          :style="isMobile ? { marginTop: '8px' } : {}"
          @click="toggleActive"
        />
      </div>
      <el-tooltip
        v-show="!hasSideIM"
        effect="light"
        :content="chatDisabledTips"
        placement="right"
        :disabled="!chatDisabled"
        :style="!isPortrait && isMobile ? {width: '80%', background: 'none'}: {}"
      >
        <div
          v-show="active"
          :class="['quick-im-component-tools-input quick-im-component-meme__blur', editFlag ? 'quick-im-component-tools-input-edit' : '', {'small-screen': isMobile}]"
        >
          <div :class="isMobile && !isPad ? 'mobile' : 'pc'">
            <el-popover
              ref="popover"
              :popper-class="isSmallScreen ?
                'quick-im-component-meme-popover-small' : 'quick-im-component-meme-popover'"
              placement="top"
              trigger="click"
              :disabled="chatDisabled || isSupervisor"
              class="quick-im-meme-popver"
              :style="{width: '54px', height: '100%'}"
              @show="showEmoji"
              @hide="hideEmoji"
            >
              <div
                slot="reference"
                class="position-z"
                :class="[
                  'quick-im-component-meme',
                  {
                    'small-screen': isSmallScreen,
                    'is-disabled': chatDisabled || isSupervisor,
                  },
                  !emojiSelected ? 'meme': 'meme-selected',
                ]"
              />
              <Emoji
                ref="emoji"
                :is-closed="emojiClosed"
                @send-emoji="sendEmoji"
              />
            </el-popover>
            <div
              :class="['quick-im-component-input',
                       editFlag ? 'quick-im-component-input-focus' : 'quick-im-component-input-blur']"
              :data-value="inputText"
            >
              <InputElement
                id="inputTextData"
                ref="input"
                size="1"
                class="im-component-input"
                :disabled="chatDisabled || isSupervisor"
                :use-textarea-input="!isMobile && !isPad"
                @enter="textInputEnter"
                @click="textInputClick"
                @change="textChange"
                @focus="textGetFocus"
                @blur="textLoseFocus"
                @mouseover.native="onMouseInEdit"
                @mouseout.native="onMouseOutEdit"
              />
            </div>
          </div>
          <div class="position-z">
            <el-button
              v-if="editFlag"
              :class="['im-component-input-send',
                       inputEmpty ? 'im-component-input-send-disable' : '']"
              type="primary"
              :disabled="inputEmpty"
              @click="textInputEnter"
              @mouseover.native="onMouseSendOver"
              @mouseout.native="onMouseSendLeave"
            >
              {{ $t('发送') }}
            </el-button>
          </div>
        </div>
      </el-tooltip>
      <!-- 举手相关操作 -->
      <div
        v-if="handUpAvailable"
        :style="{'height': '100%'}"
      >
        <HandUpIcon
          :is-class-start="isClassStarted"
          class="quick-im-component-tools-handup"
          :style="{'height': '100%', 'display': 'block'}"
        />
      </div>
    </div>
  </div>
</template>

<script>
import Constant from '@/util/Constant';
import Emoji from './Emoji.vue';
import { IMBaseComponent } from './IMBase';
import InputElement from './InputElement.vue';
import HandUpIcon from '../hand-up-member-component/HandUpIcon.vue';
import IconBulletOpen from './assets/svg-component/ic_bullet_open.svg';
import IconBulletClose from './assets/svg-component/ic_bullet_close.svg';

export default {
  components: {
    InputElement,
    Emoji,
    HandUpIcon,
    IconBulletOpen,
    IconBulletClose,
  },
  extends: IMBaseComponent,

  data() {
    return {
      active: true,               // 当前是否处于活跃状态
      emojiSelected: false,       // 表情按钮处于选中状态
      inputText: '',
      showLiveCommenting: true,   // 显示弹幕消息
      chatDisabled: false,        // 禁止发送消息
      chatDisabledTips: '',       // 禁止发送消息时的提示
      isRecordMode: false,        // 当前是否录制模式
      isClassStarted: false,      // 是否已开始上课
      msgList: [],                // 消息列表
      editFlag: false, // 编辑标识符
      inputEmpty: true, // 输入内容的长度 >0 false
      quickMsgVisible: true, // im和弹幕互斥显示
      echoText: '', // 回显内容
      overSendButton: false,
      mouseOverEdit: false,
      showHandUp: false, // 上课才能显示举手相关操作
      emojiClosed: false,
      isLiveClass: false, // 是否直播课
      isOneOnOneClass: false, // 是否1v1课堂
      isUnitedLiveClass: false,
      isPad: TCIC.SDK.instance.isPad(),
      mouseMoveTask: null,
      studentShowJoinExitMsg: true, // 学生是否显示进退房信息
      showJoinExitMsg: true, // 是否显示进退房信息
      inputShowHandUp: true,
      isShareScreen: false,
      hasSideIM: false, // 有侧边栏
      isPortrait: true,
      isStageUp: false,
      isMobile: false,
      isWeb: false,
      maxRtcMember: 0,
    };
  },

  computed: {
    quickIMAvailable() {
      // IM可用:
      // 1. 直播课屏幕分享时 (老师）
      // 2. 直播课（学生）
      // 3. 互动课且最大连麦人数>0（学生+老师）
      return (this.isLiveClass && this.isShareScreen)
        || (this.isLiveClass && !this.isTeacher && this.isMobile && !this.isPad)
        || !this.isLiveClass;
    },
    handUpAvailable() {
      // 台下观众不能举手
      if (TCIC.SDK.instance.isRoundTable() && TCIC.SDK.instance.isStudent() && !TCIC.SDK.instance.isRoundTableGuest()) {
        return false;
      }
      // 举手可用
      return this.showHandUp && !this.isLiveClass  && !this.isUnitedLiveClass && this.maxRtcMember > 0;
    },
  },

  watch: {
    isShareScreen(val) {
      if (this.isLiveClass) {
        TCIC.SDK.instance.updateComponent('quickmsg-show-component', {
          display: val ? 'block' : 'none',
        });
        const isPlayVodDoc = TCIC.SDK.instance.getState(Constant.TStateVodPlayerVisible, false);
        const isSubCamera = TCIC.SDK.instance.getState(Constant.TStateStartSubCamera, false);
        this.toggleActive(val && !isPlayVodDoc && !isSubCamera);
      }
    },
  },

  mounted() {
    window.onblur = (() => {
      if (this.$refs.popover) {
        this.$refs.popover.doClose();
        this.emojiClosed = true;
      }
    });
    // 监听课堂开始
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      this.isClassStarted = status > TCIC.TClassStatus.Not_Start;
      this.showHandUp = status === TCIC.TClassStatus.Already_Start;
    });
    this.addLifecycleTCICStateListener(Constant.TStateShowChatBox, (isVisible) => {
      this.quickMsgVisible = !isVisible;
    });
    this.addLifecycleTCICStateListener(Constant.TStateRecordMode, (recordMode) => {
      this.isRecordMode = recordMode;
    });
    this.addLifecycleTCICStateListener(Constant.TStateShowLiveCommenting, (show) => {
      this.showLiveCommenting = show;
    });
    this.addLifecycleTCICStateListener(Constant.TStateDisableChat, (disabled) => {
      this.chatDisabled = disabled;
    });
    this.addLifecycleTCICStateListener(Constant.TStateDisableChatTips, (tips) => {
      this.chatDisabledTips = tips;
    });
    this.makeSureClassJoined(() => {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const lastActive = window.localStorage.getItem(`${classInfo.classId}_${TCIC.SDK.instance.getUserId()}_quick_im_active`);
      if (lastActive) {
        this.active = (lastActive === 'true');
      }
      TCIC.SDK.instance.setState(Constant.TStateChatTipsEnable, this.active);
      if (TCIC.SDK.instance.isLiveClass() && !this.isSmallScreen) {   // 公开课大屏关闭弹幕
        if (!this.isShareScreen) {
          this.toggleActive(false);
        }
      }
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isLiveClass = TCIC.SDK.instance.isLiveClass();
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
      this.isUnitedLiveClass = TCIC.SDK.instance.isUnitedLiveClass();
      this.showJoinExitSwitch = TCIC.SDK.instance.isFeatureAvailable('JoinExitSwitch');
      this.studentShowJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('StudentShowJoinExitMsg');
      this.showJoinExitMsg = TCIC.SDK.instance.isFeatureAvailable('ShowJoinExitMsg');
      this.isMobile = TCIC.SDK.instance.isMobile() && !this.isPad;
      this.isWeb = TCIC.SDK.instance.isWeb();
      this.maxRtcMember = classInfo.maxRtcMember;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, (value) => {
      if (TCIC.SDK.instance.isElectron()) {
        // 只在桌面端启用拖动
        this.isShareScreen  =  value < 2;
        if (value === 0) {  // 进入屏幕分享后允许拖动
          this.toggleComponentDrag(true, '.quick-im-component');
        } else if (value === 2) {  // 停止屏幕分享后，禁止拖动，并且恢复层级
          this.toggleComponentDrag(false, '.quick-im-component');
          this.updateComponent({
            zIndex: 450,
          });
        }
      }
    });
    if (!this.isSmallScreen) {
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
        this.hasSideIM = TCIC.SDK.instance.getClassLayoutMainFrame(layout).sideIM;
      });
    }
    this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (val) => {
      this.isStageUp = val;
    });
  },

  methods: {
    toggleActive(active) {
      if (typeof active !== 'boolean') {
        active = !this.active;
      }
      this.active = active;
      TCIC.SDK.instance.setState(Constant.TStateChatTipsEnable, this.active);
      if (this.active) {
        if (this.isVisible) {
          // 组件处于可见状态时弹幕切换到活动状态直接标记所有消息为已读
          TCIC.SDK.instance.markAllMessagesAsRead();
        }
      } else {
        // 弹幕切换到非活动状态时同时关闭表情弹窗
        if (this.$refs.popover) {
          this.$refs.popover.doClose();
          this.emojiClosed = true;
        }
      }
      this.makeSureClassJoined(() => {
        const classInfo = TCIC.SDK.instance.getClassInfo();
        window.localStorage.setItem(`${classInfo.classId}_${TCIC.SDK.instance.getUserId()}_quick_im_active`, this.active);
        TCIC.SDK.instance.setState(Constant.TStateChatTipsEnable, this.active);
      });
    },

    onComponentVisibilityChange(visible) {
      if (this.active && visible) {
        // 弹幕处于活动状态时组件显示出来后直接标记所有消息为已读
        TCIC.SDK.instance.markAllMessagesAsRead();
      }
    },

    onRecvMsg(msg) {
      if (this.active && this.isVisible) {
        // 弹幕处于活动状态且组件处于可见状态时直接标记收到的消息为已读
        TCIC.SDK.instance.markMessageAsRead(msg.seq);
      }
    },

    textInputEnter() {
      // 某些Android机点发送时,可能会触发native的后退键事件,弹出离开课堂
      TCIC.SDK.instance.ignoreBackPressed(true);
      this.sendMsg(this.$refs.input.getPureText())
        .then(() => {
          this.editFlag = false;
          this.$emit('on-edit-status-change', false);
          this.inputEmpty = true;
          this.$refs.input.clearText();
          this.$refs.input.blur();
          this.echoText = '';
          this.inputShowHandUp = true;
          setTimeout(() => {
            TCIC.SDK.instance.ignoreBackPressed(false);
          }, 2000);
        });
    },

    clearText() {
      this.$refs.input.clearText();
    },

    textInputClick(event) {
      if ((this.isMobile || TCIC.SDK.instance.isPad()) && !this.chatDisabled && !this.isWeb) {
        TCIC.SDK.instance.getComponent('mobile-im-input-bar-component')
          .getVueInstance()
          .showTextInput(this);
      }
      if (document.getElementById('inputTextData').offsetWidth > 90) {
        if (TCIC.SDK.instance.isMobile()) {
          // 手机上弹出虚拟键盘，可不处理
          this.inputShowHandUp = true;
        } else {
          this.inputShowHandUp = false;
        }
      } else {
        this.inputShowHandUp = true;
      }
    },

    textChange() {
      TCIC.SDK.instance.ignoreBackPressed(true);
      this.inputText = this.$refs.input.inputText;
      if (this.inputText.length > 0) {
        this.inputEmpty = false;
      } else {
        this.inputEmpty = true;
      }

      if (document.getElementById('inputTextData').offsetWidth > 90 && !this.inputEmpty) {
        if (TCIC.SDK.instance.isMobile()) {
          // 手机上弹出虚拟键盘，可不处理
          this.inputShowHandUp = true;
        } else {
          this.inputShowHandUp = false;
        }
      } else {
        this.inputShowHandUp = true;
      }
    },

    mobileInputTextChange(text) {
      this.$refs.input.setText(text);
    },

    textGetFocus() {
      document.addEventListener('mousedown', this.onEventHandler, true);
      document.addEventListener('touchstart', this.onEventHandler, true);
      if (!(this.isMobile || this.isPad)) {
        this.editFlag = true;
      }
      this.$emit('on-edit-status-change', true);
      this.$refs.input.setText(this.echoText);
      TCIC.SDK.instance.setState(Constant.TStateQucikIMEdit, true);
    },

    textLoseFocus() {
      if (!this.overSendButton) {   // 失去焦点后若是在发送按钮上则不更新布局，避免发送按钮点不到
        this.inputShowHandUp = true;
      }
      this.echoText = this.$refs.input.inputText;
      if (this.inputText.length === 0) {
        this.editFlag = false;
        this.$emit('on-edit-status-change', false);
        this.inputEmpty = true;
        this.$refs.input.clearText();
      }
      document.removeEventListener('mousedown', this.onEventHandler, true);
      document.removeEventListener('touchstart', this.onEventHandler, true);
      TCIC.SDK.instance.setState(Constant.TStateQucikIMEdit, false);
    },

    emojiInputClick(event) {
    },

    sendEmoji(emojiId) {
      this.sendMsg(emojiId);
      this.closeEmoji();
    },

    closeEmoji() {
      if (this.$refs.popover) {
        this.$refs.popover.doClose();
        this.emojiClosed = true;
      }
    },

    showEmoji() {
      this.emojiSelected = true;
      this.emojiClosed = false;
    },

    hideEmoji() {
      this.emojiSelected = false;
      this.emojiClosed = true;
    },

    onMouseSendOver() {
      this.overSendButton = true;
    },

    onMouseSendLeave() {
      this.overSendButton = false;
    },

    onEventHandler(event) {
      if (this.inputEmpty) {
        this.overSendButton = false;
      }
      if (!this.mouseOverEdit && !this.overSendButton) {
        this.$refs.input.blur();
      }
    },

    onMouseInEdit() {
      this.mouseOverEdit = true;
    },

    onMouseOutEdit() {
      this.mouseOverEdit = false;
    },
  },
};
</script>

<style lang="less">

@quick-im-base-width: var(--quick-im-base-width, 40px);
@quick-im-base-height: var(--quick-im-base-height, 42px);

.quick-im-component {
  //width: 100%;
  height: 100%;
  transition: 0.5s all;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  justify-content: flex-end;
  pointer-events: none;

  &.small-screen {
    width: auto;
  }

  .quick-im-component-msg-list {
    padding-bottom: 10px;
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    justify-content: flex-end;
    max-width: 535px;

    &-enter-active,
    &-leave-active {
      transition: all 1s;
    }

    &-enter {
      opacity: 0;
      transform: translateY(30px);
    }

    &-leave-to {
      opacity: 0;
      transform: translateY(-30px);
    }
  }

  .quick-im-component-tools {
    position: relative;
    z-index: 99;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    flex-shrink: 0;

    &.small-screen {
      border-radius: 8px;
      background-color: rgba(120, 120, 122, 0.5);
    }

    &.disabled {
      .quick-im-component-tools-toggle {
        opacity: .5;
      }

      .quick-im-component-tools-input {
        opacity: .5;

        .quick-im-component-meme:hover {
          border: none;
        }
      }
    }

    &.record-mode {
      display: none;
    }

    .quick-im-component-tools-handup {
      .handup-icon {
        &.small-screen{
          width: 24px;
          height: 24px;
        }
      }
    }

    .quick-im-component-tools-toggle {
      position: relative;
      cursor: pointer;
      width: 15%;
      height: 100%;
      max-width: @quick-im-base-width;
      max-height: @quick-im-base-height;
      border-radius: 10px;
      flex-shrink: 0;
      pointer-events: auto;
      overflow: hidden;

      svg {
        position: relative;
        z-index: 9;
        display: flex;
        width: 90%;
        height: 90%;
        background-repeat: no-repeat;
        background-position: 50%;
        margin: auto;
        background-size: 100%;
      }

      &.quick-im-component-tools-toggle__blur {
        transition: opacity 1s;
        transition-delay: 3s;

        &:hover {
          opacity: 1;
          transition: opacity 0s;
        }
      }
    }

    .quick-im-component-tools-input-edit {
      padding-right: 16px;
    }

    .quick-im-component-tools-input {
      position: relative;
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: flex-start;
      margin-left: 8px;
      /* max-width: 100%;
      height: 48px;
      border-radius: 24px;*/
      // min-width: 164px;
      width: 60%;
      height: 100%;
      max-width: 250px;
      max-height: @quick-im-base-height;
      border-radius: 4px;
      //background-color: rgba(0, 0, 0, 0.3);
      //box-shadow: 0 4px 10px 0 rgba(0, 0, 0, 0.1);
      background: var(--input-bg-color, #2A334E); // V1.5.0
      //background: linear-gradient(141deg, rgba(21, 27, 48, 0.7) 0%, rgba(28, 33, 49, 0.9) 100%); // V1.5.0
      box-shadow: 0 0 3px 0 rgba(32, 32, 32, 0.4);
      pointer-events: auto;
      overflow: hidden;
      &.small-screen {
        width: 80% !important;
        background: none;
        max-width: none;
      }

      &.quick-im-component-meme__blur {
        transition: opacity 1s;
        transition-delay: 3s;

        &:hover {
          opacity: 1;
          transition: opacity 0s;
        }
      }

      .im-component-input-send-disable {
        width: 48px;
        height: 32px;
        color: #fff;
        background: #006EFF;
        border-radius: 4px;
        border-color: #006EFF;
        opacity: 0.3
      }

      .im-component-input-send {
        margin: 0px !important;
        padding: 0px !important;
        width: 48px;
        height: 32px;
        border-radius: 4px;
        text-align: center;
      }

      .quick-im-component-input-blur {
        width: 76px;
      }

      .quick-im-component-input-focus {
        width: 62px;
      }

      .quick-im-component-input {
        position: relative;
        z-index: 9;
        margin-left: 4px;
        margin-right: 4px;
        display: inline-grid;
        vertical-align: top;
        align-items: center;
        flex-grow: 1;
        // height: 100%;
        // min-width: 98px;
        max-width: 250px;

        &::after,
        input {
          color: #FFFFFF;
          resize: none;
          background: none;
          appearance: none;
          border: none;
          outline: none;
          margin: 0;
          padding: 0;
          width: auto;
          min-width: 1em;
          font-size: 14px;
          font-weight: 400;
          height: 32px;
          line-height: 32px;
        }

        input::placeholder {
          color: rgba(#FFFFFF, .3);
        }

        &::after {
          content: attr(data-value);
          visibility: hidden;
          height: 0;
          min-width: 48px;
          white-space: pre-wrap;
        }
      }

      .quick-im-component-meme {
        margin: 0 7px;
        width: 100%;
        height: 100%;
        max-width: 40px;
        max-height: 40px;
        // margin: 0 8px;
        //background-repeat: no-repeat;
        //background-position: 50% 50%;
        cursor: pointer;
        background-size: 100%;
        background-repeat: no-repeat;
        background-position: 50%;

        /* &:active {
          border-radius: 2px;
          border: 1px solid #306FF6;
        } */

        &.meme {
          background-image: url('./assets/ic_emoji.svg');
        }

        &.meme-selected {
          background-image: url('./assets/ic_emoji.svg');
        }
        &.small-screen {
          background-image: url('./assets/ic-smile.svg')
        }

        &.is-disabled {
          opacity: 0.5 !important;
          cursor: not-allowed !important;
        }
      }
    }
  }
}

// .el-popper[x-placement^=top] {
//     margin-bottom: 32px !important;
// }

// 小图标，表情面板
.quick-im-component-meme-popover {
  margin-left: 20px;
  margin-top: -145px !important;
  padding: 0px !important;
  width: 324px;
  height: 77px;
  border-radius: 4px !important;
  border-color: var(--pop-bg-color, rgb(28, 33, 49)) !important;
  background-color: var(--pop-bg-color, rgb(28, 33, 49)) !important;

  .popper__arrow {
    display: none !important;
  }
}

.quick-im-component-meme-popover-small {
  padding: 0px !important;
  width: 113px;
  height: 38px;
  border-radius: 2px !important;
  border-color: rgb(28, 33, 49) !important;
  background-color: rgb(28, 33, 49) !important;

  .popper__arrow {
    display: none !important;
  }
}

.position-z {
  position: relative;
  z-index: 9;
}
.mobile {
  display: flex;
  background: none;
  // padding: 10px;
  justify-content: center;
  align-items: center;
  border-radius: 15px;
  .handup-icon__wrap {
    background: rgb(41, 45, 56);
    border-radius: 5px;
  }
}
.pc {
  display: flex;
  align-items: center;
  width: 100%;
  height: 100%;
}
@media (max-width: 485px) {
  .quick-im-meme-popver {
    display: none;
  }
  .quick-im-component .quick-im-component-tools .quick-im-component-tools-input {
    height: 50%;
    .quick-im-component-input input {
      font-size: 12px;
    }

  }
}
</style>
