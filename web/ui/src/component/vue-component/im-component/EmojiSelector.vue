<template>
  <el-popover
    ref="popover"
    v-model="emojiOpen"
    :popper-class="popoverClass"
    :visible-arrow="false"
    placement="top-start"
    :width="isSmallScreen ? 150 : 226"
    trigger="click"
    :disabled="disabled"
  >
    <i
      slot="reference"
      class="ic-emoji"
      :class="disabled ? 'is-disabled' : ''"
    >
      <IconEmoji />
      <span v-if="false && !isMobile && !isPad">{{ $t('表情') }}</span>
    </i>
    <div
      class="emoji-popper-content"
      :class="isSmallScreen ? 'small-screen' : ''"
    >
      <i
        class="ic-emoji-01"

        @click="sendEmoji('[鼓掌]')"
      />
      <i
        class="ic-emoji-02"
        @click="sendEmoji('[强]')"
      />
      <i
        class="ic-emoji-03"
        @click="sendEmoji('[玫瑰]')"
      />
      <i
        class="ic-emoji-04"
        @click="sendEmoji('[爱心]')"
      />
    </div>
  </el-popover>
</template>

<script>
import IconEmoji from './assets/svg-component/emoji.svg';

export default {
  name: 'EmojiSelector',
  components: {
    IconEmoji,
  },
  props: {
    disabled: {
      type: Boolean,
      default: false,
    },
  },
  data() {
    return {
      isPad: TCIC.SDK.instance.isPad(),
      isMobile: TCIC.SDK.instance.isMobile(),
      emojiOpen: false,
    };
  },
  computed: {
    popoverClass() {
      if (!this.isPad && !this.isMobile) return 'emoji-popper client-black';
      if (this.isPad) return 'emoji-popper pad-black';
      return 'emoji-popper';
    },
    isSmallScreen() {
      return this.isMobile && !this.isPad;
    },
  },
  watch: {
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    sendEmoji(emoji) {
      this.$emit('send-emoji', emoji);
      this.emojiOpen = false;
    },
  },
};
</script>
<style lang="less">
.icon-emoji {
  display: flex;
  align-items: center;
  justify-content: center;
}
.ic-emoji.is-disabled {
  opacity: 0.3 !important;
  cursor: not-allowed !important;
}
.emoji-popper .emoji-popper-content.small-screen{
  i {
    width: 26px;
    height: 26px;
    background-size: 26px !important;
  }
  //transform: scale(.8);
}
</style>
