<?xml version="1.0" encoding="UTF-8"?>
<svg width="24px" height="24px" viewBox="0 0 24 24" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <title>ic_xlsx</title>
    <defs>
        <polygon id="path-1" points="12.0204601 0 12.0204601 4.06160062 16 4.06160062"></polygon>
        <filter x="-75.4%" y="-49.2%" width="200.5%" height="198.5%" filterUnits="objectBoundingBox" id="filter-2">
            <feOffset dx="-1" dy="0" in="SourceAlpha" result="shadowOffsetOuter1"></feOffset>
            <feGaussianBlur stdDeviation="0.5" in="shadowOffsetOuter1" result="shadowBlurOuter1"></feGaussianBlur>
            <feColorMatrix values="0 0 0 0 0   0 0 0 0 0   0 0 0 0 0  0 0 0 0.15 0" type="matrix" in="shadowBlurOuter1"></feColorMatrix>
        </filter>
    </defs>
    <g id="音视频播放" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="图标汇总" transform="translate(-107.000000, -117.000000)">
            <g id="ic_list_XLS" transform="translate(107.000000, 117.000000)">
                <rect id="矩形" x="0" y="0" width="24" height="24"></rect>
                <g id="编组-5" transform="translate(4.000000, 2.000000)">
                    <path d="M2,0 L11.95578,0 L11.95578,0 L16,4.06160062 L16,18 C16,19.1045695 15.1045695,20 14,20 L2,20 C0.8954305,20 1.3527075e-16,19.1045695 0,18 L0,2 C-1.3527075e-16,0.8954305 0.8954305,2.02906125e-16 2,0 Z" id="矩形" fill="#217346"></path>
                    <svg xmlns="http://www.w3.org/2000/svg"><text x="33.1%" y="47%" font-size="10" font-weight="800" fill="#ffffff" font-family="system-ui, sans-serif" text-anchor="middle" dominant-baseline="middle">X</text></svg>
                    <g id="路径-29">
                        <use fill="black" fill-opacity="1" filter="url(#filter-2)" xlink:href="#path-1"></use>
                        <use fill="#217346" fill-rule="evenodd" xlink:href="#path-1"></use>
                    </g>
                </g>
            </g>
        </g>
    </g>
</svg>