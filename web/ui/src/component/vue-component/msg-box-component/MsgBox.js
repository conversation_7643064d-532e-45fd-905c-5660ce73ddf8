import Vue from 'vue';
import MsgBox from './MsgBox.vue';
import ClassRate from './ClassRate.vue';
import CollegeMsgBox from './CollegeMsgBox.vue';
import CoTeachingMsgBox from './CoTeachingMsgBox.vue';
import { TCICCustomUI } from '../../../pages/class/customUI';
import Constant from '../../../util/Constant';
import LoadingBox from "@/component/vue-component/msg-box-component/LoadingBox.vue";

TCIC.SDK.registerComponent('msg-box-component', MsgBox);
Vue.customElement('msg-box-component', MsgBox);
TCIC.SDK.registerComponent('loading-box-component', LoadingBox);
Vue.customElement('loading-box-component', LoadingBox);
TCIC.SDK.registerComponent('class-rate-component', ClassRate);
Vue.customElement('class-rate-component', ClassRate);
TCIC.SDK.registerComponent('college-msg-box-component', CollegeMsgBox);
Vue.customElement('college-msg-box-component', CollegeMsgBox);
TCIC.SDK.registerComponent('co-teaching-msg-box-component', CoTeachingMsgBox);
Vue.customElement('co-teaching-msg-box-component', CoTeachingMsgBox);

// 课后评分
TCICCustomUI.hooks(TCICCustomUI.THookType.MsgBox_ClassEnded_BeforeShow).tap((event) => {
  const classInfo = TCIC.SDK.instance.getClassInfo();
  if (classInfo.gradingAfterClass && TCIC.SDK.instance.isStudent()) {
    // 阻止弹出"课堂已结束"提示框
    event.cancel();

    // 展示课后评分组件
    TCIC.SDK.instance.loadComponent('class-rate-component', {
      width: '100%',
      height: '100%',
      top: '50%',
      left: '50%',
      zIndex: 30000,
      display: 'block',
    }, null)
      .then((msgBox) => {
        if (TCIC.SDK.instance.isCollegeClass()) {
          // 取消透传点击
          TCIC.SDK.instance.setState(Constant.TStatePassThroughStatus, false);
        }
        msgBox.getVueInstance().show();
      });
  }
}, -100);
