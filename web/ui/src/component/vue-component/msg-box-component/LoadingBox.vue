<template>
  <div class="loading-box-component">
    <el-dialog
      :visible.sync="dialogVisible"
      width="420px"
      top="0"
      class="loading-box-dialog"
      :show-close="false"
      :close-on-click-modal="false"
      :before-close="handleClose"
      destroy-on-close
    >
      <div class="loading-header">
        <div class="loading-bar">
          <div class="bar-progress" />
        </div>
      </div>

      <div class="loading-message">
        {{ message }}
      </div>

      <div
        v-if="buttons.length > 0"
        slot="footer"
        class="dialog-footer"
      >
        <el-button
          v-for="(button, index) in buttons"
          :key="index"
          :type="index === 0 ? 'primary' : 'secondary'"
          @click="close(index)"
        >
          {{ button }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import BaseComponent from '../../core/BaseComponent';

export default {
  name: 'LoadingBox',
  extends: BaseComponent,
  data() {
    return {
      dialogVisible: false,
      message: '',
      buttons: [],
      callback: null,
      dialogWinId: -1,
    };
  },
  methods: {
    show(info) {
      this.message = info.message;
      this.buttons = info.buttons || [];
      this.callback = info.callback;
      this.dialogVisible = true;
      this.$nextTick(() => {
        const dlgWinName = `loadingBox-${Date.now()}-${Math.floor(Math.random() * 1024)}`;
        this.dialogWinId = window.tbm.pushWindow(dlgWinName);
        const btns = Array.from(this.$el.querySelectorAll('.el-button')).map(btn => window.tbm.generateNode(btn));
        window.tbm.pushTarget('loadingBox', btns, 'content', this.dialogWinId);
        window.tbm.activeWindow(this.dialogWinId, 'loadingBox', 'content');
      });
    },
    close(index) {
      this.dialogVisible = false;
      window.tbm.removeWindow(this.dialogWinId);
      if (this.callback) {
        this.callback(index);
      }
    },
    handleClose(done) {
      done();
      this.close(-1);
    },
  },
};
</script>

<style lang="less">
.loading-box-component {
  .loading-box-dialog {
    display: flex;
    align-items: center;
    .el-dialog{

    }
    .el-dialog__header {
      padding: 0;
    }

    .loading-header {
      padding: 0 25px;
      .loading-bar {
        width: 100%;
        height: 4px;
        background-color: #d0e3ff;
        overflow: hidden;
        border-radius: 2px;
        .bar-progress {
          height: 100%;
          width: 100%;
          background-color: #007AFF;
          animation: loading-stripe 2s linear infinite;
        }
      }
    }

    .loading-message {
      padding: 0 25px;
      text-align: center;
      font-size: 12px;
      color: #555;
      margin: 24px 0 0 0;
    }

    .el-dialog__footer {
      padding: 0 25px 25px 25px;
      text-align: center;
      .el-button {
        padding: 8px 19px;
        font-size: 12px;
        background-color: #FFF;
        border-color: #CFD4E6;
        color: #006EFF;
      }

      .el-button:hover {
        background-color: #ebeef2;
        border-color: #cfd5de;
      }

      .el-button--primary {
        background-color: #006EFF;
        border-color: #006EFF;
        color: #FFFFFF;
      }

      .el-button--secondary {
        background-color: #FFF;
        border-color: #CFD4E6;
        color: #006EFF;
      }

      .el-button--secondary:hover {
        background-color: #ebeef2;
        border-color: #cfd5de;
      }

      .el-button--primary:hover {
        background-color: #338bff;
        border-color: #338bff;
        color: #FFFFFF;
      }
    }
  }

  @keyframes loading-stripe {
    0% {
      transform: translateX(-100%);
    }
    100% {
      transform: translateX(100%);
    }
  }
}
</style>
