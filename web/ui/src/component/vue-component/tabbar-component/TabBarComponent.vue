<template>
  <div
    v-if="isPortrait"
    class="tabbar-component"
  >
    <section
      class="tabbar-item"
      @click="toggleMic"
    >
      <i :class="['tabbar-item-icon', isMicOpen ? 'icon_mic_open' : 'icon_mic_close']" />
      <div class="tabbar-item-title">
        {{ isMicOpen ? $t('静音') : $t('解除静音') }}
      </div>
    </section>

    <section
      class="tabbar-item"
      @click="toggleCamera"
    >
      <i
        class="tabbar-item-icon"
        :class="isCameraOpen ? 'icon_camera_open' : 'icon_camera_close'"
      />
      <div class="tabbar-item-title">
        {{ isCameraOpen ? $t('关闭视频') : $t('打开视频') }}
      </div>
    </section>

    <section class="tabbar-item">
      <Message
        :show-text="false"
        :icon-style="iconStyle"
      />
      <div class="tabbar-item-title">
        {{ $t('消息') }}
      </div>
    </section>

    <section
      v-if="!isStudent"
      class="tabbar-item"
    >
      <Member
        :component="menu.find( item => item.name === 'member')"
        :show-text="false"
        :icon-style="iconStyle"
      />
      <div class="tabbar-item-title">
        {{ roomInfo.memberList }}
      </div>
    </section>

    <!-- <section
      v-if="isStudent"
      class="tabbar-item"
    >
      <Notice
        :component="menu.find( item => item.name === 'notice')"
        @icon-click="drawerShow = false"
      />
    </section>

    <section
      v-if="isStudent"
      class="tabbar-item"
    >
      <Setting
        @icon-click="drawerShow = false"
      />
    </section> -->

    <section
      class="tabbar-item"
      @click="drawerShow = true"
    >
      <i
        class="tabbar-item-icon"
        :class="['icon_more']"
      />
      <div
        class="tabbar-item-title"
      >
        {{ $t('更多') }}
      </div>
    </section>
    <el-drawer
      :visible.sync="drawerShow"
      :modal-append-to-body="true"
      :append-to-body="true"
      custom-class="tab-bar-drawer"
      :wrapper-closable="true"
      :with-header="false"
      :size="90"
      direction="btt"
    >
      <Notice
        :component="menu.find( item => item.name === 'notice')"
        @icon-click="drawerShow = false"
      />
      <Setting
        @icon-click="drawerShow = false"
      />
    </el-drawer>
  </div>
</template>

<script>
import HeaderBase from '@/component/vue-component/header-component/HeaderBase.js';
import Message from '@/component/vue-component/header-component/sub-component/Message';
import Notice from '@/component/vue-component/header-component/sub-component/Notice';
import Member from '@/component/vue-component/header-component/sub-component/Member';
import Setting from '@/component/vue-component/header-component/sub-component/Setting';

export default {
  components: {
    Message,
    Notice,
    Member,
    Setting,
  },
  extends: HeaderBase,
  data() {
    return {
      isStudent: TCIC.SDK.instance.isStudent(),
      isCameraOpen: false,
      isMicOpen: false,
      isPortrait: false, // 是否为竖屏
      drawerShow: false,
      iconStyle: { width: '32px', height: '32px', backgroundSize: '100%', marginBottom: '5px', borderRadius: 0 },
      roleInfo: {},
      roomInfo: {},
    };
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;

    const DeviceOrientation = TCIC.TMainState.Device_Orientation;
    const Portrait = TCIC.TDeviceOrientation.Portrait;
    this.isPortrait = TCIC.SDK.instance.getState(DeviceOrientation, Portrait) === Portrait;
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
    });
    this.isCameraOpen = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Capture);
    this.isMicOpen = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
    this.$EventBus.$on('tabbar-mic-status', (value) => {
      this.isMicOpen = value;
    });
    this.$EventBus.$on('tabbar-camera-status', (value) => {
      this.isCameraOpen = value;
    });
  },
  methods: {
    toggleMic() {
      if (this.isStudent) {
        const studentCom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
        if (studentCom) {
          studentCom.getVueInstance()
            .enableMic(!this.isMicOpen);
        }
      } else {
        const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
        if (teacherCom) {
          teacherCom.getVueInstance().videoVueInstance()
            .toggleMic();
        }
      }
    },
    toggleCamera() {
      if (this.isStudent) {
        const studentCom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
        if (studentCom) {
          studentCom.getVueInstance()
            .enableCamera(!this.isCameraOpen);
        }
      } else {
        const teacherCom = TCIC.SDK.instance.getComponent('teacher-component');
        if (teacherCom) {
          teacherCom.getVueInstance().videoVueInstance()
            .toggleCamera();
        }
      }
    },
  },
};
</script>

<style lang="less">
.tabbar-component{
  background: #0D1015;
  color: #fff;
  display: flex;
  justify-content: space-between;
  padding-top: 18px;
  height: 90px;
  bottom: 0;
  .tabbar-item{
    -webkit-text-size-adjust:none;
    width: 50px;
    text-align: center;
    .icon-message{
      margin-top: 0!important;
    }
    .side-bar-btn .icon-member.mobile{
      transform: scale(1);
    }
    &-icon{
      height: 32px;
      width: 32px;
      display: block;
      margin: 0 auto 5px;
      background-repeat: no-repeat;
      background-size: contain;
      &.icon_mic_open{
        background-image: url(../video-component/assets/video-ctrl/mobile/mic-open.svg);
      }

      &.icon_mic_close{
        background-image: url(../video-component/assets/video-ctrl/mobile/mic-close.svg);
      }
      &.icon_camera_open{
        background-image: url(../video-component/assets/video-ctrl/mobile/camera-open.svg);
      }
      &.icon_camera_close{
        background-image: url(../video-component/assets/video-ctrl/mobile/camera-close.svg);
      }
      &.icon_more{
        background-image: url(../header-component/assets/ic_navigation_more.svg);
      }
    }
    &-title{
      font-weight: 400;
      font-size: 10px;
      line-height: 14px;
      white-space: nowrap;
    }
  }
}
.tab-bar-drawer{
  .el-drawer__body{
    background-color: #0D1015;
    display: flex;
    padding: 18px;
    >div{
      width: 50px;
    }
    span.header__btn-text {
      color: #fff;
      display: block;
    }

    .icon-notice, .ic_nav_setting {
      width: 32px;
      height: 32px;
      background-size: 100%;
    }
  }
}

</style>
