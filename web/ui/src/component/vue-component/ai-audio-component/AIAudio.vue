<template>
  <div class="ai-audio">
    <div class="ai-audio-header">
      <div class="ai-audio-header_icon">
        <span class="ai-icon" />
      </div>
      <div class="ai-audio-header_message">
        {{ message }}
      </div>
    </div>
    <div class="ai-audio-content">
      <div class="ai-audio-content_spectrum">
        <Spectrum
          :spectrum="aiTeacherSpectrumData"
          :highlight-color="''"
          :end-color="''"
          :ai-state="aiState"
        />
      </div>
      <div class="ai-audio-content_stop">
        <div
          v-show="aiState === 3"
          class="stop-btn"
          @click="stopAnwser"
        >
          <span class="stop-icon" />
          {{ stopMessage }}
        </div>
      </div>
      <div class="ai-audio-content_message">
        {{ selfMessage }}
      </div>
    </div>
    <div class="ai-audio-footer">
      <div class="ai-audio-footer_icon">
        <span :class="['text', {'active': selfSpectrumData.length !== 0}]">{{ meText }}</span>
      </div>
      <div class="ai-audio-footer_volumn">
        <SelfSpectrum :spectrum="selfSpectrumData" />
      </div>
      <div
        class="ai-audio-footer_mic"
        @click="handleMicOpen"
      >
        <span :class="['icon', { 'close': !micOpen, 'not-speaking': !selfIsSpeaking, 'speaking': selfIsSpeaking }]" />
        <div
          v-show="false"
          ref="audio"
          class="trtc__audio"
        />
      </div>
    </div>
  </div>
</template>
<script>
import BaseComponent from '@core/BaseComponent';
import Spectrum from './Spectrum.vue';
import SelfSpectrum  from './SelfSpectrum.vue';
import i18next from 'i18next';

export default {
    name: 'AIAudio',
    components: {
      Spectrum,
      SelfSpectrum,
  },
    extends: BaseComponent,
    data() {
       return {
        selfIsSpeaking: false,
        aiTeacherIsSpeaking: false,
        selfUserId: null,
        micOpen: false,
        aiTeacherIsReady: false,
        message: i18next.t('AI 老师即将上课，请做好准备'),
        stopMessage: i18next.t('点击打断'),
        meText: i18next.t('我'),
        selfMessage: '',
        aiState: 1,
        teacherId: '',
        aiTaskId: '',
        selfSpectrumData: [],
        aiTeacherSpectrumData: [],
       };
    },
    watch: {
      aiTeacherIsReady(val) {
        if (val) {
          this.init();
        }
      },
      aiState(val) {
        if (val === 1) {
          this.message = i18next.t('聆听中...');
        } else if (val === 2) {
          this.message = i18next.t('思考中...');
        } else if (val === 3) {
          this.message = i18next.t('回答中...');
        } else {
          this.message = i18next.t('打断...');
        }
      },
    },
    mounted() {
      this.initListener();
      this.selfUserId = TCIC.SDK.instance.getUserId();
      this.makeSureClassJoined(() => {
        TCIC.SDK.instance.startClass().catch((err) => {
          TCIC.SDK.instance.reportException('[AI] startClass error', err);
        });
      });
    },
    methods: {
      initListener() {
        // 音量检测
        this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Volume_Update, this.onUserVolumChange);
        // 进房
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Add, this.onAvAdd);
        // 退房
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.AV_Remove, this.onAvRemove);
        // 字幕
        this.addLifecycleTCICEventListener(TCIC.TIMEvent.Recv_C2C_Custom_Msg, this.recvC2CCustomMsg);
        // ai ready
        this.addLifecycleTCICEventListener(TCIC.TMainEvent.AI_CLASS_READY, this.aiClassReady);
      },
      aiClassReady({ taskId }) {
        this.aiTaskId = taskId;
      },
      getMockSpectrumData(number) {
        const spectrumData = [];
        const numFrequencyBins =  number ?? 20;

        // 生成模拟的频谱数据
        for (let i = 0; i < numFrequencyBins; i++) {
            // 生成范围在[-300, 0]之间的随机能量值，单位为dBFS
            const energyValue = Math.random() * 300;
            spectrumData.push(energyValue);
        }
        return spectrumData;
      },
      onUserVolumChange(result) {
        const { userId, volume, spectrumData } = result;
        if (userId === this.selfUserId) {
            this.selfIsSpeaking = this.micOpen && parseInt(volume, 10) > 10;
            this.selfSpectrumData = (this.micOpen && this.selfIsSpeaking) ? this.getMockSpectrumData(40) : [];
        } else if (TCIC.SDK.instance.isTeacher(userId)) {
          this.aiTeacherIsSpeaking = parseInt(volume, 10) > 10;
          this.aiTeacherSpectrumData = this.getSpectrumData(spectrumData, 20);
        }
      },
      getSpectrumData(spectrumData, number) {
        if (spectrumData) {
          const numSegments = number ?? 20;
          const segmentSize = Math.floor(spectrumData.length / numSegments);
          const averagedData = [];
          for (let i = 0; i < numSegments; i++) {
              const startIndex = i * segmentSize;
              const endIndex = startIndex + segmentSize;
              // 计算这一份数据的平均值
              const segmentData = spectrumData.slice(startIndex, endIndex);
              const segmentAverage = Math.max(...segmentData);

              averagedData.push(segmentAverage);
          }
          return this.amplifyAudioDataOnce(averagedData.map(i => i + 300), 3) ;
        }
        return this.getMockSpectrumData(number);
      },
      amplifyAudioDataOnce(data, scaleFactor) {
          const amplifiedData = [data[0]];
          for (let i = 0; i < data.length - 1; i++) {
              const diff = data[i + 1] - data[i];
              const newDiff = diff * scaleFactor;

              // 确保每个数据点只被放大一次
              const newDataPoint = amplifiedData[amplifiedData.length - 1] + newDiff;
              amplifiedData.push(newDataPoint);
          }
          return amplifiedData;
      },
      startLocalAudio() {
        this.micOpen = true;
          TCIC.SDK.instance.startLocalAudio(this.$refs.audio).then(() => {
          this.micOpen = true;
        })
.catch(() => {
          this.micOpen = false;
        });
      },
      stopLocalAudio() {
        this.micOpen = false;
        TCIC.SDK.instance.muteLocalAudio(true).then(() => {
          this.micOpen = false;
          this.selfSpectrumData = [];
        })
.catch(() => {
          this.micOpen = true;
        });
      },
      unMuteLocalAudio() {
        this.micOpen = true;
        TCIC.SDK.instance.muteLocalAudio(false).then(() => {
          this.micOpen = true;
        })
.catch(() => {
          this.micOpen = false;
        });
      },
      stopAnwser() {
        if (this.isStoping) {
          return;
        }
        this.isStoping = true;
        const customData = {
          type: 20001, // 端上发送打断信令
          sender: this.selfUserId, // 发送者userid， 服务端会check该userid是否有效
          receiver: [this.teacherId], // 接受者userid列表，只需要填写机器人userid，服务端会check该userid是否有效
          payload: {
            id: this.selfUserId, // 消息id，可以使用uuid，排查问题使用
            timestamp: +new Date(), // 时间戳，排查问题使用
            taskid: this.aiTaskId,
          },
        };
        if (!this.aiTaskId) {
          TCIC.SDK.instance.showMessageBox(i18next.t('打断失败'), i18next.t('未查到任务id'), [i18next.t('确认')], (index) => {
          });
        } else {
          TCIC.SDK.instance.callExperimentalAPI('sendTRTCCustomData', {
            serviceCommand: 'trtc_ai_service.SendCustomCmdMsg',
            data: customData,
          }).then(() => {
            this.aiState = 1 ;
          })
          .catch((erro) => {
            TCIC.SDK.instance.showMessageBox(i18next.t('打断失败'), '',  [i18next.t('确认')], (index) => {
            });
          });
        }
        this.isStoping = false;
      },
      recvC2CCustomMsg(msg) {
        try {
          const data = JSON.parse(msg.data);
          const { type, payload, sender } = data;
          if (type === 10000) {
              const { text } = payload;
              if (sender === this.selfUserId) {
                this.selfMessage = text;
              } else if (text !== '' && this.aiState === 3) {
                this.message = text;
              }
            } else if (type === 10001) {
              const { state } = payload;
            //  1 聆听中  2 思考中  3 说话中  4 被打断
              this.aiState = state === 4 ? 1 : state;
            }
        } catch (error) {

        }
      },
      enableVolumeEvaluation() {
        TCIC.SDK.instance.enableVolumeEvaluation(100);
      },
      handleMicOpen() {
        if (this.micOpen)  {
          this.stopLocalAudio();
        } else {
          this.unMuteLocalAudio();
        }
      },
      init() {
        this.makeSureClassJoined(() => {
          this.startLocalAudio();
          this.enableVolumeEvaluation();
        });
      },
      onAvAdd(val) {
        const { userId } = val;
        const isTeacher = TCIC.SDK.instance.isTeacher(userId);
        if (isTeacher) {
          this.teacherId = userId;
          this.aiTeacherIsReady = true;
        }
      },
      onAvRemove(val) {
        const { userId } = val;
        const isTeacher = TCIC.SDK.instance.isTeacher(userId);
        if (isTeacher) {
          this.aiTeacherIsReady = false;
        }
      },
    },
};

</script>
<style lang="less">
    .ai-audio {
      height: 100%;
      display: flex;
      color: #fff;
      flex-direction: column;
      padding: 16px;

        .ai-audio-header {
          display: flex;
        }

        .stop-btn {
          display: inline-flex;
          padding: 8px 24px;
          align-items: center;
          gap: 4px;
          border-radius: 100px;
          border: 1px solid var(--stroke-color-primary, #3A3C42);
          background: var(--bg-color-input, #2B2C30);

          /* 投影-S */
          box-shadow: 0px 8px 18px 0px var(---Black-8, rgba(0, 0, 0, 0.06)), 0px 2px 6px 0px var(---Black-8, rgba(0, 0, 0, 0.06));
        }
        .ai-audio-header_icon {
          display: flex;
          width: 30px;
          height: 30px;
          padding: 8px;
          justify-content: center;
          align-items: center;
          border-radius: 999px;
          background: linear-gradient(180deg, #2565FA 0%, #48B6FF 120%);
          .ai-icon {
            display: block;
            width: 14px;
            height: 14px;
            background-image: url('../../../assets/images/ai/icon-create-ai.svg');
          }
        }
        .ai-audio-header_message {
          height: 88px;
          overflow: hidden;
          margin-left: 12px;
          color: var(--text-color-primary, rgba(255, 255, 255, 0.90));
          font-family: "PingFang SC";
          font-size: 14px;
          font-style: normal;
          font-weight: 400;
          line-height: 22px; /* 157.143% */
          margin-bottom: 14px;
          margin-top: 5px;
        }

        .ai-audio-content {
          display: flex;
          flex-direction: column;
          margin-top: 14px;
          width: 100%;
          flex: 1;
        }
        .ai-audio-content_stop {
          flex: 1;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .stop-icon {
          width: 14px;
          height: 14px;
          background: #E6594C;
          border-radius: 50%;
          margin-right: 4px;
        }

        .ai-audio-footer {
          height: 30px;
          width:  100%;
          display: flex;
          align-items: center;
        }
        .ai-audio-footer_icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 22px;
            height: 22px;
            border-radius: 999px;
            opacity: 0.8;
            background: linear-gradient(180deg, #4D20DF 0%, #822DF8 126.67%);
            span {
              color: var(--text-color-primary, rgba(255, 255, 255, 0.8));
            }

            .active {
              color: var(--text-color-primary, rgba(255, 255, 255, 0.9));
            }
          }

        .ai-audio-footer_volumn {
          flex: 1;
        }

        .ai-audio-footer_mic {
          display: inline-flex;
          padding: 8px 16px;
          align-items: flex-start;
          border-radius: 8px;
          background: var(--bg-color-input, #2B2C30);
          backdrop-filter: blur(4px);

          .icon {
            display: block;
            width: 20px;
            height: 20px;
          }

          .icon.close {
            background-image: url('../../../assets/images/ai/mic-close.svg');
          }

          .speaking {
            background-image: url('../../../assets/images/ai/mic-speak.svg');
          }

          .not-speaking {
            background-image: url('../../../assets/images/ai/mic-not-speak.svg');
          }
        }

        .ai-audio-content_spectrum {
          width: 100%;
          height: 360px;
        }


        .ai-audio-content_message {
          margin-bottom: 16px;
        }
    }
</style>
