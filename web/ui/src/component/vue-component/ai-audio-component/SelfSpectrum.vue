<template>
  <div
    ref="container"
    class="self-spectrum"
  >
    <canvas
      ref="canvas"
    />
  </div>
</template>
<script>
export default {
    props: {
        spectrum: {
            type: Array,
            required: true,
        },
    },
    data() {
        return {};
    },
    watch: {
        spectrum: {
            handler(val) {
                this.draw(val);
            },
            deep: true,
        },
    },
    mounted() {
        const canvas =  this.$refs.canvas;
        const canvasCtx = canvas.getContext('2d');
        this.canvas = canvas;
        this.ctx = canvasCtx;
        const containerDom = this.$refs.container;
        this.canvas.width = containerDom.offsetWidth;
        this.canvas.height = containerDom.offsetHeight;
        this.draw();
    },
    methods: {
        draw(val) {
            if (this.spectrumIsEmpty(this.spectrum)) {
                this.drawEmpty();
            } else {
                this.drawSpectrum();
            }
        },
        drawSpectrum() {
            const dataLength = 30;
            const minBarHeight = 10;
            const barSpacing = 10;
            const canvasCtx = this.ctx;
            const canvas = this.canvas;
            canvasCtx.fillStyle = '#14181d';
            canvasCtx.fillRect(0, 0, canvas.width, canvas.height);

            const barWidth = (canvas.width - dataLength * barSpacing) / dataLength;

            for (let i = 0; i < dataLength; i++) {
                const barHeight = Math.max(this.spectrum[i] / 10, minBarHeight); // 取最大值作为高度
                const x = i * (barWidth + barSpacing);
                const y = canvas.height / 2 - barHeight / 2;

                canvasCtx.fillStyle = 'rgba(255, 255,255, 0.9)';
                canvasCtx.fillRect(x, y, barWidth, barHeight);
            }
        },
        drawEmpty() {
            const dataLength = 30;
            const minBarHeight = 10;
            const barSpacing = 10;
            const canvasCtx = this.ctx;
            const canvas = this.canvas;
            canvasCtx.fillStyle = '#14181d';
            canvasCtx.fillRect(0, 0, canvas.width, canvas.height);

            const barWidth = (canvas.width - dataLength * barSpacing) / dataLength;

            for (let i = 0; i < dataLength; i++) {
                const barHeight = minBarHeight; // 取最大值作为高度
                const x = i * (barWidth + barSpacing);
                const y = canvas.height / 2 - barHeight / 2;

                canvasCtx.fillStyle = 'rgba(255, 255,255, 0.6)';
                canvasCtx.fillRect(x, y, barWidth, barHeight);
            }
        },
        spectrumIsEmpty(val) {
            if (val.length === 0) {
                return true;
            }
            return val.every(i => i === 0);
        },
    },
};
</script>
<style lang="less">
.self-spectrum {
    width: 80%;
    height: 30px;
    padding: 0 12px;
}

</style>

