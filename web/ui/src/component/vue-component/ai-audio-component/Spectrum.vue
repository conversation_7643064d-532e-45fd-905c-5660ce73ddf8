<template>
  <div
    ref="container"
    style="width: 100%; height: 100%;"
    :class="['canvas-container', { 'listening': aiState === 1, 'thinking': aiState === 2}]"
  >
    <canvas
      v-show="aiState === 3 "
      id="spectrum-canvas"
      ref="canvas"
    />
  </div>
</template>
<script>
export default {
    props: {
        spectrum: {
            type: Array,
            required: true,
        },
        highlightColor: {
            type: String,
            required: true,
        },
        endColor: {
            type: String,
            required: true,
        },
        aiState: {
          type: Number,
          required: true,
        },
  },
  data() {
    return {
      canvas: null,
      canvasCtx: null,
      barSpacing: 5,
      anwserTransform: false,
    };
  },
  watch: {
    spectrum: {
            handler(val) {
                cancelAnimationFrame(this.animation);
                this.draw(val);
            },
            deep: true,
        },
  },
  mounted() {
    const canvas =  this.$refs.canvas;
    const canvasCtx = canvas.getContext('2d');
    this.canvas = canvas;
    this.canvasCtx = canvasCtx;
  },
  methods: {
    draw() {
      const containerDom = this.$refs.container;
      this.canvas.width = containerDom.offsetWidth;
      this.canvas.height = containerDom.offsetHeight;
      const isSpectrumEmpty = this.spectrumIsEmpty();
      if (isSpectrumEmpty) {
        this.drawWhenEmpty();
      } else {
        this.drawSpectrum();
      }
    },
    drawSpectrum() {
      const { canvas, canvasCtx } = this;
      const bufferLength = this.spectrum.length;
      canvasCtx.fillStyle = '#14181d';
      canvasCtx.fillRect(0, 0, this.canvas.width, this.canvas.height);
      const barWidth = (canvas.width - bufferLength * this.barSpacing) / bufferLength;
      const colors = [
        '#275EC5',
        '#92D4FF',
        '#D9EFFE',
        '#92D4FF',
        '#275EC5',
      ];
      for (let i = 0; i < bufferLength; i++) {
            const barHeight = Math.max(this.spectrum[i] / 2, 20); // 取最大值作为高度
            const x = i * (barWidth + this.barSpacing);
            const y = canvas.height / 2 - barHeight / 2;
            let colorIndex = 0;
            if (i === 0) {
                colorIndex = 0; // 0%
            } else if (i < bufferLength * 0.2344) {
                colorIndex = 1; // 0% - 20%
            } else if (i < bufferLength * 0.5) {
                colorIndex = 2; // 20% - 50%
            } else if (i < bufferLength * 0.8079) {
                colorIndex = 3; // 50% - 80%
            } else {
                colorIndex = 4; // 80% - 100%
            }
            canvasCtx.fillStyle = colors[colorIndex];;
            // canvasCtx.fillRect(x, y, barWidth, barHeight);
            this.roundRect(canvasCtx, x, y, barWidth, barHeight, 5); // 高度为20px，圆角半径为10px
      }
      this.animation = requestAnimationFrame(this.drawSpectrum);
    },
     roundRect(ctx, x, y, width, height, radius) {
            ctx.beginPath();
            ctx.moveTo(x + radius, y);
            ctx.lineTo(x + width - radius, y);
            ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
            ctx.lineTo(x + width, y + height - radius);
            ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
            ctx.lineTo(x + radius, y + height);
            ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
            ctx.lineTo(x, y + radius);
            ctx.quadraticCurveTo(x, y, x + radius, y);
            ctx.closePath();
            ctx.fill();
        },
    drawWhenEmpty() {
      const { canvas, canvasCtx } = this;
        const gradient = canvasCtx.createLinearGradient(0, 0, canvas.width, 0);
        gradient.addColorStop(0, '#275EC5');        // 0%
        gradient.addColorStop(0.2344, '#92D4FF');  // 23.44%
        gradient.addColorStop(0.5, '#D9EFFE');      // 50%
        gradient.addColorStop(0.8079, '#92D4FF');   // 80.79%
        gradient.addColorStop(1, '#275EC5');

         // 动画参数
        let scale = 1;
        let growing = false;

        const animate = () => {
          canvasCtx.clearRect(0, 0, canvas.width, canvas.height); // 清除画布

            // 计算当前矩形的宽度
            const width = canvas.width * scale;
            const height = 10;
            const x = (canvas.width - width) / 2;
            const y = canvas.height / 2 - height / 2;

            // 绘制圆角矩形
            canvasCtx.fillStyle = gradient;
            this.roundRect(canvasCtx, x, y, width, height, 5); // 高度为20px，圆角半径为10px

            // 更新动画状态
            if (growing) {
                scale += 0.02; // 增加缩放比例
                if (scale >= 1) growing = false; // 达到最大值后开始收缩
            } else {
                scale -= 0.02; // 减少缩放比例
                if (scale <= 0.5) growing = true; // 达到最小值后开始扩展
            }

          this.animation = requestAnimationFrame(animate); // 请求下一帧动画
        };

        animate(); // 启动动画
    },
    spectrumIsEmpty() {
      if (this.spectrum.length === 0) {
        return true;
      }
      return this.spectrum.every(i => i === 0);
    },
  },

};

</script>
<style>
  .listening {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('./animation/listenning.gif');
  }

  .transform {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('./animation/anwser-transform.gif') ;
  }

  .thinking {
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    background-image: url('./animation/anwser-transform.gif') ;
  }

</style>
