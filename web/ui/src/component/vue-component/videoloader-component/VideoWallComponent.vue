<template>
  <div
    v-show="isWorking"
    :class="['video-wall-component', {'no-padding': wrapTop === 0, 'small-screen': isSmallScreen, 'portrait-class': isPortrait && isVideoPortraitClass}]"
  >
    <div
      v-if="isPortrait && isVideoPortraitClass"
      ref="wrap"
      :key="'portrait'"
      class="video-wrap"
    >
      <el-carousel
        :height="'100%'"
        :autoplay="false"
        :indicator-position="userArr.length <= 6 ? 'none' : ''"
        :loop="false"
      >
        <el-carousel-item
          v-for="(item, index) in portraitGroup"
          :key="index"
        />
      </el-carousel>
    </div>

    <div
      v-else
      :key="'landscape'"
      ref="wrap"
      class="video-wrap"
    />
    <div
      v-if="showScrollButton"
      class="video-pack left"
      :class="{ 'disable' : !leftEnable }"
      @click.stop.prevent="onScrollLeft()"
    >
      <i />
    </div>
    <div
      v-if="showScrollButton"
      class="video-pack right"
      :class="{ 'disable' : !rightEnable }"
      @click.stop.prevent="onScrollRight()"
    >
      <i />
    </div>
    <SwitchOrientationComponent v-if="isVideoPortraitClass" />
  </div>
</template>


<script>
/**
 * @desc 这个组件用来编排 video 的布局，目前有 3 种形式
 * 1. 16个视频按照一定的结构排列
 * 2. 移动端竖屏课堂，每页最多 6 个，超过支持分页
 * 3. 1v1视频课堂，支持微信视频通话形式的布局
 */
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
import VideoWall from '@/util/VideoWall.js';
import SwitchOrientationComponent from '@vueComponent/videowrap-component/SwitchOrientationComponent';
import Lodash from 'lodash';

export default {
  name: 'VideoWallComponent',
  components: {
    SwitchOrientationComponent,
  },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      isWorking: false,
      isPortrait: false,
      isOneOnOneClass: TCIC.SDK.instance.isOneOnOneClass(),
      fullscreenUserId: null,
      isVideoPortraitClass: TCIC.SDK.instance.isPortraitClass(),
      isStudent: TCIC.SDK.instance.isStudent(),
      videoWall: null,
      userArr: [],
      leftEnable: false,
      rightEnable: false,
      showScrollButton: false,
      timeStamp: new Date().getTime(),
      isFirstUpdate: true,
      wrapHeight: 0,
      wrapTop: null,
      wrapBottom: null,
    };
  },
  computed: {
    portraitGroup() {
      return Lodash.chunk(this.userArr, 6);
    },
    isVideoFullscreen() {
      return !!this.fullscreenUserId;
    },
  },
  watch: {
    isPortrait(value) {
      if (this.isVideoPortraitClass) {
        if (value) {
          // 竖屏时设置为1000，防止超过16个的时候方块显示很小
          this.videoWall.in_page_size = 1000;
        } else {
          this.videoWall.in_page_size = 16;
        }
      }
    },
    wrapBottom() {
      this.updateVideoWrapSize();
    },
    wrapTop() {
      this.updateVideoWrapSize();
    },
  },
  mounted() {
    // 初始化视频墙
    this.initVideoWall();
    // 监听视频组件加载事件
    this.addLifecycleTCICEventListener(Constant.TEventAddVideoComponent, this.onVideoAdd);
    this.addLifecycleTCICEventListener(Constant.TEventRemoveVideoComponent, this.onVideoRemove);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, this.onLayoutUpdate);
    // 监听辅助摄像头
    this.addLifecycleTCICStateListener(Constant.TStateShowSubCameraComponent, this.onSubCameraVisible);

    const resizeObserver = new ResizeObserver((entries) => {
      this.onComponentResize(entries[0].contentRect);
    });
    resizeObserver.observe(this.$el);
    this.makeSureClassJoined(this.onJoinClass);
    // 监听横竖屏变化
    this.addLifecycleTCICStateListener(TCIC.TMainState.Device_Orientation, (orientation) => {
      this.isPortrait = orientation === TCIC.TDeviceOrientation.Portrait;
      this.updateVideoWrapSize();
      this.$nextTick(() => {
        if (this.isVideoPortraitClass) {
          TCIC.SDK.instance.changeResolutionMode(this.isPortrait ? 1 : 0);
          this.loadVideos();
        }
      });
    });
    // electron 全屏共享后重置视频墙
    if (TCIC.SDK.instance.isBigRoom() && TCIC.SDK.instance.isElectron() && this.isOneOnOneClass && TCIC.SDK.instance.isVideoOnlyClass()) {
      TCIC.SDK.instance.subscribeState(TCIC.TMainState.Screen_Share, (state) => {
        console.warn('ScreenShare state:', state, TCIC.SDK.instance.isFeatureAvailable('ScreenShareAdvanceMode'));
        const isScreenSharing = state < 2;
        if (!isScreenSharing) {
          // 取消全屏共享后，等ScreenVideoWrapperComponent.unloadVideoWraper执行完后再重置视频墙
          setTimeout(() => {
            this.loadVideos();
          }, 300);
        }
      });
    }
    if (this.isVideoPortraitClass && this.isOneOnOneClass) {
      this.$EventBus.$on('toggle-video-size', ({ name, label }) => {
        if (!TCIC.SDK.instance.isPortrait()) {
          // 横屏不要切换
          return;
        }
        const bigDom = TCIC.SDK.instance.getComponent(name, label);
        const smallDom = this.$refs.wrap.querySelector(name === 'student-component' ? 'teacher-component' : 'student-component');
        TCIC.SDK.instance.updateComponent(smallDom.tagName.toLowerCase(), {
          position: 'absolute',
          zIndex: 301,
          style: 'overflow: visible;',
          left: 'calc(100vw - 100px)',
          top: '20px',
          width: '100px',
          height: '192px',
        }, smallDom.getAttribute('label'));
        smallDom.getVueInstance().disableCtrl = true;
        smallDom.getVueInstance().style = { boxShadow: '0 2px 4px 2px rgba(0, 0, 0, 0.15)', borderRadius: '15px' };
        TCIC.SDK.instance.updateComponent(bigDom.tagName.toLowerCase(), {
          position: 'relative',
          zIndex: 200,
          style: 'overflow: visible;',
          top: '0px',
          left: '0px',
          width: '100%',
          height: '100%',
        }, label);
        bigDom.getVueInstance().disableCtrl = false;
        bigDom.getVueInstance().style = {};
      });
    }
  },
  methods: {
    onJoinClass() {
      this.onLayoutUpdate(TCIC.SDK.instance.getClassLayout());
    },
    onScrollLeft: Lodash.throttle(function () {
      this.onScrollVideoH(-1);
    }, 500, {
      leading: true,
      trailing: false,
    }),
    onScrollRight: Lodash.throttle(function () {
      this.onScrollVideoH(1);
    }, 500, {
      leading: true,
      trailing: false,
    }),
    onScrollVideoH(direct) {
      if (!this.videoWall) return;
      if (direct < 0) {
        this.videoWall.pageUp();
      } else if (direct > 0) {
        this.videoWall.pageDown();
      }
      this.showScrollButton = this.videoWall.needScroll();
      this.leftEnable = this.videoWall.canPageUp();
      this.rightEnable = this.videoWall.canPageDown();
    },
    onComponentResize(rect) {
      if (!this.isWorking) return ;
      this.updateVideoWrapSize(rect);
    },
    onLayoutUpdate(layout) {
      const oldStatus = this.isWorking;
      this.isWorking = layout === TCIC.TClassLayout.Video || layout === TCIC.TClassLayout.VideoIM;
      if (this.isWorking && !oldStatus) {
        this.loadVideos();
      } else if (!this.isWorking && oldStatus) {
        this.unloadVideos();
      }
      if (!this.isFirstUpdate) {
        // 保存布局到任务
        TCIC.SDK.instance.updateTask(Constant.TConstantCollegeTaskLayout, JSON.stringify({
          layout,
          timeStamp: this.timeStamp,
        }));
      }
      this.isFirstUpdate = false;
    },
    onVideoAdd(info, refresh = true) {
      if (!this.isWorking) return ;
      if (this.isVideoFullscreen) {
        return;
      }
      let ele;
      if (info.isTeacher) {
        ele = TCIC.SDK.instance.getComponent('teacher-component');
        this.videoWall.unshiftVideo(info.userId, ele, true);
        this.videoWall.lockVideo(info.userId, refresh);
      } else {
        ele = TCIC.SDK.instance.getComponent('student-component', info.userId);
        const classInfo = TCIC.SDK.instance.getClassInfo();
        this.videoWall.pushVideo(info.userId, ele, refresh);
      }
      if (this.userArr.indexOf(info.userId) === -1) {
        this.userArr.push(info.userId);
      }

      // [竖屏分页布局] 将video元素插入到父节点
      if (this.isVideoPortraitClass && this.isPortrait) {
        this.$nextTick(() => {
          const pageIndex = Math.floor(this.userArr.indexOf(info.userId) / 6);
          const carouselItems = this.$refs.wrap.querySelectorAll('.el-carousel__item');
          // 老师始终放在第一个
          if (info.isTeacher && carouselItems[0].children.length >= 1) {
            carouselItems[0]?.insertBefore(ele, carouselItems[0].children[0]);
            if (carouselItems[0].children.length > 6) {
              carouselItems[pageIndex]?.appendChild(carouselItems[0].children[carouselItems[0].children.length - 1]);
            }
          } else {
            carouselItems[pageIndex]?.appendChild(ele);
          }
        });
      } else {
        this.$refs.wrap.appendChild(ele);
      }
      this.onScrollVideoH(0);
    },
    onVideoRemove(info) {
      if (!this.isWorking) return;
      const isFullscreenVideoRemoved = info.userId === this.fullscreenUserId;
      if (this.isVideoFullscreen && !isFullscreenVideoRemoved) {
        return;
      }
      this.userArr = this.userArr.filter(id => id !== info.userId);
      this.videoWall.removeVideo(info.userId);
      this.onScrollVideoH(0);
      if (this.isVideoPortraitClass && this.isPortrait) {
        // 刷新当前布局
        this.$nextTick(() => {
          this.loadVideos();
        });
      }
      if (isFullscreenVideoRemoved) {
        this.fullscreenUserId = null;
        this.$nextTick(() => {
          this.loadVideos();
        });
      }
    },
    onSubCameraVisible(visible) {
      if (visible) {
        this.videoWall.pushVideo('sub-camera', TCIC.SDK.instance.getComponent('sub-camera-component'), true, VideoWall.VideoTypeSub);
      } else {
        this.videoWall.removeVideo('sub-camera', true, VideoWall.VideoTypeSub);
      }
    },
    // 初始化当前布局
    loadVideos() {
      const loaderCom = TCIC.SDK.instance.getComponent('video-loader-component');
      this.updateVideoWrapSize();
      if (loaderCom) {
        const loaderVue = loaderCom.getVueInstance();
        let videoInfos = loaderVue.getCurLoadedVideoInfos();
        if (this.isVideoPortraitClass) {
          videoInfos = videoInfos.sort((info1, info2) => (info2.isTeacher ? 1 : -1));
        }
        console.log(`XDBG-VideoWallComponent::loadVideos=>size: ${videoInfos.length}`);

        for (let i = 0; i < videoInfos.length; i ++) {
          this.onVideoAdd(videoInfos[i], i > videoInfos.length - 2);
        }
      }
      const doubleScreen = TCIC.SDK.instance.getState(Constant.TStateCollegeDoubleScreenMode);
      if (doubleScreen) {
        this.startDoubleScreenTimer();
      }
    },
    // 纯视频课支持放大某一个 video
    toggleFullscreen(userId) {
      const classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status);
      if (classStatus !== TCIC.TClassStatus.Already_Start) {
        return;
      }
      if (this.fullscreenUserId) {
        this.fullscreenUserId = null;
        this.loadVideos();
      } else {
        // 最大化一个 video
        const loaderCom = TCIC.SDK.instance.getComponent('video-loader-component');
        const loaderVue = loaderCom.getVueInstance();
        const videoInfos = loaderVue.getCurLoadedVideoInfos();
        if (videoInfos.length === 1) {
          // 视频墙只有一个 video，无需放大
          return;
        }
        const userInfo = videoInfos.find(user => user.userId === userId);
        this.unloadVideos();
        this.onVideoAdd(userInfo, true);
        this.fullscreenUserId = userId;
      }
    },
    // 卸载视频组件
    unloadVideos() {
      this.userArr.forEach(id => this.videoWall.removeVideo(id, true));
      this.userArr = [];
      this.stopDoubleScreenTimer && this.stopDoubleScreenTimer();
    },
    initVideoWall() {
      this.videoWall = new VideoWall(this.$el.getBoundingClientRect(), 16);
      this.videoWall.setSortFunc((item1, item2) => {
        const permission1 = TCIC.SDK.instance.getPermission(item1.uid);
        const permission2 = TCIC.SDK.instance.getPermission(item2.uid);
        // 进入课堂顺序
        if (permission1.lastEnterTime && permission2.lastEnterTime) {
          return permission1.lastEnterTime  - permission2.lastEnterTime;
        }
        return 0;
      });
      this.videoWall.setLayoutCallback((dom, left, top, width, height) => {
        const label = dom.tagName.toLowerCase() === 'teacher-component' ? 'default' : dom.getAttribute('label');
        // h5 老师端固定横屏 先去掉
        // const isMobileWeb = TCIC.SDK.instance.isMobile() && !TCIC.SDK.instance.isMobileNative();
        const videoWidth = width;
        const videoHeight = height;
        const videoLeft = left;
        const videoTop = top;
        if (this.isPortrait && TCIC.SDK.instance.isPortraitClass()) {
          document.querySelector('.video-wall-component').style.backgroundColor = 'rgb(28, 33, 49)';
          if (TCIC.SDK.instance.isOneOnOneClass()) {
            console.log('更新 1v1视频课的布局', this.userArr);
            // 视频画面填满容器
            TCIC.SDK.instance.setLocalVideoParams({ mode: 0 });
            // 1v1 课堂不显示声音变化动画
            dom.getVueInstance().enableShowMicBorder = false;
            if (TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Not_Start || this.userArr.length === 1) {
              TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
                position: 'relative',
                zIndex: 200,
                style: 'overflow: visible;',
                top: '0px',
                left: '0px',
                width: '100%',
                height: '100%',
              }, label);
              return;
            }
            const isSelfDom = (dom.tagName.toLowerCase() === 'student-component' && TCIC.SDK.instance.isStudent())
                || (dom.tagName.toLowerCase() === 'teacher-component' && TCIC.SDK.instance.isTeacher());
            // 自己显示小屏，对方显示大屏
            if (isSelfDom) {
              // 小屏
              TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
                position: 'absolute',
                zIndex: 301,
                style: 'overflow: visible;',
                left: 'calc(100vw - 100px)',
                top: '20px',
                width: '100px',
                height: '192px',
              }, label);
              dom.getVueInstance().disableCtrl = true;
              dom.getVueInstance().style = { boxShadow: '0 2px 4px 2px rgba(0, 0, 0, 0.15)', borderRadius: '15px' };
            } else {
              TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
                position: 'relative',
                zIndex: 200,
                style: 'overflow: visible;',
                top: '0px',
                left: '0px',
                width: '100%',
                height: '100%',
              }, label);
            }
            return;
          }
          TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
            position: 'relative',
            zIndex: this.isLiveClass ? 301 : 200,
            style: 'overflow: visible;',
            left: '0px',
            top: '0px',
            width: `${videoWidth}px`,
            height: `${videoHeight}px`,
          }, label);
        } else {
          TCIC.SDK.instance.updateComponent(dom.tagName.toLowerCase(), {
            position: 'absolute',
            left: `${videoLeft}px`,
            top: `${videoTop}px`,
            width: `${videoWidth}px`,
            height: `${videoHeight}px`,
            zIndex: this.isLiveClass ? 301 : 200,
            style: 'overflow: visible;',
          }, label);
        }
      });
      this.$EventBus.$on('video-fullscreen', (userId) => {
        this.toggleFullscreen(userId);
      });
    },
    updateVideoWrapSize(rect) {  // 更新大屏中视频尺寸为16/9
      if (!this.rect) rect = this.$el.getBoundingClientRect();
      const isMobile = TCIC.SDK.instance.isMobile();

      const headerComponent = TCIC.SDK.instance.getComponent('header-component');
      const headerRect = headerComponent.getBoundingClientRect();
      const safeAreaHeight = parseInt(getComputedStyle(this.$el).paddingTop);
      const wrapTop = Lodash.isFinite(this.wrapTop) ? this.wrapTop : headerRect.height;
      const wrapBottom = Lodash.isFinite(this.wrapBottom) ? this.wrapBottom : 90 /* tabbar 的高度 */;

      const isPortraitClass = TCIC.SDK.instance.isPortraitClass();
      // 竖屏纯视频直播
      if (this.isPortrait
        && isPortraitClass
        && isMobile) {
        this.$refs.wrap.style.top = `${wrapTop}px`;
        if (rect.height / rect.width > 16 / 9) {
          this.$refs.wrap.style.width = `${rect.width}px`;
          // if (this.isStudent) {
          //   this.$refs.wrap.style.height = `${rect.height - wrapTop}px`;
          //   this.wrapHeight = rect.height - wrapTop - safeAreaHeight;
          // } else {
          //   const wrapHeight = rect.height - (wrapTop + wrapBottom + safeAreaHeight);
          //   this.$refs.wrap.style.height = `${wrapHeight}px`;
          //   this.wrapHeight = wrapHeight;
          // }
          const wrapHeight = rect.height - (wrapTop + wrapBottom + safeAreaHeight);
          this.$refs.wrap.style.height = `${wrapHeight}px`;
          this.wrapHeight = wrapHeight;
        } else {
          this.$refs.wrap.style.height = `${rect.height}px`;
          // this.$refs.wrap.style.width = `${rect.height * 9 / 16}px`;
          this.$refs.wrap.style.width = `${rect.width}px`;
          this.wrapHeight = rect.height;
        }
      } else {
        if (rect.width / rect.height > 16 / 9) {
          rect.height = Math.min(rect.height, window.innerHeight - 2 * wrapTop);
          this.$refs.wrap.style.height = `${rect.height}px`;
          this.$refs.wrap.style.width = `${rect.height * 16 / 9}px`;
        } else {
          this.$refs.wrap.style.width = `${rect.width}px`;
          this.$refs.wrap.style.height = `${Math.min((rect.width * 9 / 16), window.innerHeight - 2 * wrapTop)}px`;
        }
        this.$refs.wrap.style.top = `${-this.$refs.wrap.style.height}px`;
      }
      this.videoWall.updateRect(this.$refs.wrap.getBoundingClientRect(), 16);
    },
  },
};

</script>

<style lang="less">
.light .video-wall-component {
  --bg-color: transparent;
}
.video-wall-component{
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: var(--bg-color, #131524);
  &.portrait-class{
    display: block;
    .video-wrap{
      position: relative;
      box-sizing: border-box;
    }
  }
  .video-wrap{
    .el-carousel{
      width: 100%;
      height: 100%;
      .el-carousel__item{
        display: inline-flex;
        flex-wrap: wrap;
        align-content: center;
        gap: 4px;
      }
    }
    &.portrait {
      > * {
        margin-bottom: 2px;
      }
      > :nth-child(2n+1) {
        margin-right: 2px;
      }
    }

  }
  .disable {
    opacity: 0.3;
  }

  .video-pack {
    position: absolute;
    background: transparent;
    top: 50%;
    transform: translateY(-50%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    padding: 0;
    border: 0;
    z-index: 300;

    i {
      display: flex;
      width: 32px;
      height: 32px;
      margin: 41px 0;
    }


    &.left {
      left: 6px;
      i {
        background: url("./assets/icon_left.png") no-repeat;
        background-size: contain;
      }
      &:hover {
        cursor: pointer;
        i {
          background: url("./assets/icon_left_hover.png") no-repeat;
          background-size: contain;
        }
      }
    }

    &.right {
      right: 6px;
      i {
        background: url("./assets/icon_right.png") no-repeat;
        background-size: contain;
      }
      &:hover {
        cursor: pointer;
        i {
          background: url("./assets/icon_right_hover.png") no-repeat;
          background-size: contain;
        }
      }
    }

    &.left, &.right {
      position: absolute;
    }
  }
}

/**
* 顶部菜单栏会遮挡
* 处理桌面端和移动端
*/
@media (max-height: 1400px){
  .video-wall-component{
    padding-top: 65px;
    padding-top: env(safe-area-inset-top);
    &.no-padding{
      padding-top: 0;
    }
  }
}

@media (max-height: 400px){
  .video-wall-component{
    padding-top: 45px;
    &.no-padding{
      padding-top: 0;
    }
  }
}


</style>
