<template>
  <div
    v-if="msg.convType === 'tips'"
    class="discuss-dynamic"
  >
    <div class="discuss-dynamic-item">
      <span>{{ msg.nickname || msg.from }}</span>
      <span>{{ msg.parsedTips }}</span>
      <span>{{ msg.parsedTime }}</span>
    </div>
  </div>
  <div
    v-else
    class="discuss-item im-component-msg"
    :class="msg.ownMsg ? 'discuss-mine' : ''"
  >
    <div class="discuss-item-name">
      <div class="user-info">
        <i
          v-if="msg.teacherMsg || msg.assistantMsg"
          :class="[msg.teacherMsg ? 'teacher-icon' : 'assistant-icon', 'role-icon']"
        >
          <span>
            {{ msg.teacherMsg ? roleInfo.teacher : roleInfo.assistant }}
          </span>
        </i>
        <span
          v-if="!isPrivateMsg"
          class="name-user"
          :title="msg.nickname || msg.from"
        >
          {{ msg.nickname || msg.from }}
        </span>
        <span
          v-if="isPrivateMsg"
          class="name-user"
          :title="msg.nickname || msg.from"
        >
          {{ msg.ownMsg ? translateTip.privateMsgNameFromSelf : translateTip.privateMsgNameFromOther }}
        </span>
        <span
          v-if="!isMobile"
          class="name-time"
        >{{ msg.parsedTime }}</span>
        <span
          v-if="isPrivateMsg"
          class="im-component-msg-user-private"
        >{{ $t("（私聊）") }}</span>
      </div>
      <MsgControl
        v-if="showControl"
        :msg="msg"
        :show-reply="showReply"
        @click-reply="setReplyMsg"
        @click-delete="deleteMsg"
        @click-toggle-silence="silenceMsg(!msg.silenceMsg)"
      />
    </div>
    <div class="discuss-item-body">
      <div class="bubble">
        <MsgExtReply
          v-if="msg.dataExt && msg.dataExt.replyMsg"
          :msg="msg.dataExt.replyMsg"
          @click-img="openPreviewImg(msg.dataExt.replyMsg)"
        />
        <div
          v-if="msg.msgType === 'img_message'"
          :class="['im-component-img-control', msg.ownMsg ? 'img-mine' : '']"
        >
          <!--发送中 -->
          <div
            v-if="imgSendStatus === 1 && msg.ownMsg"
            class="div-per"
          >
            <div class="image-per" />
            <span class="per-font">{{ per }}</span>
          </div>
          <!--发送成功 啥都不显示-->
          <!--发送失败 -->
          <div
            v-else-if="imgSendStatus === 0 && msg.ownMsg"
            class="div-per"
          >
            <div
              class="image-fail"
              @click="reSendImgMsg"
            />
          </div>
          <img
            class="image-element"
            :src="msg.preview1 || msg.preview2 || msg.data"
            @load="onImageLoaded"
            @click="openPreviewImg(msg)"
          >
        </div>
        <div
          v-else-if="msg.msgType === 'file_message'"
          :class="['im-component-file-message', msg.ownMsg ? 'img-mine' : '']"
          @click="downloadFile(msg.data)"
        >
          <div class="file-info">
            <div
              class="file-icon"
              alt="file"
            />
            <div class="file-details">
              <div class="file-name">
                {{ msg.desc || $t('未命名文件') }}
              </div>
              <div class="file-size">
                {{ formatFileSize(msg.ext) }}
              </div>
            </div>
          </div>
        </div>
        <div
          v-else
          class="discuss-item-con im-component-text-content"
        >
          <template v-if="msg.parsedEmoji.type === 1">
            <span
              v-for="(part, index) in parsedMessageParts"
              :key="index"
            >
              <a
                v-if="part.type === 'link'"
                :key="index"
                :href="part.content"
                target="_blank"
                rel="noopener noreferrer"
                class="tcic-link"
              >{{ part.display }}</a>
              <span
                v-else
                v-text="part.content"
                :key="index"
              />
            </span>
            <translate-msg
              v-if="!isUrl(msg.parsedEmoji.content)"
              :msg="msg"
            />
          </template>
          <pre
            v-if="msg.parsedEmoji.type === 2"
            class="pre-img"
          ><i class="ic-img"><img
            :class="[isMobile ? `oc-${msg.parsedEmoji.imageClass}`: msg.parsedEmoji.imageClass]"
            alt=""
            src="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABAQMAAAAl21bKAAAAA1BMVEUAAACnej3aAAAAAXRSTlMAQObYZgAAAApJREFUCNdjYAAAAAIAAeIhvDMAAAAASUVORK5CYII="
          ></i></pre>
        </div>
      </div>
      <div
        v-if="msg.msgType === 'file_message' && imgSendStatus === 1 && msg.ownMsg"
        class="sending-icon"
        alt="file_sending"
      />
      <div
        v-if="msg.msgType === 'file_message' && imgSendStatus === 0 && msg.ownMsg"
        class="failed-icon"
        alt="file_failed"
      />
    </div>
  </div>
</template>

<script>
import { MsgBaseComponent } from '../im-component/MsgBase';
import i18next from 'i18next';

export default {
  extends: MsgBaseComponent,
  data() {
    return {
      supportUrlLink: false,
      handleLinkClick: null,
    };
  },
  computed: {
    translateTip() {
      return {
        privateMsgNameFromSelf: i18next.t('我对 {{privateMsgName}}', { privateMsgName: this.privateMsgName }),
        privateMsgNameFromOther: i18next.t('{{privateMsgName}} 对我', { privateMsgName: this.privateMsgName }),
      };
    },
    parsedMessageParts() {
      return this.parseMessageContent(this.msg.parsedEmoji.content);
    }
  },
  mounted() {
    this.handleLinkClick = this.onOpenWithSystemBrowser.bind(this);
    this.$el.addEventListener('click', this.handleLinkClick);
    this.supportUrlLink = TCIC.SDK.instance.isFeatureAvailable('UrlLink');
  },
  beforeDestroy() {
    this.$el.removeEventListener('click', this.handleLinkClick);
  },
  methods: {
    parseMessageContent(content) {
      if (!content) return [];

      const urlRegex = /(https?:\/\/[^\s]+)/g;
      const result = [];

      let lastIndex = 0;
      content.replace(urlRegex, (match, url, offset) => {
        if (offset > lastIndex) {
          const plainText = content.slice(lastIndex, offset);
          result.push({
            type: 'text',
            content: plainText,
          });
        }
        result.push({
          type: 'link',
          content: url,
          display: url,
        });
        lastIndex = offset + url.length;
      });

      if (lastIndex < content.length) {
        const remainingText = content.slice(lastIndex);
        result.push({
          type: 'text',
          content: remainingText,
        });
      }

      return result;
    },
    onOpenWithSystemBrowser(event) {
      if (event.target.classList.contains('tcic-link')) {
        event.preventDefault();
        this.openWithSystemBrowser(event);
      }
    },
    openWithSystemBrowser(e) {
      TCIC.SDK.instance.openBrowser(e.target.href);
    },
    formatTextWithLinks(text) {
      if (!text) return '';

      const urlRegex = /(https?:\/\/[^\s]+)/g;

      return this.supportUrlLink ? text.replace(urlRegex, (url) => {
        const href = url.startsWith('http') ? url : `http://${url}`;
        return `<a href="${href}" target="_blank" class="tcic-link" rel="noopener noreferrer">${url}</a>`;
      }) : text;
    },
    hasUrl(text) {
      const urlRegex = /(https?:\/\/[^\s]+(?:\.[a-z]{2,})+(?:\/[^\s]*)?)/gi;
      return urlRegex.test(text);
    },
    downloadFile(fileUrl) {
      if (!fileUrl) return;

      const a = document.createElement('a');
      a.href = fileUrl;
      a.download = this.msg.desc || 'downloaded-file';
      a.target = '_blank';
      a.className = 'tcic-link';

      a.addEventListener('click', (event) => {
        event.preventDefault();
        this.openWithSystemBrowser(event);
      });

      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
    },
    formatFileSize(size) {
      if (!size) return i18next.t('未知大小');
      const bytes = parseInt(size, 10);
      if (bytes < 1024) return `${bytes} B`;
      if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
      return `${(bytes / 1024 / 1024).toFixed(1)} MB`;
    },
  },
};
</script>

<style lang="less">
@import '../im-component/MsgControl.less';

.failed-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  background-repeat: no-repeat;
  background-size: contain;
  background-image: url('./assets/file_failed.svg');
}

.sending-icon {
  width: 16px;
  height: 16px;
  margin-right: 10px;
  background-repeat: no-repeat;
  background-size: contain;
  background-image: url('./assets/file_sending.svg');
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

.discuss-dynamic {
  margin-bottom: 10px;
  &-item {
    padding: 0.5rem 0;
    font-size: 0.58rem;
    color: #8a9099;
    text-align: center;
    span + span {
      margin-left: 0.33rem;
    }
  }
}

.discuss-item {
  padding: 8px 16px 8px 16px;

  .role-icon {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    padding: 0 4px;
    border-radius: 2px;
    height: 20px;
    &.assistant-icon {
      background: linear-gradient(152deg, #23be82 0%, #08ae6e 94%);
    }
    &.teacher-icon {
      background: linear-gradient(152deg, #00a6ff 0%, #006dff 94%);
    }
    span {
      font-size: 12px;
      color: #fff;
    }
  }

  .discuss-item-name {
    display: flex;
    margin-bottom: 6px;
    font-size: 0.58rem;
    color: #8A9099;
    .user-info {
      flex: 1;
      overflow: hidden;
      display: flex;
      word-break: normal;
      gap: 4px;
    }
    .name-user {
      max-width: calc(100% - 50px);
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .name-time {
    }
    .im-component-msg-user-private{
      display: inline-block;
      color: #236CFA;
      font-size: 12px;
      margin: 0 -4px 0 -8px;
      flex-shrink: 0;
    }
  }

  .discuss-item-body {
    display: flex;
    flex-direction: row;
    align-items: center;
    align-content: flex-start;

    .bubble {
      display: inline-block;
      width: auto;
      //min-width: 44px;
      max-width: 68vw;
      min-height: 48px;
      border-radius: 8px;
      padding: 8px;
      color: var(--text-color, #ffffff);
      background: var(--bubble-bg, rgba(223, 223, 223, 0.05));
      user-select: text !important;
      -webkit-user-select: text !important;

      .discuss-item-reply {
        margin-bottom: 6px;
        font-size: 0.58rem;
        color: #8A9099;
      }

      .discuss-item-con {
        span {
          user-select: text !important;
          -webkit-user-select: text !important;
        }
        pre {
          user-select: text !important;
          -webkit-user-select: text !important;
          font-size: 0.75rem;
          line-height: 1.25rem;
          .ic-img {
            width: 1.25rem;
            height: 1.25rem;
            display: flex;
            align-items: center;
            justify-content: center;
          }

          a {
            color: #13a449;
            // padding: 0 3px;
            text-decoration: underline;
            cursor: pointer;
          }
        }
      }
    }
  }

  // 我发送的信息
  &.discuss-mine {
    .discuss-item-name {
      text-align: right;
      display: flex;
      .user-info {
        justify-content: flex-end;
      }
      .name-user {
        display: inline-block;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: calc(100% - 50px);
        vertical-align: top;
      }
      .name-time {
        display: inline-block;
        vertical-align: top;
      }
      .im-component-msg-user-private{
        vertical-align: middle;
        color: #236CFA;
        font-size: 12px;
        flex-shrink: 0;
      }
    }
    .discuss-item-body {
      flex-direction: row-reverse;

      .bubble {
        // 可以对自己的消息设置不同的样式
        color: var(--text-color, #ffffff);
        background: var(--bubble-bg, rgba(223, 223, 223, 0.05));

        // 自己的回复文字要靠右才加上这个
        // .discuss-item-con {
        //   display: flex;
        //   justify-content: flex-end;
        // }
      }
    }
  }
  &:last-child {
    padding-bottom: 12px;
  }
  .im-component-file-message {
  display: flex;
  align-items: center;
  padding: 10px;
  border-radius: 8px;
  word-break: break-word;
  cursor: pointer; /* 点击整个气泡下载 */
}

.file-info {
  display: flex;
  align-items: center;
  flex-grow: 1;
}

.file-icon {
  width: 36px;
  height: 36px;
  margin-right: 10px;
  background-image: url('./assets/file.svg');
  background-size: 85%;
  background-repeat: no-repeat;
}

.file-details {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.file-name {
  font-size: 14px;
  font-weight: bold;
  color: var(--text-color, #f0f0f0); /* 适配深蓝底色 */
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 140px;
}

.file-size {
  font-size: 12px;
  color: var(--text-color, #cccccc); /* 适配深色背景 */
  margin-top: 4px;
}

/* 小屏幕适配 */
@media (max-width: 768px) {
  .file-name {
    max-width: 150px;
  }
  .file-icon {
    width: 30px;
    height: 30px;
  }
}
}


.im-component-img-control {
  display: flex;
  flex-direction: row;
  align-items: center;
  // align-content: flex-start;
  // text-align: left;
  justify-content: flex-start;

  div {
    width: 160px;
    height: 90px;
    margin: 0 4px;
    background-repeat: no-repeat;
    background-position: 50% 50%;

    &:hover {
      border-radius: 2px;
      border: 1px solid #306ff6;
    }

    &:active {
      border-radius: 2px;
      border: 1px solid #306ff6;
    }
  }

  .image-element {
    max-height: 150px;
    max-width: 200px;
  }

  .div-per {
    text-align: right;
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: center;
    width: 38px !important;
    &:hover {
      border-radius: 2px;
      border: none !important;
    }
  }

  .image-per {
    width: 28px;
    height: 24px;
    margin-right: 5px;
    background-image: url('./assets/ic_net_normal.svg');
    animation: rotating 1.5s linear infinite;
    &:hover,
    &:active {
      border: none;
      opacity: 1;
    }
  }

  .image-fail {
    width: 24px;
    height: 24px;
    margin-right: 5px;
    background-image: url('./assets/ic_net_poor.svg');
    cursor: pointer;
    &:hover,
    &:active {
      border: none;
      opacity: 1;
      cursor: pointer;
    }
  }

  .per-font {
    font-size: 12px;
    font-family: PingFangSC-Medium, PingFang SC;
    font-weight: 500;
    color: #8a9099;
    line-height: 17px;
    margin-top: 5px;
  }
}

.img-mine {
  justify-content: flex-end !important;
}
</style>
