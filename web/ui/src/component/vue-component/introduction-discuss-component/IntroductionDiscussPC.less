@import '../im-component/IMInput.less';

@--color-primary: #006EFF;
.light{
  .emoji-popper {
    --bg-color: #fff;
  }
}
.emoji-popper {
  &.client-black {
    background: var(--bg-color, #1C2131) !important;
    border-color: rgba(184, 184, 184, 0.1) !important;
    .popper__arrow {
      border-top-color: transparent !important;
      &:after {
        border-top-color: transparent !important;
      }
    }
  }
}
// 连麦管理 - 弹出列表
.stage-popper {
  padding: 0!important;
  background: #252A39!important;
  border-radius: 4px;
  border: none!important;
  box-shadow: none!important;
  .cu-member-list {
    width: 330px;
    &.el-scrollbar {
      .el-scrollbar__wrap {
        width: 100%;
        max-height: 350px;
        padding-right: 10px;
        overflow: scroll;
      }
    }
    .cu-member-item {
      display: flex;
      justify-content: space-between;
      height: 28px;
      margin: 20px 0 40px 14px;
      align-items: center;
      &:last-child {
        margin-bottom: 20px;
      }
      //离线
      &.offline {
        .ic-me {
          -webkit-filter: grayscale(100%);
          -moz-filter: grayscale(100%);
          -ms-filter: grayscale(100%);
          -o-filter: grayscale(100%);
          filter: grayscale(100%);
          filter: gray;
        }
      }
      //名字
      .left {
        display: flex;
        flex: 1;
        align-items: center;
      }
      .ic-me {
        display: flex;
        align-items: center;
        width: 16px;
        height: 16px;
        margin-right: 4px;

        &.pc {
          background: url('../member-list-component/assets/device-pc.svg') no-repeat center;
        }

        &.phone {
          background: url('../member-list-component/assets/device-phone.svg') no-repeat center;
        }

        &.pad {
          background: url('../member-list-component/assets/device-pad.svg') no-repeat center;
        }

        &.mini {
          background: url('../member-list-component/assets/device-mini.svg') no-repeat center;
        }

        &.unknow {
          background: url('../member-list-component/assets/device-unknow.svg') no-repeat center;
        }
      }
      label {
        display: -webkit-box;
        max-width: 3.33rem;
        font-size: 0.83rem;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
        -webkit-line-clamp: 1; //显示多少行
        -webkit-box-orient: vertical;
        letter-spacing: 0;
        color: #fff;
      }
      .stage-apply {
        display: flex;
        align-items: center;
        height: 24px;
        padding: 0 6px;
        margin-left: 10px;
        color: #13A449;
        font-size: 12px;
        background: rgba(19, 164, 73, 0.1);
        border-radius: 2px;
        line-height: 1;
      }
      .el-button-group {
        display: flex;
        align-items: center;
        .el-button--mini {
          display: flex;
          align-items: center;
          height: 28px;
          padding: 0 8px;
          &.el-dropdown__caret-button {
            padding: 0 1px 0 3px;
            &::before {
              top: 7px;
              bottom: 7px;
            }
          }
          .el-icon-arrow-down:before {
            content: "";
          }
        }
      }
    }
  }
}

// 连麦管理 - 拒绝/连麦按钮
.el-dropdown-menu {
  &.stage-dropdown {
    margin-top: 4px!important;
    padding: 0;
    border: 0;
    background: transparent;
    .el-dropdown-menu__item {
      padding: 0;
      .el-button {
        width: 64px;
        height: 26px;
        padding: 0;
        background: rgba(0, 110, 255, 0.5);
        border-color: rgba(0, 110, 255, 0.2);
        opacity: .8;
        &:hover {
          opacity: 1;
        }
      }
      &:focus, &:not(.is-disabled):hover {
        background: transparent;
      }
    }
  }
}

.stage-ref {
  display: flex;
  align-items: center;
  opacity: .9;
  &:hover {
    opacity: 1;
  }
}

.introduction-discuss-component {
  // 客户端-黑色主题 继承样式 ==== client-black
  &.client-black {
    overflow: auto;
    background: var(--bg-color, #1C2131);
    border: 1px solid rgba(184, 184, 184, 0.08);
    // 头部导航
    .curriculum-nav {
      background: none;
      height: 3rem;
      .el-tabs {
        &__active-bar { // 屏蔽手机端效果
          display: initial;
          border-radius: 8px;
        }
        &__item {
          height: 3rem;
          padding: 0 0.83rem!important;
          line-height: 3rem;
          color: #8A9099;
          outline: none;
          box-shadow: none;
          &.is-bottom:nth-child(2),
          &.is-top:nth-child(2) {
            padding-left: 0!important;
          }
          &.is-active {
            color: #fff;
            outline: none;
            box-shadow: none;
          }
          .tab-label { // 屏蔽手机端效果
            &:after{
              display: none;
            }
          }
        }
        &__nav-wrap {
          &:after {
            background: rgba(#B8B8B8 , .08);
          }
        }
      }
      .el-tabs__active-bar {
        height: 3px;
        background: #fff;
      }
    }

    // 简介区
    .curriculum-introduction {
      background: none;
      height: calc(100% - 3rem);
      .curriculum-introduction-title {
        padding: .83rem .67rem 0 .67rem;
        font-size: 0.83rem;
        color: #fff;
      }
      .live-intro-item {
        padding: .83rem .67rem;
        .live-intro-title {
          color: #8A9099;
        }
        .live-intro-info {
          color: rgba(#FFF,.9);
          word-break: break-all;
        }
      }
    }

    // 讨论区
    .curriculum-discuss {
      background: none;
      height: calc(100% - 3rem);
      //.el-scrollbar__wrap {
      //  padding-top: 1rem;
      //}
      .discuss-item {
        &-con {
          .ic-img {
            .new_smiley_42, .new_smiley_63, .new_smiley_66, .new_smiley_79 {
              zoom: 0.65;
            }
          }
        }

        // 看起来是尺寸相关的都改成rem单位，为了兼容大屏幕？
        .discuss-item-body {
          .bubble {
            max-width: 90%;
            min-height: inherit;
            padding: 0.3rem 0.5rem;
          }
        }
      }
      .im-component-img-control {
        display: flex;
        flex-direction: row;
        align-items: center;
        // align-content: flex-start;
        // text-align: left;
        justify-content: flex-start;

        div {
          width: 160px;
          height: 90px;
          margin: 0 4px;
          background-repeat: no-repeat;
          background-position: 50% 50%;

          &:hover {
            border-radius: 2px;
            border: 1px solid #306FF6;
          }

          &:active {
            border-radius: 2px;
            border: 1px solid #306FF6;
          }
        }

        .image-element {
          max-height: 150px;
          max-width: 200px;
        }

        .div-per {
          text-align: right;
          display: flex;
          flex-direction: column;
          align-items: flex-end;
          justify-content: center;
          width: 38px !important;
          &:hover {
            border-radius: 2px;
            border: none !important;
          }
        }

        .image-per {
          width: 24px;
          height: 24px;
          margin-right: 5px;
          background-image: url('./assets/ic_net_normal.svg');
          &:hover, &:active {
            border: none;
            opacity: 1;
          }
        }

        .image-fail {
          width: 24px;
          height: 24px;
          margin-right: 5px;
          background-image: url('./assets/ic_net_poor.svg');
          cursor: pointer;
          &:hover, &:active {
            border: none;
            opacity: 1;
            cursor: pointer;
          }
        }

        .per-font {
          font-size: 12px;
          font-family: PingFangSC-Medium, PingFang SC;
          font-weight: 500;
          color: #8A9099;
          line-height: 17px;
          margin-top: 5px;
        }
      }

      .img-mine{
        justify-content: flex-end !important;
      }
      .discuss-footer {
        background: none;
        // 右侧发送图片，静音
        .img-smg {
          display: flex;
          align-items: center;
        }
        // 客户端
        &.client {
          display: flex;
          align-items: center;
          flex-direction: column;
          border-top: 1px solid rgba(184, 184, 184, 0.08);
          height: 12.2rem;
          .discuss-footer-ops {
            display: flex;
            justify-content: space-between;
            width: 100%;
            padding: 0.125rem 0;
            > span {
              display: flex;
              align-content: center;
              align-items: center;
            }
          }
          .ic-emoji {
            width: 24px;
            height: 16px;
            display: flex;
            align-items: center;
            padding: 16px 0;;
            border-radius: 4px;
            cursor: pointer;
            transition: background-color 0.2s;
            justify-content: center;
            &:hover {
              background-color: #006EFF;
              --icon-color: #fff;
            }
            img {
              width: 16px;
              height: 16px;
              margin-right: 6px;
            }
            span {
              color: #C5CCDB;
              font-size: 12px;
              @media (max-width: 1920px) {
                display: none;
              }
            }
          }
        }
        // 客户端-ipad
        &.client-ipad {
          align-items: center;
          justify-content: space-between;
          .discuss-silence {
            display: flex;
            width: 24px;
            height: 24px;
          }

          .chat-setting-icon{
            width: 28px;
            height: 28px;
          }
        }
        // 全员静音
        .discuss-silence {
          display: flex;
          width: 16px;
          height: 16px;
          margin: 0 4px;
          i {
            width: 100%;
            height: 100%;
            background: url('./assets/ic_msg_enable.svg') center no-repeat;
            background-size: 100%;
            &:hover {
              background: url('./assets/ic_msg_enable_hover.svg') center no-repeat;
              background-size: 100%;
            }
            &.i-disable {
              background: url('./assets/ic_msg_disable.svg') center no-repeat;
              background-size: 100%;
              &:hover {
                background: url('./assets/ic_msg_disable_hover.svg') center no-repeat;
                background-size: 100%;
              }
            }
          }
        }

        .im-component-tool {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: auto;
          height: 32px;
          padding: 2px 4px;
          border-radius: 4px;
          cursor: pointer;
          transition: background-color 0.2s;
          position: relative;
          margin: 0 0 0 4px;

          &:hover {
            background-color: #006eff;
            --icon-color: #fff;
          }

          &.disabled {
            pointer-events: none;
          }

          input {
            position: absolute;
            width: 100%;
            height: 100%;
            opacity: 0;
            cursor: pointer;
          }

          .file-icon {
            width: 16px;
            height: 16px;
            margin: 0;
            // background-image: url('./assets/file.svg');
          }

          .image-icon {
            width: 16px;
            height: 16px;
            margin: 0;
            // background-image: url('./assets/image.svg');
          }

          .file-text {
            font-size: 12px;
            color: #C5CCDB;
            margin-left: 4px;
            display: none;
            @media (max-width: 1920px) {
              display: none;
            }
          }
        }
        .discuss-img-tool-file{
          display: flex;
          width: 36px;
          height: 36px;
          margin-right: 0.67rem;
          // width: 100%;
          // height: 100%;
          cursor: pointer;
          background: url('./assets/file.svg') center no-repeat;
          background-size: 78%;
          &.disabled {
            pointer-events: none;
          }
          &:hover {
            background-image: url('./assets/file_hover.svg');
            border: none
          }
        }
        .discuss-img-tool-file input{
          width: 36px !important;
          height: 36px !important;
          opacity: 0;
          cursor: pointer;
        }
        // 发送图片
        .discuss-img-tool{
          display: flex;
          width: 36px;
          height: 36px;
          margin-right: 0.67rem;
          // width: 100%;
          // height: 100%;
          cursor: pointer;
          background: url('./assets/file.svg') center no-repeat;
          background-size: 100%;
          &.disabled {
            pointer-events: none;
          }
          &:hover {
            background-image: url('./assets/file_hover.svg');
            border: none
          }
        }
        .discuss-img-tool input{
          width: 36px !important;
          height: 36px !important;
          opacity: 0;
          cursor: pointer;
        }
        .im-component-img-input{
          resize: none;
          outline: none;
          border: none;
          background: none;
          width: 100%;
          height: 88px;
          font-size: 20px;
          font-weight: 400;
          color: #ffffff;
          img {
            box-shadow: rgba(0, 0, 0, 0.15) 1.95px 1.95px 2.6px;
          }
          line-height: 28px;
          margin-top: 0;
          padding-left: 16px;
          padding-right: 16px;
          line-break: anywhere;
        }
        .im-toast {
          width: 62%;
          position: absolute;
          margin-left: 200px;
          margin-top: 90px;
          background: black;
          padding: 10px;
          border-radius: 5px;
          transform: translate(-50%, -50%);
          animation: show-toast .3s;
          color: white;
          overflow: hidden;
          display: flex;
          align-items: center;
        }
        // 回复
        .discuss-footer-reply {
          padding: 0 0.67rem;
        }
        .discuss-footer-selector {
           padding: 0 0.67rem;
         }
        // 客户端输入
        .discuss-client-input {
          flex: 1;
          resize: none;
          outline: none;
          border: none;
          background: none;
          width: 100%;
          font-size: 0.75rem;
          color: var(--text-color, #fff);
          line-height: 1.04rem;
          margin-top: 0;
          padding-left: 0.67rem;
          padding-right: 0.67rem;
          word-break: break-word;
        }
        // 客户端ipad输入
        .discuss-ipad-input {
          flex: 1;
          padding: 0 1rem 0 0.67rem;
          color: #fff;
          line-break: anywhere;
          .el-input__inner {
            width: 100%;
            height: 2rem;
            padding-left: 1.83rem;
            font-size: 0.58rem;
            line-height: 0.83rem;
            color: #fff;
            border: none;
            border-radius: 10px;
            background: rgba(0, 0, 0, 0.1);
            &::-webkit-input-placeholder {
              color: rgba(255,255,255, .3);
            }
          }
          .el-input {
            position: inherit !important;
            user-select: text;
            -webkit-user-select: text;
          }
          .discuss-ipad-sub-input {
            flex: 1;
            resize: none;
            outline: none;
            border: none;
            background: none;
            width: 100%;
            font-size: 0.75rem;
            color: #fff;
            line-height: 1.04rem;
            margin-top: 0;
            padding-left: 2.27rem;
            line-break: anywhere;
          }
          // ipad表情图标单独样式处理
          .ic-emoji {
            position: absolute;
            align-items: center;
            width: 1.33rem;
            height: 2rem;
            margin: 0 8px;
            @media (max-width: 1920px) {
              width: 1.33rem;
            }
            img {
              width: 1.33rem;
              height: 1.33rem;
              margin-bottom: 20px;
            }
            span {
              color: #C5CCDB;
              font-size: 12px;
              @media (max-width: 1200px) {
                display: none;
              }
            }
          }
        }
        // 客户端发送
        .discuss-client-btn {
          display: flex;
          width: 100%;
          padding: 0 0.67rem 0.83rem;
          justify-content: space-between;
          align-items: center;
          .el-button {
            width: 2.17rem;
            height: 1.42rem;
            padding: 0;
            font-size: 0.73rem;
            border-radius: 4px;
            border: none;
          }
          .im-component-switch-wrap {
            flex: 1;

            .im-component-msg-switch {
              margin-bottom: 15px;
    
              &:last-child {
                margin-bottom: 0;
              }
            }

            span {
              color: #eee;
              vertical-align: middle;
            }
          }
        }
      }
    }

    // 成员
    .curriculum-member {
      position: relative;
      display: flex;
      flex-direction: column;
      flex: 1;
      .cu-stage { // 连麦
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 1.87rem;
        margin: 0.41rem 1.04rem 0 1.04rem;
        label {
          font-size: 0.75rem;
          color: #fff;
        }
        .el-switch__label {
          color: #8A9099;
          font-size: 0.67rem;
        }
      }
      .cu-stage-manage { // 连麦管理
        display: flex;
        align-items: center;
        height: 1.87rem;
        margin: 0 1.04rem;
        label {
          font-size: 0.75rem;
          color: #fff;
        }
        .ic-arrow-change { // 切换小图标
          display: flex;
          width: 0.83rem;
          height: 0.83rem;
          margin-left: 2px;
          background: url('./assets/ic_pack.svg') no-repeat center;
          background-size: 0.65rem;
          transform: rotate(90deg);
          transition: all 0.2s;
          &.up {
            transform: rotate(-90deg);
            transition: all 0.2s;
          }
        }
        .i-badge { // 连麦数量
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 0.83rem;
          height: 0.83rem;
          padding: 0 0.17rem;
          margin-left: 0.25rem;
          color: #fff;
          font-size: 0.58rem;
          line-height: .83rem;
          text-align: center;
          background: #FA6400;
          border-radius: 0.42rem;
        }
      }
      .cu-member-input {
        padding: 0.7rem 0.9rem 0 0.9rem;
        .el-input__inner {
          height: 2.08rem;
          padding-left: 2.08rem;
          font-size: 0.67rem;
          background: rgba(0, 0, 0, 0.1);
          border-radius: 4px;
          border: none;
          &::-webkit-input-placeholder {
            color: rgba(255,255,255, .3);
          }
        }
        .el-input__prefix {
          display: flex;
          align-items: center;
          left: 1.5rem;
          top: 1.3rem;
          height: auto;
        }
        i.el-input__icon.el-icon-search {
          width: 1rem;
          height: 1rem;
          content: url('./assets/ic_search.svg');
          opacity: 0.9;
        }

        .el-input__clear:hover {
          color: #909399
        }

        .el-input__clear {
          color: #5a616d;
          font-size: 18px;
          cursor: pointer;
          -webkit-transition: color .2s cubic-bezier(.645, .045, .355, 1);
          transition: color .2s cubic-bezier(.645, .045, .355, 1);
          padding-top: 18px;
          margin-right: 18px;
        }

      }
      .member-list__table-wrap {
        position: relative;
        padding: 0;
        overflow-y: auto;
        margin-top: 24px;

        .member-list__table {
          border: 0;
          border-collapse: collapse;
          font-size: 14px;
          white-space: nowrap;

          ::-webkit-scrollbar {
            display: none; /* Chrome Safari */
          }

          .member-list__thead {
            display: table;
            width: calc(100% - 16px);
            table-layout: fixed;
            margin-bottom: 15px;

            .member-list__th {
              box-sizing: border-box;
              font-size: 16px;
              font-weight: inherit;
              max-height: 40px;
              color: #A1A8B4;
              text-align: center;
            }
          }

          .member-list__tbody {
            color: white;
            display: block;
            overflow-y: auto;
            overflow-x: hidden;
            border-top: 1px solid rgba(184, 184, 184, 0.1);
            position: relative;
            padding: 12px 16px 12px 0;
            min-height: 450px;

            .empty {
              height: 30px;
              line-height: 30px;
              text-align: center;
              color: #8A9099;
              position: absolute;
              top: 0;
              left: 0;
              right: 0;
              bottom: 0;
              margin: auto;
            }

            .member-list__tr {
              display: table;
              width: 100%;
              table-layout: fixed;

              .member-list__row-online {
                color: #ffffff;
              }

              .member-list__row-offline {
                color: rgba(#a1a8b4, 0.8);
              }

              &.disabled {
                .member-list__td {
                  opacity: .5;

                  &.member-list__user-name-wrap {
                    opacity: 1
                  }
                }
              }

              .member-list__td {
                text-align: center;
                padding: 4px 0;
                vertical-align: middle;
                position: relative;


                .item-wrapper {
                  padding: 18px 12px;
                  background: rgb(25, 29, 44);
                  display: flex;
                  justify-content: center;
                  height: 60px;
                }

                .context {
                  transform: scale(0.7);
                  font-family: Number;
                  position: absolute;
                  bottom: 6px;
                  left: 0;
                  right: 0;
                }

                .member-list__icon {
                  display: inline-block;
                  background-position: center;
                  background-size: contain !important;
                  cursor: pointer;
                  height: 24px;
                  vertical-align: middle;
                  padding-top: 24px;
                  opacity: .9;
                }

                .member-list__icon:hover {
                  opacity: 1;
                }

                .member-list__handup {
                  display: inline-block;
                  width: 24px;
                  height: 24px;
                  background: url('../member-list-component/assets/member-list__handup-on.svg') no-repeat center;
                  background-size: contain;
                  cursor: unset;

                  .text {
                    background: #13a449;
                    text-align: center;
                    position: absolute;
                    padding: 2px 4px;
                    transform: scale(0.5);
                    border-radius: 4px;
                    left: -10px;
                    right: -10px;
                    bottom: 4px;
                  }
                }


                .member-list__online {
                  display: inline-block;
                  min-width: 30px;
                  background: #7ED321;
                  color: black;
                  text-align: center;
                  border-radius: 2px;
                  font-size: 12px;
                  height: 16px;
                  line-height: 16px;
                }

                .member-list__offline {
                  display: inline-block;
                  min-width: 30px;
                  background: #999999;
                  color: black;
                  border: solid 1px #999999;
                  text-align: center;
                  border-radius: 4px;
                  font-size: 12px;
                  height: 16px;
                  line-height: 16px;
                }

                .member-list__stage-on {
                  max-width: 30px;
                  min-width: 30px;
                  background: url('../member-list-component/assets/member-list__stage-on.svg') no-repeat center;
                }

                .member-list__stage-off, .member-list__stage-no {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__stage-off.svg') no-repeat center;
                }

                .member-list__stage-no {
                  opacity: .5;
                }

                .member-list__mic-on {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__mic-on.svg') no-repeat center;
                }

                .member-list__mic-off, .member-list__mic-no {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__mic-off.svg') no-repeat center;
                }

                .member-list__mic-no {
                  opacity: .5;
                }

                .member-list__camera-on {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__camera-on.svg') no-repeat center;
                }

                .member-list__camera-off, .member-list__camera-no {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__camera-off.svg') no-repeat center;
                }

                .member-list__camera-no {
                  opacity: .5;
                }

                .member-list__board-on {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__board-on.svg') no-repeat center;
                }

                .member-list__board-off, .member-list__board-no {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__board-off.svg') no-repeat center;
                }

                .member-list__board-no {
                  opacity: .5;
                  cursor: default;

                  &:hover {
                    opacity: .5;
                  }
                }

                .member-list__chat-on {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__chat-on.svg') no-repeat center;
                }

                .member-list__chat-off {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__chat-off.svg') no-repeat center;
                }

                .member-list__kick-off, .member-list__kick-no {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__kick-off.svg') no-repeat center;
                }

                .member-list__kick-no {
                  opacity: .5;
                }

                .member-list__trophy, .member-list__trophy-no {
                  max-width: 24px;
                  min-width: 24px;
                  background: url('../member-list-component/assets/member-list__trophy.svg') no-repeat center;
                }

                .member-list__trophy-no {
                  opacity: .5;
                  cursor: default;

                  &:hover {
                    opacity: .5;
                  }
                }
              }

            }
          }

          .member-list__handup-wrap {
            width: 80px;
          }

          .member-list__user-name-wrap {
            overflow: hidden;
            line-height: 24px;
            max-width: 120px;
            min-width: 80px;
            width: 120px;
            text-align: left !important;
            padding-left: 16px !important;;

            .member-list__user-name {
              font-size: 20px;
              white-space: nowrap;
              text-overflow: ellipsis;
              overflow: hidden;
              line-height: 24px;
              width: calc(100% - 20px);
              text-align: left;
              display: inline-block;
              vertical-align: middle;

              .em {
                color: @--color-primary;
              }

              &.offline {
                color: #8A9099;
              }
            }

            .device-icon {
              width: 16px;
              height: 16px;
              background-size: 100% auto;
              display: inline-block;
              vertical-align: middle;

              &.pc {
                background: url('../member-list-component/assets/device-pc.svg') no-repeat center;

                &.offline {
                  background: url('../member-list-component/assets/device-pc-off.svg') no-repeat center;
                }
              }

              &.phone {
                background: url('../member-list-component/assets/device-phone.svg') no-repeat center;

                &.offline {
                  background: url('../member-list-component/assets/device-phone-off.svg') no-repeat center;
                }
              }

              &.pad {
                background: url('../member-list-component/assets/device-pad.svg') no-repeat center;

                &.offline {
                  background: url('../member-list-component/assets/device-pad-off.svg') no-repeat center;
                }
              }

              &.mini {
                background: url('../member-list-component/assets/device-mini.svg') no-repeat center;

                &.offline {
                  background: url('../member-list-component/assets/device-mini-off.svg') no-repeat center;
                }
              }

              &.unknow {
                background: url('../member-list-component/assets/device-unknow.svg') no-repeat center;

                &.offline {
                  background: url('../member-list-component/assets/device-unknow-off.svg') no-repeat center;
                }
              }
            }
          }

        }
      }
      .cu-member-pagination {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        flex: 1;
        border-top: 1px solid rgba(184, 184, 184, 0.08);
        bottom: 13px;
        position: absolute;
        width: 100%;
        padding-top: 16px;
        //flex-shrink: 0;

        .el-pagination {
          display: flex;
          align-items: center;
          justify-content: space-around;
          width: 100%;
          padding: 2px 8px !important;
          button {
            margin: auto;
            padding-left: 5px;
            background: transparent;

            .el-icon {
              width: 20px;

              &.el-icon-arrow-left {
                content: url('./assets/ic_menu_left.svg')
              }

              &.el-icon-arrow-right {
                content: url('./assets/ic_menu_right.svg')
              }
            }
          }

          button[disabled] {
            .el-icon {
              &.el-icon-arrow-left {
                content: url('./assets/ic_menu_left_disable.svg')
              }

              &.el-icon-arrow-right {
                content: url('./assets/ic_menu_right_disable.svg')
              }
            }
          }

          .el-pager {
            display: flex;
            flex: 1;
            align-items: center;
            justify-content: space-between;
            li {
              display: flex;
              align-items: center;
              justify-content: center;
              width: 1.33rem;
              height: 1.33rem;
              min-width: inherit;
              margin: auto;
              line-height: 1.33rem;
              font-size: 0.67rem;
              font-weight: normal;
              background: transparent;
              &:not(.disabled).active {
                background-color: @--color-primary;
                color: #fff;
              }
            }
          }
        }
      }
    }
  }

}




html {
  font-size: 24px;
}
.curriculum-discuss .discuss-item-con {
  user-select: text;
  pre, span {
    font-size: 0.75rem;
    user-select: text;
  }
}

// V1.3.4暂时自适应处理 -- pc
@media screen and(min-width: 1920px) {
  //1920   //2048   //2560   //3840
  //.emoji-popper {
  //  transform: scale(1.32);
  //}
}

@media screen and(min-width: 1600px) and (max-width: 1919px) {
  html {
    font-size: 23px !important;
  }
}

@media screen and(min-width: 1400px) and (max-width: 1599px) {
  html {
    font-size: 22px !important;
  }
  .introduction-discuss-component {
    &.client-black {
      .curriculum-discuss .discuss-item-con {
        .ic-img {
          .new_smiley_42, .new_smiley_63, .new_smiley_66, .new_smiley_79 {
            zoom: 0.6;
          }
        }
      }
    }
  }
}

@media screen and(min-width: 1360px) and (max-width: 1439px) {
  html {
    font-size: 21px !important;
  }
  .introduction-discuss-component {
    &.client-black {
      .curriculum-discuss {
        //说话文字
        .discuss-item-con {
          .ic-img {
            .new_smiley_42, .new_smiley_63, .new_smiley_66, .new_smiley_79 {
              zoom: 0.55;
            }
          }
        }
        .discuss-footer {
          //静音图标
          .discuss-silence {
            width: 16px;
            height: 16px;
            margin: 0 4px;
          }
          //发送图片
          .ic-translator {
            width: 30px;
            height: 30px;
            margin-right: 8px;
            img {
              width: 30px;
              height: 30px;
            }
          }
          // 发送图片
          .discuss-img-tool {
            width: 30px;
            height: 30px;
            margin-right: 10px;
            input{
              width: 30px !important;
              height: 30px !important;
            }
          }
          &.client {
            height: 270px;
            //表情图标
            .ic-emoji {
              width: 1rem;
              height: 1.5rem;
              @media (max-width: 1920px) {
                width: 1rem;
              }
              img {
                width: 1rem;
                height: 1rem;
              }
              span {
                color: #C5CCDB;
                font-size: 12px;
                @media (max-width: 1200px) {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
}

@media screen and(max-width: 1359px) {
  html {
    font-size: 21px !important;
  }
  .introduction-discuss-component {
    &.client-black {
      .curriculum-discuss {
        //说话文字
        .discuss-item-con {
          .ic-img {
            .new_smiley_42, .new_smiley_63, .new_smiley_66, .new_smiley_79 {
              zoom: 0.55;
            }
          }
        }
        .discuss-footer {
          //静音图标
          .discuss-silence {
            width: 16px;
            height: 16px;
            margin: 0 4px;
            z-index: 5;
          }
          //发送图片
          .discuss-img-tool {
            width: 28px;
            height: 28px;
            margin-right: 8px;
            input{
              width: 28px !important;
              height: 28px !important;
            }
          }
          //发送图片
          .ic-translator {
            width: 1.2rem;
            height: 1.2rem;
            margin-right: 8px;
            img{
              width: 1.2rem;
              height: 1.2rem;
            }
          }
          &.client {
            height: 270px;
            //表情图标
            .ic-emoji {
              width: 1rem;
              height: 1.2rem;
              display: flex;
              align-items: center;
              padding: 16px 0;;
              border-radius: 4px;
              cursor: pointer;
              transition: background-color 0.2s;
              // justify-content: end;
              @media (max-width: 1920px) {
                width: 1rem;
              }
              img {
                width: 1rem;
                height: 1rem;
                margin-right: 6px;
              }
              span {
                color: #C5CCDB;
                font-size: 12px;
                @media (max-width: 1200px) {
                  display: none;
                }
              }
            }
          }
        }
      }
    }
  }
}


// V1.3.4暂时自适应处理 -- 手机&iPad
//@media screen and(min-width: 960px) and (max-width: 1199px) {
//  html {
//    font-size: 16px;
//  }
//}
//
//@media screen and(min-width: 768px) and (max-width: 959px) {
//  html {
//    font-size: 14px;
//  }
//}
//
//@media screen and(min-width: 480px) and (max-width: 767px) {
//  html {
//    font-size: 12px;
//  }
//}
//
//@media screen and (max-width: 479px) {
//  html {
//    font-size: 12px;
//  }
//}
@media screen and (max-width: 479px) {
  html {
    font-size: 21px !important;
  }
}
