<template>
  <div class="big-class-portrait-video">
    <div class="class-header">
      <AutoPlayFailedMask style="width: 100%; height: 100%;">
        <div
          ref="videoAreaRef"
          class="video-area"
          :style="{
            '--video-width': videoSize.videoWidth + 'px',
            '--video-height': videoSize.videoHeight + 'px',
          }"
        >
          <VideoAreaTwo
            v-if="videoCountRef <= 2"
            :teacher-video="teacherVideo"
            :student-videos="studentVideos"
          />
          <VideoAreaMulti
            v-else
            :teacher-video="teacherVideo"
            :student-videos="studentVideos"
          />
        </div>
      </AutoPlayFailedMask>
    </div>
    <div
      class="im-area"
    >
      <PortraitIMWrap />
    </div>
  </div>
</template>
<script setup>
import { onMounted, ref, watch, onBeforeMount } from 'vue';
import VideoAreaTwo from './VideoAreaTwo.vue';
import VideoAreaMulti from './VideoAreaMulti.vue';
import PortraitIMWrap from '../components/PortraitIMWrap.vue';
import { useVideoSize } from '../../../hooks/useVideoSize';
import AutoPlayFailedMask from '../components/autoplay-failed-mask/autoplay-failed-mask.vue';
import { on } from 'events';

const props = defineProps({
  teacherVideo: Object,
  studentVideos: Array,
});

const videoAreaRef = ref();
const videoCountRef = ref(props.studentVideos.length + 1);

const videoSize = useVideoSize(videoAreaRef, videoCountRef);

onBeforeMount(() => {
  // 初始化视频区域的样式
  TCIC.SDK.instance.loadComponent('footer-component', {
    display: 'none',
  });
});

onMounted(() => {
  // initVideos({ teacherVideo: props.teacherVideo, studentVideos: props.studentVideos });
  const screenPlayerComponent = TCIC.SDK.instance.getComponent('screen-player-component');
  videoAreaRef.value.appendChild(screenPlayerComponent);
  // 不展示气泡消息
  TCIC.SDK.instance.getComponent('quickmsg-show-component').getVueInstance().quickMsgVisible = false;
});

// const initVideos = ({ teacherVideo, studentVideos }) => {
//   console.warn('bigClass-video-sync::initVideos', teacherVideo, studentVideos);
//   const promiseArr = [];
//   if (teacherVideo) {
//     promiseArr.push(TCIC.SDK.instance.updateComponent('teacher-component', {
//       left: '0',
//       top: '0',
//       width: 'var(--video-width)',
//       height: 'var(--video-height)',
//       display: 'block',
//       position: 'relative',
//     }).then(() => {
//       const ele = TCIC.SDK.instance.getComponent('teacher-component');
//       if (ele) {
//         videoAreaRef.value?.appendChild(ele);
//       }
//     }));
//   }
//   studentVideos?.forEach((info) => {
//     promiseArr.push(TCIC.SDK.instance.updateComponent('student-component', {
//       left: '0',
//       top: '0',
//       width: 'var(--video-width)',
//       height: 'var(--video-height)',
//       display: 'block',
//       position: 'relative',
//     }, info.userId).then(() => {
//       const studentDom = TCIC.SDK.instance.getComponent('student-component', info.userId);
//       if (studentDom) {
//         videoAreaRef.value?.appendChild(studentDom);
//       }
//     }));
//   });
//   return Promise.all(promiseArr);
// };

watch(
  () => [props.teacherVideo, props.studentVideos],
  ([teacherVideo, studentVideos]) => {
    // initVideos({ teacherVideo, studentVideos });
    videoCountRef.value = studentVideos.length + 1;
  },
  {
    deep: true,
  },
);

</script>

<style lang="less">
.big-class-portrait-video{
  height: 100%;
  display: flex;
  gap: 10px;
  flex-direction: column;
  .portrait-im-component{
    background-color: transparent!important;
  }
  .class-header{
    height: calc(100vw * 9 / 16);
    .video-area {
      width: 100%;
      height: 100%;
      z-index: 10;
      display: flex;
      justify-content: center;
      align-items: center;
      student-component {
        order: 2;
      }
      teacher-component {
        order: 1;
      }
    }
  }
  .im-area{
    flex: 1;
    border-top: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
  }
}
</style>
