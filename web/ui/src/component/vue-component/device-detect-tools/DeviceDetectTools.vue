<template>
    <el-dialog :title="steps[currentStep]" :visible.sync="dialogVisible" :width="dialogWidth"
        :before-close="handleClose" :append-to-body="true" :close-on-click-modal="false" :destroy-on-close="true"
        id="device-detect-tools-dialog">
        <div>
            {{ descs[currentStep] }}
            <i v-if="currentStep == 0 || currentStep == 2" class="el-icon-time" style="color: #006eff;"></i>
            <i v-if="currentStep == 1" class="el-icon-warning-outline" style="color: red;"></i>
        </div>
        <div v-show="false" ref="microphone" />
        <!-- 扬声器检查 -->
        <div v-if="isSpeaker">
            <div v-if="isSpeakerTesting || isSpeakerFinish">
                <div class="detect-item">
                    <div class="detect-label">
                        {{
        $t("扬声器")
    }}
                    </div>
                    <div class="detect-content">
                        <el-select v-model="speakerid" :placeholder="$t('请选择扬声器')" size="large"
                            @change="onSpeakerChange">
                            <el-option v-for="item in speakers" :key="item.deviceId" :label="item.label"
                                :value="item.deviceId">
                            </el-option>
                        </el-select>
                    </div>
                </div>
                <div class="volume volume-scrollbar" @click.stop @mousedown.stop @drag.stop v-if="isSpeakerTesting">
                    <i class="el-icon-message-solid primary" style="color: #006eff;"></i>
                    <div class="capacity">
                        <el-slider v-model="volume" :show-tooltip="false"
                            :format-tooltip="(item) => { return `${item}%` }" @change="onSpeakerVolumeChange" />
                    </div>
                    <el-button class="plain" size="mini" type="primary" @click="toggleAudioPlay">
                        {{ audioPlayStatus ? $t('暂停') : $t('试听') }}
                    </el-button>
                </div>
                <div class="speaker-next">
                    <el-button class="speaker-next-btn no" :disabled="!listened" size="mini" type="danger"
                        @click="noSpeaker" v-if="isSpeakerTesting">
                        {{ $t('没有听到') }}
                    </el-button>
                    <el-button class="speaker-next-btn yes" :disabled="!listened" size="mini" type="primary"
                        @click="speakerReady" v-if="isSpeakerTesting">
                        {{ $t('听到了') }}
                    </el-button>
                    <el-button class="speaker-result" :disabled="!listened" size="mini" type="primary"
                        @click="speakerResult" v-if="isSpeakerFinish">
                        {{ $t('检查结果') }}
                    </el-button>
                </div>
            </div>
        </div>
        <!-- 麦克风检测 -->
        <div v-if="isMic">
            <div class="detect-item">
                <div class="detect-label">
                    {{ $t('麦克风') }}
                </div>
                <div class="detect-content">
                    <el-select v-model="micid" :placeholder="$t('请选择麦克风')" size="large" @change="onMicrophoneChange">
                        <el-option v-for="item in mics" :key="item.deviceId" :label="item.label" :value="item.deviceId">
                        </el-option>
                    </el-select>
                </div>
            </div>
            <div class="record-progress">
                <el-progress :percentage="this.percent" class="record-progress-content"
                    :show-text="false"></el-progress>
                <el-button :disabled="this.percent < 100" :type="this.percent < 100 ? 'info' : 'primary'"
                    class="record-progress-play" @click="playRecord">{{ this.recordBtnText }}</el-button>
            </div>
            <div class="speaker-next">
                <el-button class="speaker-next-btn no" size="mini" type="danger" @click="noMic" v-if="isMicTesting"
                    :disabled="!played">
                    {{ $t('没有听到') }}
                </el-button>
                <el-button class="speaker-next-btn yes" size="mini" type="primary" @click="micReady" v-if="isMicTesting"
                    :disabled="!played">
                    {{ $t('听到了') }}
                </el-button>
                <el-button class="speaker-result" size="mini" type="primary" @click="micResult" v-if="isMicFinish"
                    :disabled="!played">
                    {{ $t('检查结果') }}
                </el-button>
            </div>
        </div>
        <!-- 结束 -->
        <div v-if="isFinish">
            <div :class="[
        'check-result',
        checkSuccess ? 'success' : 'error'
    ]">{{ checkSuccess ? $t("检查成功") : $t("检查失败") }}</div>
            <div v-if="result.speaker.status != 0" class="check-result-item">
                <div class="check-result-item-name">{{ $t('扬声器') }}</div>
                <div class="check-result-item-label">{{ result.speaker.label }}</div>
                <div class="check-result-item-icon">
                    <i class="el-icon-circle-check" style="color: #67c23a" v-if="result.speaker.status == 1"></i>
                    <i class="el-icon-warning-outline" style="color: #f56c6c" v-if="result.speaker.status == 2"></i>
                </div>
            </div>
            <div v-if="result.mic.status != 0" class="check-result-item">
                <div class="check-result-item-name">{{ $t('麦克风') }}</div>
                <div class="check-result-item-label">{{ result.mic.label }}</div>
                <div class="check-result-item-icon">
                    <i class="el-icon-circle-check" style="color: #67c23a" v-if="result.mic.status == 1"></i>
                    <i class="el-icon-warning-outline" style="color: #f56c6c" v-if="result.mic.status == 2"></i>
                </div>
            </div>
        </div>
    </el-dialog>
</template>
<script>
import i18next from 'i18next';
import { DeviceDetectBaseComponent } from '../device-detect-component/DeviceDetectBase';
import DetectUtil from '@vueComponent/device-detect-component/DetectUtil';
import BaseComponent from '@/component/core/BaseComponent';
export default {
    name: 'DetectForSetting',
    component: DeviceDetectBaseComponent,
    extends: BaseComponent,
    data() {
        return {
            dialogVisible: true,
            currentStep: 0,
            steps: [i18next.t('扬声器检测中'), i18next.t('扬声器检测完成'), i18next.t('麦克风检测中'), i18next.t('麦克风检测完成'), i18next.t('设备检查结果')],
            descs: [i18next.t('点击试听播放音乐，您听到声音了吗？'), i18next.t('所有扬声器均没有听到声音，麦克风检测可能无法正常进行。'), i18next.t('系统正在录音，请您说几句话并等待回放。'), i18next.t('所有设备均没有声音'), ''],
            getDevicesReady: false,
            speakers: [],
            mics: [],
            speaker: null,
            mic: null,
            dialogWidth: '',
            speakerid: '',
            micid: '',
            volume: 10,
            audioPlayStatus: false,
            speakerTestMediaUrl: 'https://res.qcloudclass.com/assets/detect.mp3',
            speakerTestMediaDuration: 9000,
            speakerTestSto: null,
            currentSpeakerIndex: 0,
            currentMicIndex: 0,
            mediaRecorder: null,
            audioChunks: [],
            recordTime: 0,
            checkTime: 500,
            checkAllTime: 5000,
            listened: false,
            played: false,
            result: {
                speaker: {
                    status: 0,
                    label: "",
                },
                mic: {
                    status: 0,
                    label: "",
                }
            }
        };
    },
    computed: {
        checkSuccess() {
            if (this.result.mic.status == 1 && this.result.speaker.status == 1) {
                return true;
            }
            return false;
        },
        percent() {
            return Math.ceil((this.recordTime / this.checkAllTime) * 100);
        },
        isSpeaker() {
            return this.currentStep == 0 || this.currentStep == 1;
        },
        isSpeakerTesting() {
            return this.currentStep == 0;
        },
        isSpeakerFinish() {
            return this.currentStep == 1;
        },
        isMic() {
            return this.currentStep == 2 || this.currentStep == 3;
        },
        isMicTesting() {
            return this.currentStep == 2;
        },
        isMicFinish() {
            return this.currentStep == 3;
        },
        isFinish() {
            return this.currentStep == 4;
        },
        recordBtnText() {
            return this.percent < 100 ? i18next.t('录制中') : i18next.t('播放');
        }
    },
    watch: {
        audioPlayStatus(val) {
            clearTimeout(this.speakerTestSto);
            if (val) {
                this.detectSpeaker();
            } else {
                this.stopDetectSpeaker();
            }
        }
    },
    mounted() {
        if (TCIC.SDK.instance.isWeb()) {
            this.dialogWidth = '500px';
        } else {
            this.dialogWidth = '50%';
        }
        TCIC.SDK.instance.getInstances().trtc._getDevices().then(async (devices) => {
            const tempSpeakers = [];
            const tempMics = [];
            this.getDevicesReady = true;
            for (let index = 0; index < devices.length; index++) {
                const device = devices[index];
                if (device.kind == 'audioinput') {
                    tempMics.push(device);
                }
                if (device.kind == 'audiooutput') {
                    tempSpeakers.push(device);
                }
            }
            this.speakers = tempSpeakers;
            this.mics = tempMics;
            if (tempSpeakers.length) {
                // 先判断选中，在用index
                const dvcId = await DetectUtil.getSpeakerDeviceId();
                if (dvcId) {
                    this.speaker = tempSpeakers.find((ele) => ele.deviceId == dvcId);
                    this.speakerid = dvcId;
                    const idx = tempSpeakers.findIndex((ele) => ele.deviceId == dvcId);
                }
                if (!this.speaker) {
                    this.speaker = tempSpeakers[this.currentSpeakerIndex];
                    this.speakerid = tempSpeakers[this.currentSpeakerIndex].deviceId;
                }
            }
            if (tempMics.length) {
                const dvcId = await DetectUtil.getMicDeviceId();
                if (dvcId) {
                    this.mic = tempMics.find((ele) => ele.deviceId == dvcId);
                    this.micid = dvcId;
                    const idx = tempMics.findIndex((ele) => ele.deviceId == dvcId);
                }
                if (!this.mic) {
                    this.mic = tempMics[this.currentMicIndex];
                    this.micid = tempMics[this.currentMicIndex].deviceId;
                }
            }
            // 排序 将选中的排到第一个
            this.speakers = this.speakers.sort((item) => {
                if (item.deviceId == this.speakerid) {
                    return -1;
                }
                return 0;
            });
            this.mics = this.mics.sort((item) => {
                if (item.deviceId == this.micid) {
                    return -1;
                }
                return 0;
            });
        });
    },
    methods: {
        async destroy() {
            await this.stopDetectSpeaker();
            this.recordStop();
            const audio = document.getElementById('audio-detect-player');
            if (audio) {
                document.body.removeChild(audio);
            }
        },
        async onSpeakerChange(speakerId) {
            TCIC.SDK.instance.notify('user-event', {
                type: 'change_speaker',
                param: {
                    action: `Device-Speaker`,
                    speakerId,
                },
            });
            DetectUtil.switchSpeaker(speakerId);
        },
        async onMicrophoneChange(micId) {
            TCIC.SDK.instance.notify('user-event', {
                type: 'change_mic',
                param: {
                    action: `Device-Mic`,
                    micId,
                },
            });
            DetectUtil.switchMic(micId);
        },
        async noMic() {
            const audio = document.getElementById('audio-detect-player');
            if (audio) {
                document.body.removeChild(audio);
            }
            this.played = false;
            if (this.mics.length - 1 > this.currentMicIndex) {
                const currentIndex = this.currentMicIndex + 1;
                const currentMicid = this.mics[currentIndex].deviceId;
                this.micid = currentMicid;
                const micLabel = this.mics[currentIndex].label;
                this.mic = this.mics[currentIndex];
                this.audioPlayStatus = false;
                this.currentMicIndex = currentIndex;
                await DetectUtil.switchMic(currentMicid);
                window.showToast(i18next.t(`切换到设备:${micLabel}，正在重新录音`));
                this.recordTime = 0;
                this.mediaRecorder.start(this.checkTime);
                this.descs[this.currentStep] = i18next.t('系统正在录音，请您说几句话并等待回放。');
            } else {
                this.currentStep = 3;
                this.played = true;
            }
        },
        micReady() {
            this.currentStep = 4;
            this.result.mic = {
                status: 1,
                label: this.mic.label
            };
        },
        micResult() {
            this.result.mic = {
                status: 2,
                label: this.mic.label
            };
            this.currentStep = 4;
        },
        recordFormat(per) {
            return per === 100 ? i18next.t('录制完成') : i18next.t('录制中..');
        },
        playRecord() {
            const audio = document.getElementById('audio-detect-player');
            if (audio) {
                audio.play();
                this.descs[this.currentStep] = i18next.t('系统正在播放录音，您听到声音了吗？');
                this.played = true;
            }
        },
        async speakerResult() {
            this.result.speaker = {
                status: 2,
                label: this.speaker.label
            };
            this.currentStep = 4;
            this.stopDetectSpeaker();
        },
        async noSpeaker() {
            if (this.speakers.length - 1 > this.currentSpeakerIndex) {
                const currentIndex = this.currentSpeakerIndex + 1;
                const currentSpeakerid = this.speakers[currentIndex].deviceId;
                this.speakerid = currentSpeakerid;
                const speakerLabel = this.speakers[currentIndex].label;
                this.speaker = this.speakers[currentIndex];
                this.audioPlayStatus = false;
                this.currentSpeakerIndex = currentIndex;
                this.listened = false;
                await DetectUtil.switchSpeaker(currentSpeakerid);
                window.showToast(i18next.t(`切换到设备:${speakerLabel}，点击试听重新测试`));
            } else {
                this.currentStep = 1;
            }
            this.stopDetectSpeaker();
        },
        dataavailable(event) {
            if (this.recordTime >= this.checkAllTime) {
                return;
            }
            let rct = this.recordTime;
            rct = rct + this.checkTime;
            this.recordTime = rct;
            this.audioChunks.push(event.data);
            if (this.checkAllTime == rct) {
                this.mediaRecorder.stop();
            }
        },
        createAudioElement(audioSrc) {
            // 创建audio元素
            const audio = document.createElement('audio');
            // 设置audio属性
            audio.id = "audio-detect-player";
            audio.src = audioSrc; // 设置视频源
            audio.controls = true; // 显示控制条
            audio.width = 0; // 设置视频宽度
            audio.height = 0; // 设置视频高度
            // 将audio元素插入到body中
            document.body.appendChild(audio);
        },
        recordStop(event) {
            const blob = new Blob(this.audioChunks, { type: "audio/ogg; codecs=opus" });
            const audioURL = window.URL.createObjectURL(blob);
            this.createAudioElement(audioURL);
            this.audioChunks = [];
        },
        async registerRecord() {
            // const data = await DetectUtil.startMicTest(this.$refs.microphone);
            try {
                if (navigator.mediaDevices && navigator.mediaDevices.getUserMedia) {
                    const stream = await navigator.mediaDevices
                        .getUserMedia(
                            // constraints - only audio needed for this app
                            {
                                audio: true,
                            },
                        )

                        // Error callback
                        .catch(function (err) {
                            console.log(`The following getUserMedia error occured:  ${err}`);
                        });
                    this.mediaRecorder = new MediaRecorder(stream);
                    this.mediaRecorder.ondataavailable = this.dataavailable;
                    this.mediaRecorder.onstop = this.recordStop;
                    this.mediaRecorder.start(this.checkTime);
                }
            } catch (err) {
                TCIC.SDK.instance.reportLog(
                    'reordFail',
                    `${err}`,
                );
            }
        },
        async speakerReady() {
            this.currentStep = 2;
            this.result.speaker = {
                status: 1,
                label: this.speaker.label
            };
            await this.stopDetectSpeaker();
            this.registerRecord();
        },
        stopDetectSpeaker() {
            try {
                DetectUtil.stopSpeakerTest();
            } catch (e) {
                console.error('stopDetectSpeaker', e);
            }
        },
        async onSpeakerVolumeChange(value) {
            console.log('>>>>>>>>>>>>>>  set microphone volume', value);
            try {
                await DetectUtil.setSpeakerVolume(value);
                this.volume = value;
            } catch (e) {
                console.error('set microphone volume error', e);
                TCIC.SDK.instance.reportLog(
                    'device-detect',
                    `[onSpeakerVolumeChange] [error] name: ${e.name}, message: ${e.message}`,
                );
            }
            this.updateTbmTarget();
        },
        toggleAudioPlay() {
            if (!this.audioPlayStatus) {
                // 点击试听
                this.listened = true;
            }
            this.audioPlayStatus = !this.audioPlayStatus;
        },
        async handleClose(done) {
            if (done) {
                this.dialogVisible = false;
                await this.destroy();
                await TCIC.SDK.instance.hideDeviceDetectDailog();
            }
        },
        async detectSpeaker() {
            try {
                await DetectUtil.setSpeakerVolume(this.volume);
                await DetectUtil.stopSpeakerTest();
                await DetectUtil.startSpeakerTest(this.speakerTestMediaUrl);
                this.speakerTestSto = setTimeout(() => {
                    this.audioPlayStatus = false;
                }, this.speakerTestMediaDuration);
            } catch (e) {
                console.error('detectSpeaker', e);
                this.$message.error(i18next.t('检测扬声器失败，请检查设备并重试'));
                this.audioPlayStatus = false;
                TCIC.SDK.instance.reportLog(
                    'device-detect',
                    `[detectSpeaker] [error] name: ${e.name}, message: ${e.message}`,
                );
            }
        },
    },
};
</script>
<style>
#device-detect-tools-dialog {
    .el-dialog__body {
        padding: 0px 20px 20px 20px;
    }

    .label {
        font-size: 14px;
        line-height: 38px;
        color: #fff;
        width: 112px;
        flex-shrink: 0;
    }

    .el-select {
        display: inline-block;
        position: relative;
        width: 100%;
    }

    .el-select>.el-input {
        display: block;
    }

    .el-input {
        position: relative;
        font-size: 14px;
        display: inline-block;
        width: 100%;
        border: 0;
    }

    .el-input--suffix .el-input__inner {
        padding-right: 30px;
    }

    .el-select .el-input__inner {
        cursor: pointer;
        padding-right: 35px;
    }

    .el-input__inner {
        background-color: #fff;
        background-image: none;
        border-radius: 4px;
        border: 1px solid #dcdfe6;
        box-sizing: border-box;
        color: #606266;
        display: inline-block;
        font-size: inherit;
        height: 40px;
        line-height: 40px;
        outline: none;
        padding: 0 15px;
        transition: border-color .2s cubic-bezier(.645, .045, .355, 1);
        width: 100%;
    }

    .detect-item {
        display: flex;
        align-items: center;
        padding: 10px 0;
    }

    .detect-label {
        width: 80px;
    }

    .detect-content {
        flex: 1;
    }

    .volume.volume-scrollbar {
        display: flex;
        align-items: center;
    }

    .plain {
        width: 80px;
    }

    .capacity {
        flex: 1;
        margin: 0 10px;
    }

    .speaker-next {
        display: flex;
        margin: 20px 0 0 0;
    }

    .speaker-next-btn {
        flex: 1;
        margin: 0 20px;
    }

    .speaker-result {
        flex: 1;
        margin: 0 20px;
    }

    .record-progress {
        display: flex;
        align-items: center;
    }

    .record-progress-content {
        flex: 1;
    }

    .record-progress-play {
        width: 100px;
        margin-left: 10px;
    }

    .check-result {
        text-align: center;
        line-height: 40px;
        font-size: 24px;
        font-weight: 600;
    }

    .check-result.success {
        color: #67c23a;
    }

    .check-result.error {
        color: #f56c6c;
    }

    .check-result-item {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .check-result-item-label {
        margin: 0 10px;
    }
}
</style>
