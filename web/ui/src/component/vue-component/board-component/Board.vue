<template>
  <div
    id="white-board-parent"
    ref="wbp"
    :class="['board-parent', {'small-screen': isSmallScreen}]"
  >
    <div
      v-show="!showNotStartMask"
      id="white-board"
      ref="board"
      class="board-component"
    />
    <div
      v-if="showNotStartMask"
      class="white-board-not-start"
    >
      <div class="not-start-icon" />
      <span>{{ translateTip.classNotStart }}</span>
    </div>
    <div
      v-if="showDisableMask"
      class="white-board-disable-mask"
    />
  </div>
</template>

<script>
/* global TEduBoard*/
import i18next from 'i18next';
import Constant from '@/util/Constant';
import WaterMark from '@/util/WaterMark';
import BaseComponent from '@core/BaseComponent';
import LaserIcon from './assets/laser.png';
import BoardIcon from './BoardIcon';
import Lodash from 'lodash';
import { getStartedStatus } from '@/util/class';
import DocumentUtil from '@/util/Document';
import Marquee from '@/util/Marquee';

export default {
  components: {
  },
  extends: BaseComponent,
  mixins: [BoardIcon],

  data() {
    return {
      isTeacher: false,
      isAssistant: false,
      isStudent: false,
      teduBoard: null,
      hasPermission: false,
      poorNetworkQualityTimes: 0,
      isRecordMode: false,
      defaultDocumentId: null,
      joinedBeforeClassStarted: false,
      isClassStarted: false,
      roomInfo: {},
      roleInfo: {},
      classInfo: {},
      isMicOpen: false,
      showDisableMask: false,
      slidesVideoPlayStatus: 0,
      localScaleFileTypeReg: /\.(ppt|pptx|pdf|jpeg|jpg|png|bmp|doc|docx|gif)$/i,
      micClosedByTea: false,
      assistSyncMsgBoxId: 0,
      currentScaleRatio: 100,
      boardMessageBoxShow: false,
    };
  },
  computed: {
    translateTip() {
      return {
        classNotStart: i18next.t('{{arg_0}}还未开始，请稍后...', { arg_0: this.roomInfo.name }),
      };
    },
    canTurnPage() {
      return this.isTeacher || this.hasPermission;
    },
    // 是否展示未开课下的遮罩信息
    showNotStartMask() {
      // 所有端保持一致的逻辑，不显示黑色遮罩
      return false;

      // 小屏 && 未开课 & 没有默认课件 & 是学生，这种情况下展示未开课的遮罩信息
      return this.isSmallScreen && !this.isClassStarted && !this.defaultDocumentId && this.isStudent;
    },
  },
  mounted() {
    /**
     * 有用户加入房间同步绘制涂鸦，会出现历史数据缺失渲染情况，需要主动同步
     * @param {*} board
     */
    // const syncData = (board) => {
    //   const wbp = this.$refs.wbp;
    //   console.log('Board::: whiteBoard::: ', wbp);
    //   if (wbp) {
    //     const observer = new IntersectionObserver(callback);
    //     observer.observe(wbp);
    //     function callback(entries, observer) {
    //       entries.forEach((entry) => {
    //         if (entry.isIntersecting) {
    //           console.log('Board:::白板出现元素出现在用户可视区域');
    //           setTimeout(() => {
    //           /**
    //              * 白板刷新出来时，同步获取一次历史数据
    //              * 避免丢失
    //              */
    //             console.log('Board:::判断白板是否存在', board);
    //             if (board) {
    //               board.syncAndReload();
    //             }
    //           }, 200);

    //           // 停止观察元素
    //           observer.unobserve(wbp);
    //         }
    //       });
    //     }
    //   }
    // };


    TCIC.SDK.instance.registerState(Constant.TStateBoardToolType, '白板工具类型', 0);
    TCIC.SDK.instance.registerState(Constant.TStateBoardBrushColor, '白板画刷颜色', '');

    // 初始化时获取下最新的课堂状态
    this.isClassStarted = getStartedStatus();

    // 监听加入课堂事件
    this.makeSureClassJoined(() => {
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isStudent = TCIC.SDK.instance.isStudent();
      this.addLifecycleTCICStateListener(TCIC.TMainState.Audio_Publish, (flag) => {
        this.isMicOpen = flag;
      });
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Ready, true)
          .then(async () => {
            this.teduBoard = TCIC.SDK.instance.getBoard();
            window.teduBoard = this.teduBoard;
            // syncData(this.teduBoard);
            // 添加按键事件监听
            document.addEventListener('keydown', this.onKeyboardEvent, true);
            // document.removeEventListener('keydown', this.onKeyboardEvent, true);
            this.initMouseWheelEvent();
            if (TCIC.SDK.instance.isMobile()) {
              this.initTouchScaleEvent();
              this.initTouchZoomEvent();
            }

            const userInfo = await TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId());
            this.teduBoard.setUserInfo({ nickname: userInfo.nickname });

            // 设置画笔
            this.teduBoard.setToolTypeTitle(userInfo.nickname, {
              position: TEduBoard.TEduBoardPosition.TEDU_BOARD_POSITION_RIGHT_TOP,
              size: 100,
              color: '#fff',
              style: TEduBoard.TEduBoardTextStyle.TEDU_BOARD_TEXT_STYLE_NORMAL,
              displaySelf: false,
            }, TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN);

            // 设置框选显示用户(title可不传，白板可以拿到这条线的所属人)
            this.teduBoard.setToolTypeTitle('', {
              position: TEduBoard.TEduBoardPosition.TEDU_BOARD_POSITION_LEFT_TOP,
              size: 100,
              color: '#fff',
              style: TEduBoard.TEduBoardTextStyle.TEDU_BOARD_TEXT_STYLE_NORMAL,
              displaySelf: false,
            }, TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT_SELECT);

            // 设置框选显示用户(title可不传，白板可以拿到这条线的所属人)
            this.teduBoard.setToolTypeTitle('', {
              position: TEduBoard.TEduBoardPosition.TEDU_BOARD_POSITION_RIGHT_TOP,
              size: 100,
              color: '#fff',
              style: TEduBoard.TEduBoardTextStyle.TEDU_BOARD_TEXT_STYLE_NORMAL,
              displaySelf: false,
            }, TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT);

            // 更新翻页配置
            this.updateMouseToolBehavior();

            this.teduBoard.on(TEduBoard.EVENT.TEB_H5PPT_MEDIA_STATUS_CHANGED, (fileId, mediaId, status, currentTime) => {
              console.log('TEB_H5PPT_MEDIA_STATUS_CHANGED, status: ', status);
              if (status === 4) {
                // 如果移动端视频播放不出来, 白板侧弹窗, 需要开启完整权限供用户点击
                this.boardMessageBoxShow = true;
                this.teduBoard.setDrawEnable(true);
                this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
                this.teduBoard.setMouseToolBehavior({
                  turnPage: {
                    h5PPT: true,
                    audiovisual: true,
                  },
                });
              } else if (status === 5) {
                this.boardMessageBoxShow = false;
                this.teduBoard.setDrawEnable(false);
                // 如果移动端视频播放不出来, 白板侧弹窗关闭, 需要关闭完整权限供用户点击
                setTimeout(() => {
                  this.teduBoard.setMouseToolBehavior({
                    turnPage: {
                      h5PPT: false,
                      audiovisual: true,
                    },
                  });
                }, 100);
              } else {
                this.boardMessageBoxShow = false;
              }
            });

            this.teduBoard.on(TEduBoard.EVENT.TEB_H5PPT_STATUS_CHANGED, (status, data) => {
              // 上报ppt加载状态
              try {
                const fList = this.teduBoard.getFileInfoList();
                const fid = data.fid;
                const fileInfo = fList.find(item => item.fid === fid);
                const d = {
                  fid,
                  url: fileInfo.downloadURL,
                  currentPageIndex: fileInfo.currentPageIndex,
                  currentPageStep: fileInfo.currentPageStep,
                  pageCount: fileInfo.pageCount,
                  timestamp: +new Date(),
                  uid: fileInfo.uid,
                  status,
                };
                const reportStr = JSON.stringify(d);
                if (status === TEduBoard.TEduBoardH5PPTStatus.TEDU_BOARD_H5_PPT_STATUS_LOADING) {
                  TCIC.SDK.instance.reportLog('TEduBoardH5PPTStatusLoading', reportStr);
                } else if (status === TEduBoard.TEduBoardH5PPTStatus.TEDU_BOARD_H5_PPT_STATUS_LOADED) {
                  TCIC.SDK.instance.reportLog('TEduBoardH5PPTStatusLoaded', reportStr);
                } else {
                  TCIC.SDK.instance.reportLog('TEduBoardH5PPTStatusError', reportStr);
                }
              } catch (e) {
                console.error('report TEduBoardH5PPTStatus error', e);
              }
            });
            this.teduBoard.on(TEduBoard.EVENT.TEB_IMAGE_STATUS_CHANGED, (status, data) => {
              // 上报image加载状态
              try {
                const d = {
                  url: data.imgUrl,
                  currentBoardId: data.currentBoardId,
                  timestamp: +new Date(),
                  status,
                };
                const reportStr = JSON.stringify(d);
                if (status === TEduBoard.TEduBoardImageStatus.TEDU_BOARD_IMAGE_STATUS_LOADING) {
                  TCIC.SDK.instance.reportLog('TEduBoardImageStatusLoading', reportStr);
                } else if (status === TEduBoard.TEduBoardH5PPTStatus.TEDU_BOARD_H5_PPT_STATUS_LOADED) {
                  TCIC.SDK.instance.reportLog('TEduBoardImageStatusLoaded', reportStr);
                } else {
                  TCIC.SDK.instance.reportLog('TEduBoardImageStatusError', reportStr);
                }
              } catch (e) {
                console.error('report TEduBoardImageStatus error', e);
              }
            });
            this.teduBoard.on(TEduBoard.EVENT.TEB_VIDEO_STATUS_CHANGED, (data) => {
              // 上报video加载状态
              try {
                const status = data.status;
                const fid = data.fileId;
                const fList = this.teduBoard.getFileInfoList();
                const fileInfo = fList.find(item => item.fid === fid);
                const d = {
                  fid,
                  url: fileInfo.downloadURL,
                  timestamp: +new Date(),
                  status: data.status,
                };
                const reportStr = JSON.stringify(d);
                if (status === TEduBoard.TEduBoardVideoStatus.TEDU_BOARD_VIDEO_STATUS_LOADING) {
                  TCIC.SDK.instance.reportLog('TEduBoardVideoStatusLoading', reportStr);
                } else if (status === TEduBoard.TEduBoardVideoStatus.TEDU_BOARD_VIDEO_STATUS_LOADED) {
                  TCIC.SDK.instance.reportLog('TEduBoardVideoStatusLoaded', reportStr);
                } else if (status === TEduBoard.TEduBoardVideoStatus.TEDU_BOARD_VIDEO_STATUS_ERROR) {
                  TCIC.SDK.instance.reportLog('TEduBoardVideoStatusError', reportStr);
                }
              } catch (e) {
                console.error('report TEduBoardVideoStatus error', e);
              }
              // 上报video加载状态 end
              switch (data.status) {
                case TEduBoard.VIDEO_STATUS.TEDU_BOARD_VIDEO_STATUS_LOADED:
                case TEduBoard.VIDEO_STATUS.TEDU_BOARD_VIDEO_STATUS_ENDED:
                  this.hideVideoTrackDisplay();
                  if (this.isStudent && !this.isMicOpen) {
                    const { enableDirectControl } = this.classInfo;
                    if (this.micClosedByTea && enableDirectControl === 1) {
                      const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                      if (myDom && myDom.getVueInstance()) {
                        myDom.getVueInstance().enableMic(false);
                        this.micClosedByTea = false;
                      }
                      window.showToast(i18next.t('麦克风已打开'));
                    }
                  }
                  break;
                case TEduBoard.VIDEO_STATUS.TEDU_BOARD_VIDEO_STATUS_PLAYED:
                  TCIC.SDK.instance.setDocumentVideoPlaying(true);
                  if (this.isStudent && this.isMicOpen) {
                    const { enableDirectControl } = this.classInfo;
                    if (enableDirectControl === 1) {
                      const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                      if (myDom && myDom.getVueInstance()) {
                        myDom.getVueInstance().enableMic(false);
                        this.micClosedByTea = true;
                      }
                      window.showToast(i18next.t('为避免回声干扰，麦克风默认关闭。播放结束后将自动开启'));
                    } else {
                      TCIC.SDK.instance.showMessageBox(
                          i18next.t('提示'),
                          i18next.t('为了避免回声问题，请关闭您的麦克风'),
                          [i18next.t('确定'), i18next.t('取消')], (index) => {
                            if (index === 0) {
                              const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                              if (myDom && myDom.getVueInstance()) {
                                myDom.getVueInstance().enableMic(false);
                              }
                            }
                          },
                      );
                    }
                  }
                  break;
                case TEduBoard.VIDEO_STATUS.TEDU_BOARD_VIDEO_STATUS_PAUSED:
                  if (this.isStudent && !this.isMicOpen) {
                    const { enableDirectControl } = this.classInfo;
                    if (this.micClosedByTea && enableDirectControl == 1) {
                      const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                      if (myDom && myDom.getVueInstance()) {
                        myDom.getVueInstance().enableMic(true);
                        this.micClosedByTea = false;
                      }
                      window.showToast(i18next.t('麦克风已打开'));
                    }
                  }
                  break;
              }
            });
            this.teduBoard.on(TEduBoard.EVENT.TEB_AUDIO_STATUS_CHANGED, (data) => {
              switch (data.status) {
                case TEduBoard.TEduBoardAudioStatus.TEDU_BOARD_AUDIO_STATUS_PLAYED:
                  if (this.isStudent && this.isMicOpen) {
                    const { enableDirectControl } = this.classInfo;
                    if (enableDirectControl === 1) {
                      const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                      if (myDom && myDom.getVueInstance()) {
                        myDom.getVueInstance().enableMic(false);
                        this.micClosedByTea = true;
                      }
                      window.showToast(i18next.t('为避免回声干扰，麦克风默认关闭。播放结束后将自动开启'));
                    } else {
                      const detecting = TCIC.SDK.instance.getState(Constant.TStateDeviceDetect, true);
                      if (detecting) {
                        return;
                      }
                      TCIC.SDK.instance.showMessageBox(
                          i18next.t('提示'),
                          i18next.t('为了避免回声问题，请关闭您的麦克风'),
                          [i18next.t('确定'), i18next.t('取消')], (index) => {
                            if (index === 0) {
                              const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                              if (myDom && myDom.getVueInstance()) {
                                myDom.getVueInstance().enableMic(false);
                              }
                            }
                          },
                      );
                    }
                  }
                  break;
                case TEduBoard.TEduBoardAudioStatus.TEDU_BOARD_AUDIO_STATUS_PAUSED:
                  if (this.isStudent && !this.isMicOpen) {
                    const { enableDirectControl } = this.classInfo;
                    if (this.micClosedByTea && enableDirectControl == 1) {
                      const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                      if (myDom && myDom.getVueInstance()) {
                        myDom.getVueInstance().enableMic(true);
                        this.micClosedByTea = false;
                      }
                      window.showToast(i18next.t('麦克风已打开'));
                    }
                  }
                  break;
                case TEduBoard.TEduBoardAudioStatus.TEDU_BOARD_AUDIO_STATUS_ENDED:
                  if (this.isStudent && !this.isMicOpen) {
                    const { enableDirectControl } = this.classInfo;
                    if (this.micClosedByTea && enableDirectControl == 1) {
                      const myDom = TCIC.SDK.instance.getComponent('student-component', TCIC.SDK.instance.getUserId());
                      if (myDom && myDom.getVueInstance()) {
                        myDom.getVueInstance().enableMic(true);
                        this.micClosedByTea = false;
                      }
                      window.showToast(i18next.t('麦克风已打开'));
                    }
                  }
                  break;
              }
            });
            this.renderMarquee();
            this.currentScaleRatio = this.teduBoard.getBoardScale();
          });


      TCIC.SDK.instance
          .promiseState(TCIC.TMainState.Board_Ready, true)
          .then(() => {
            if (TCIC.SDK.instance.isStudent()) {
              // 学生只能访问自己的操作，2.8.0之前接口，之后版本用下列接口
              // this.teduBoard.setAccessibleUsers([TCIC.SDK.instance.getUserId()], [0], []);
              this.teduBoard.enablePermissionChecker(['Element::*::*'], [`operator/${TCIC.SDK.instance.getUserId()}`]);
            }

            /** ***  需要主动设置一下鼠标的样式，远端涂鸦会应用本地的样式，如果不设置，相当于本地没有样式，则会显示不出来 *****/
            // 设置画笔图标
            this.setCursor(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN);
            // 设置直线图标
            this.setCursor(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LINE);
            // 设置矩形图标
            this.setCursor(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT);
            // 设置正方形图标
            this.setCursor(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_SQUARE);
            // 设置椭圆图标
            this.setCursor(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_OVAL);
            // 设置正圆图标
            this.setCursor(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_CIRCLE);
            /** ***  需要主动设置一下鼠标的样式，远端涂鸦会应用本地的样式，如果不设置，相当于本地没有样式，则会显示不出来 *****/

            // 设置橡皮擦自定义图标
            this.teduBoard.setCursorIcon(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ERASER, {
              cursor: 'url',
              url: 'https://res.qcloudtiw.com/board/icons/xkt/eraser.svg',
              offsetX: 7,
              offsetY: 25,
            });

            // 设置激光笔图标
            this.teduBoard.setCursorIcon(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LASER, {
              cursor: 'url',
              url: LaserIcon,
              offsetX: 20,
              offsetY: 20,
            });

            // 白板工具类型及画刷颜色变化后，更新光标
            this.addLifecycleTCICStateListener(Constant.TStateBoardToolType, this.updateCursor);
            this.addLifecycleTCICStateListener(Constant.TStateBoardBrushColor, this.updateCursor);
          });

      TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Ready, true)
          .then(() => {
            // 启用笔锋
            this.teduBoard.setHandwritingEnable(true);
            const hasBoardPermission = TCIC.SDK.instance.getState(TCIC.TMainState.Board_Permission, false);
            console.log(`[Board] Board_Ready, hasBoardPermission ${hasBoardPermission}`);
            if (hasBoardPermission) {  // 默认选择画笔工具
              this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN);
            } else {
              this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
            }

            // 上课状态监听
            this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, async (status) => {
              console.log('Board::;;. Class_Status', TCIC.TMainState.Class_Status);

              const hasBoardPermission = TCIC.SDK.instance.getState(TCIC.TMainState.Board_Permission, false);
              if (status === TCIC.TClassStatus.Not_Start) {
                if (TCIC.SDK.instance.isClassLayoutHasDoc()) { // 有白板的才提示
                  TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false)
                      .then(() => {
                        if (this.isTeacher) {
                          window.showToast(i18next.t('{{arg_0}}尚未开始，白板操作不会记录和同步', { arg_0: this.roomInfo.name }), 'show', { duration: 5000 });
                        }
                      });
                }
                await this.boardPermissionChangeHandler(hasBoardPermission);
                this.loadDefaultDocument();
                this.joinedBeforeClassStarted = true;
              } else if (status === TCIC.TClassStatus.Already_Start) {
                // 更新上课开始的标识
                this.isClassStarted = true;
                if (this.joinedBeforeClassStarted) {
                  this.teduBoard.setDataSyncEnable(false);
                  this.teduBoard.clear();
                  if (this.defaultDocumentId) {
                    this.teduBoard.deleteFile(this.defaultDocumentId);
                    if (this.isTeacher) {
                      setTimeout(() => {
                        this.loadDefaultDocument();
                      }, 100);
                    }
                  }
                }
                await this.boardPermissionChangeHandler(hasBoardPermission);

                setTimeout(() => {
                  // 上课后, 同步一次历史, 修复偶发的丢失部分白板笔划的问题..
                  // this.teduBoard.syncAndReload();
                  console.warn('%c [ syncAndReload test. ]-314', 'font-size:13px; background:pink; color:#bf2c9f;');
                }, 500);
              }
            });
          });

      // 监听白板授权状态
      this.addLifecycleTCICStateListener(TCIC.TMainState.Board_Permission, async (value, oldValue, reason, operatorId) => {
        if (!TCIC.SDK.instance.checkPermission(TCIC.TResourcePath.Board, TCIC.TPermissionFlag.Read)) {
          return;
        }
        this.setAbility(value, reason, operatorId);
        await this.boardPermissionChangeHandler(value);
        // 修改 boardTool 可见性
        TCIC.SDK.instance.setState(Constant.TStateIsShowBoardToolComponent, value);
      });

      // 监听网络状态
      this.addLifecycleTCICEventListener(TCIC.TStatisticsEvent.Network_Statistics, (statistics) => {
        if (statistics.networkQuality === TCIC.TNetworkQuality.Vbad
            || statistics.networkQuality === TCIC.TNetworkQuality.bad
            || statistics.networkQuality === TCIC.TNetworkQuality.Poor) {
          this.poorNetworkQualityTimes += 1;
        } else {
          if (this.poorNetworkQualityTimes > 3) {
            // 连续>3次网络不佳，网络恢复时，刷新白板
            if (this.teduBoard && this.teduBoard.historySyncDone) {
              // 在上课之前预览时,此调用貌似会错误的清空白板.. 判断条件要在history事件和上课之后
              this.teduBoard.syncAndReload();
            }
          }
          this.poorNetworkQualityTimes = 0;
        }
      });

      // customJS 配置水印.
      TCIC.SDK.instance.on(TCIC.TMainEvent.WaterMark_Update, this.renderWaterMark);
      TCIC.SDK.instance.on(TCIC.TMainEvent.Marquee_Update, this.renderMarquee);
      TCIC.SDK.instance.on(TCIC.TMainEvent.Before_Leave, () => {
        if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
          DocumentUtil.addSnapshotMark('beforeLeave');
        }
      });
      this.roomInfo = TCIC.SDK.instance.getRoleInfo().roomInfo;
      this.roleInfo = TCIC.SDK.instance.getRoleInfo().roleInfo;
      this.classInfo = TCIC.SDK.instance.getClassInfo();
    });

    this.addLifecycleTCICStateListener(Constant.TStateRecordMode, (recordMode) => {
      this.isRecordMode = recordMode;
    });
    // 三分屏布局 + 大视频模式，禁用白板
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, (layout) => {
      this.updateDisableMask();
      this.renderMarquee();
    });
    this.addLifecycleTCICStateListener(Constant.TStateBigVideoMode, (isBigVideoMode) => {
      this.updateDisableMask();
      this.renderMarquee();
    });
    // 窗口大小变更
    this.addLifecycleTCICEventListener(Constant.TEventComponentUpdateLayout, this.onTargetLayoutChange);
    // window.addEventListener('resize', this.onResize);
    this.elementRo = new MutationObserver((entries, observer) => {
      this.onResize();
    });
    this.elementRo.observe(this.$el, { attributes: true, childList: true, subtree: true });
    this.elementIo = new IntersectionObserver((entries) => {
      this.onResize();
    });
    this.elementIo.observe(this.$el, { attributes: true, childList: true, subtree: true });
  },
  beforeDestroy() {
    // 窗口大小变更
    window.removeEventListener('resize', this.onResize);
    this.elementIo.disconnect();
    this.elementRo.disconnect();
  },
  methods: {
    addOrRemovePointerUpEvent(status) {
      // 移除 pointerup 事件，避免重复绑定
      this.theTCICComponent.removeEventListener('pointerup', this.pointerUpHandler);

      // 如果是状态5，绑定 pointerup 事件
      if (status === 5) {
        this.pointerUpHandler = (e) => {
          if (this.isStudent && this.isClassStarted && !this.hasPermission) {
            this.teduBoard.setDrawEnable(false);
          }
        };
        this.theTCICComponent.addEventListener('pointerup', this.pointerUpHandler);
      }
    },
    hideVideoTrackDisplay() { // 隐藏视频上层元素
      const videoTrackDisplay = document.getElementsByClassName('vjs-text-track-display');
      if (videoTrackDisplay.length > 0) {
        videoTrackDisplay[0].style.display = 'none';
      }
    },
    updateDisableMask() {
      // 三分屏布局 + 大视频模式，禁用白板
      const classLayout = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top);
      const isBigVideoMode = TCIC.SDK.instance.getState(Constant.TStateBigVideoMode, false);
      const showDisableMask = !this.isSmallScreen && classLayout === TCIC.TClassLayout.Three && isBigVideoMode;
      if (this.showDisableMask === showDisableMask) {
        return;
      }
      this.showDisableMask = showDisableMask;
      TCIC.SDK.instance.reportLog('boardDisableMask', `showDisableMask ${this.showDisableMask}, classLayout ${classLayout}, isBigVideoMode ${isBigVideoMode}`);
    },
    setAbility(enable, reason, operatorId = '') {
      if (enable === this.hasPermission) {
        return;
      }
      this.hasPermission = enable;
      const isTeacherOperator = TCIC.SDK.instance.getClassInfo().teacherId === operatorId || !operatorId;
      if (!this.isTeacher && !this.isAssistant) {
        if (enable) {
          TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
            const isOneOnOneVideoClass = TCIC.SDK.instance.isOneOnOneClass() && !TCIC.SDK.instance.isClassLayoutHasDoc();
            if (!isOneOnOneVideoClass && this.hasPermission) {
              window.showToast(i18next.t('{{arg_0}}开启了你的白板权限', { arg_0: isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant }));
            }
          });
        } else if (reason !== TCIC.TPermissionUpdateReason.KickOut && reason !== TCIC.TPermissionUpdateReason.KickOutForever) {
          window.showToast(i18next.t('{{arg_0}}关闭了你的白板权限', { arg_0: isTeacherOperator ? this.roleInfo.teacher : this.roleInfo.assistant }));
        }
      }
    },

    // 设置白板工具
    setToolType(type) {
      this.teduBoard.setToolType(type);
      TCIC.SDK.instance.setState(Constant.TStateBoardToolType, type);
    },
    // 设置白板箭头
    setGraphStyle(style) {
      this.teduBoard.setGraphStyle(style);
      // TCIC.SDK.instance.setState(Constant.TStateBoardToolType, type);
    },

    setCursor(toolType) {
      if (toolType === TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN) {
        const cursorBase64 = BoardIcon.pen();
        this.teduBoard.setCursorIcon(toolType, {
          cursor: 'url',
          url: cursorBase64,
          offsetX: 10,
          offsetY: 40,
        });
      } else if (toolType === TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LINE) {
        const cursorBase64 = BoardIcon.line();
        this.teduBoard.setCursorIcon(toolType, {
          cursor: 'url',
          url: cursorBase64,
          offsetX: 20,
          offsetY: 20,
        });
      } else if (toolType === TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_RECT) {
        const cursorBase64 = BoardIcon.rect();
        this.teduBoard.setCursorIcon(toolType, {
          cursor: 'url',
          url: cursorBase64,
          offsetX: 20,
          offsetY: 20,
        });
        // 矩形也设置一下(当按住shift键的时候，工具会切换为矩形)
        this.teduBoard.setCursorIcon(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_SQUARE, {
          cursor: 'url',
          url: cursorBase64,
          offsetX: 20,
          offsetY: 20,
        });
      } else if (toolType === TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_OVAL) {
        const cursorBase64 = BoardIcon.oval();
        this.teduBoard.setCursorIcon(toolType, {
          cursor: 'url',
          url: cursorBase64,
          offsetX: 20,
          offsetY: 20,
        });
        // 正圆也设置一下(当按住shift键的时候，工具会切换为正圆)
        this.teduBoard.setCursorIcon(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_CIRCLE, {
          cursor: 'url',
          url: cursorBase64,
          offsetX: 20,
          offsetY: 20,
        });
      }
    },

    updateCursor() {
      if (!this.teduBoard) return;

      const toolType = this.teduBoard.getToolType();
      const brushColor = this.teduBoard.getBrushColor();
      this.setCursor(toolType, brushColor);

      // 系统光标对阴影滤镜支持不好，激光笔渐变部分会出现黑色阴影，目前先将激光笔用模拟的方式来实现，其他工具继续使用系统光标
      if (toolType === TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_LASER) {
        this.teduBoard.setSystemCursorEnable(false);
      } else {
        this.teduBoard.setSystemCursorEnable(true);
      }
      this.setToolType(toolType);  // 马上再更新一下工具，保证光标生效（白板bug）
    },

    /**
     * 白板权限变更处理
     */
    async boardPermissionChangeHandler(hasPermission) {
      const userId = TCIC.SDK.instance.getUserId();
      await TCIC.SDK.instance.promiseState(TCIC.TMainState.Board_Ready, true)
          .then(() => {
            this.teduBoard = TCIC.SDK.instance.getBoard();
            const classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status);
            const notStart = classStatus === TCIC.TClassStatus.Not_Start;
            console.log(`[Board] boardPermissionChange, hasPermission ${hasPermission}, classStatus ${classStatus}`);

            // 统一开课前数据不做同步的特性
            if (notStart) {
              this.teduBoard.setDataSyncEnable(false);
              console.warn('%c [ setDataSyncEnable ]-504', 'font-size:13px; background:pink; color:#bf2c9f;', false);
            } else {
              const isAssistant = TCIC.SDK.instance.isAssistant();
              if (isAssistant) { // 如果是助教， 先不开启同步
                this.teduBoard.setDataSyncEnable(false);
                TCIC.SDK.instance.promiseState(Constant.TStateDeviceDetect, false).then(() => {
                  if (this.assistSyncMsgBoxId) {
                    return;
                  }
                  this.assistSyncMsgBoxId = TCIC.SDK.instance.showMessageBox(
                      i18next.t('注意'),
                      i18next.t('您的操作将同步至老师和学生'),
                      [i18next.t('我知道了')], (index) => { // 开启白板同步
                        if (index === 0) {
                          this.teduBoard.setDataSyncEnable(true);
                          this.assistSyncMsgBoxId = 0;
                        }
                      },
                  );
                });
              } else {
                this.teduBoard.setDataSyncEnable(hasPermission);
              }
              console.warn('%c [ setDataSyncEnable ]-507', 'font-size:13px; background:pink; color:#bf2c9f;', hasPermission);
            }

            this.teduBoard.setDrawEnable(true);
            this.teduBoard.showVideoControl(hasPermission);

            if (hasPermission) {
              // 如果有白板权限, 则默认切换为画笔工具
              this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_PEN);
            } else {
              // 如果没有白板权限，则切换为鼠标工具
              this.teduBoard.setDataSyncEnable(false);
              this.teduBoard.setDrawEnable(true);
              this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
              this.teduBoard.setMouseToolBehavior({
                turnPage: {
                  h5PPT: false,
                  audiovisual: true,
                },
              });
            }
            if (!notStart && this.isStudent) {
              setTimeout(() => {
                this.teduBoard.enablePermissionChecker(['Board::Switch::Step'], hasPermission ? [`operator/${userId}`] : ['operator/']);
                this.teduBoard.enablePermissionChecker(['Board::Switch::Page'], hasPermission ? [`operator/${userId}`] : ['operator/']);
              }, 100);
            }

            // 更新翻页配置
            this.updateMouseToolBehavior();
          });
    },

    initMouseWheelEvent() {
      let wheelTimeout = 0;
      document.onmousewheel = (e) => {   // 鼠标滚轮事件
        if (wheelTimeout) {
          clearTimeout(wheelTimeout);
          wheelTimeout = null;
        }
        wheelTimeout = setTimeout(() => {
          // https://juejin.cn/post/7177645078146449466
          this.processWheelEvent(e.wheelDelta, e.target);
        }, 64);
      };
    },
    initTouchScaleEvent() {
      const board = this.theTCICComponent;
      board.addEventListener('pointerdown', (e) => {
        const fid = this.teduBoard.getCurrentFile();
        const fileInfo = this.teduBoard.getFileInfo(fid);
        const type = `${fileInfo.title}`.toLowerCase();
        if (!this.localScaleFileTypeReg.test(type)) {
          return;
        }
        if (this.isStudent && !this.hasPermission) {
          // this.teduBoard.setDrawEnable(true);
          if (this.currentScaleRatio > 100 && !this.boardMessageBoxShow) {
            this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG);
          } else {
            this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
          }
        }
      });
    },

    initTouchZoomEvent() {
      let startDistance = 0;
      let lastScale = 1;

      const board = this.theTCICComponent;
      if (!board) return;

      board.addEventListener('touchstart', (e) => {
        if (e.touches.length === 2) {
          const fid = this.teduBoard.getCurrentFile();
          const fileInfo = this.teduBoard.getFileInfo(fid);
          const type = `${fileInfo.title}`.toLowerCase();

          if (!this.localScaleFileTypeReg.test(type)) {
            startDistance = 0;
            return;
          }

          if (!this.canTurnPage || !TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT')) {
            startDistance = this.getTouchDistance(e.touches);
            lastScale = this.teduBoard.getBoardScale();
          } else {
            startDistance = 0;
          }
        }
      });

      board.addEventListener('touchmove', (e) => {
        if (e.touches.length === 2) {
          e.preventDefault();

          if (startDistance === 0) return;

          const currentDistance = this.getTouchDistance(e.touches);
          const scaleFactor = currentDistance / startDistance;

          if (!this.canTurnPage || !TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT')) {
            this.scaleLocalPageByTouch(scaleFactor, lastScale);
          }
        }
      });

      board.addEventListener('touchend', (e) => {
        if (e.touches.length < 2) {
          startDistance = 0;
        }
      });
    },

    getTouchDistance(touches) {
      const dx = touches[0].clientX - touches[1].clientX;
      const dy = touches[0].clientY - touches[1].clientY;
      return Math.sqrt(dx * dx + dy * dy);
    },

    scaleLocalPageByTouch(scaleFactor, lastScale) {
      const fid = this.teduBoard.getCurrentFile();
      const fileInfo = this.teduBoard.getFileInfo(fid);
      const type = `${fileInfo.title}`.toLowerCase();

      const newScale = Math.max(lastScale * scaleFactor, 100);

      this.teduBoard.setBoardScale(newScale);
      this.currentScaleRatio = newScale;
      this.teduBoard.setDataSyncEnable(false);
      // this.teduBoard.setDrawEnable(true);
      if (this.currentScaleRatio > 100) {
        this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG);
      } else {
        this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);
      }
    },

    processWheelEvent(delta, target) {
      const isGrandChild = (parent, grandEl) => {
        let currentEl = grandEl;
        while (currentEl && currentEl.parentElement !== null) {
          if (currentEl.parentElement === parent) {
            return true;
          }
          currentEl = currentEl.parentElement;
        }
        return false;
      };

      const board = this.theTCICComponent;
      const isBoardWheel = isGrandChild(board, target);
      if (!isBoardWheel) {
        return;
      }

      if (!this.canTurnPage || !TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT')) {
        // 禁止翻页
        console.warn('wheel auth:', this.canTurnPage);
        if (this.isStudent) {
          this.scaleLocalPage(delta);
        }
        return;
      }

      const fid = this.teduBoard.getCurrentFile();
      const fileInfo = this.teduBoard.getFileInfo(fid);
      if (!fileInfo) {
        console.error('processWheelEvent fileInfo is null');
        TCIC.SDK.instance.reportLog('getFileInfoError', 'processWheelEvent fileInfo is null');
        return;
      }
      const type = `${fileInfo.title}`.toLowerCase();

      if (this.hasPermission && isBoardWheel && fileInfo) {
        const isPdfOrDoc = /\.(pdf|doc|docx)$/.test(type);

        if (isPdfOrDoc) {
          const scale = this.teduBoard.getBoardScale();
          const scrollOffset = this.teduBoard.getBoardScroll();
          // 当前页面没有放大
          if (scale === 100) {
            if (delta > 0) {
              // 滑到顶
              if (fileInfo.currentPageIndex > 0 && TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT.WheelPaging')) {
                this.teduBoard.prevBoard();
                setTimeout(() => {
                  // 滑到上页底部
                  this.teduBoard.setScaleAnchor(0.5, 1);
                }, 0);
              }
            } else if (delta < 0) {
              //  滑到底
              if (fileInfo.currentPageIndex + 1 < fileInfo.pageCount && TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT.WheelPaging')) {
                this.teduBoard.nextBoard();
                setTimeout(() => {
                  // 滑到下页顶部
                  this.teduBoard.setScaleAnchor(0.5, 0);
                }, 0);
              }
            } else {
              // do nothing，自然滑动即可
            }
          } else {
            // 当前页面放大了
            if (scrollOffset.scrollTop <= 0) {
              // 滑到顶
              if (fileInfo.currentPageIndex > 0  && TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT.WheelPaging')) {
                this.teduBoard.prevBoard();
                setTimeout(() => {
                  // 滑到上页底部
                  this.teduBoard.setScaleAnchor(0.5, 1);
                }, 0);
              }
            } else if (scrollOffset.scrollTop >= 1) {
              //  滑到底
              if (fileInfo.currentPageIndex + 1 < fileInfo.pageCount  && TCIC.SDK.instance.isFeatureAvailable('WhiteBoardPPT.WheelPaging')) {
                this.teduBoard.nextBoard();
                setTimeout(() => {
                  // 滑到下页顶部
                  this.teduBoard.setScaleAnchor(0.5, 0);
                }, 0);
              }
            } else {
              // do nothing，自然滑动即可
            }
          }
        } else {
          // 学生不许翻页
          if (this.isStudent) {
            console.warn('学生禁止翻页');
            return;
          }
          if (delta < 0) { // 上滑
            if (fileInfo.currentPageIndex + 1 < fileInfo.pageCount) {
              this.teduBoard.nextBoard();
            }
          } else {
            if (fileInfo.currentPageIndex > 0) {
              this.teduBoard.prevBoard();
            }
          }
        }
      }
    },

    onKeyboardEvent(e) {
      // 上课后学生禁止翻页
      if (this.isClassStarted && !TCIC.SDK.instance.isTeacher()) {
        return;
      }
      // const fid = this.teduBoard.getCurrentFile();
      // const fileInfo = this.teduBoard.getFileInfo(fid);
      // isPdfOrDoc(fileInfo) {
      //   const type = `${fileInfo.title}`.toLowerCase();
      //   const isPdfOrDoc = /\.(pdf|doc|docx)$/.test(type);
      //   return isPdfOrDoc;
      // },
      const isComKey = e.ctrlKey || e.shiftKey || e.altKey || e.metaKey;
      if (isComKey) {
        return;
      }

      const activeElement = document.activeElement;
      const isInputting =          activeElement
          && (
              activeElement.tagName === 'INPUT'
              || activeElement.tagName === 'TEXTAREA'
              || activeElement.isContentEditable
          );

      // normalize e.which for key events
      // @see http://stackoverflow.com/questions/4285627/javascript-keycode-vs-charcode-utter-confusion
      if (typeof e.which !== 'number') {
        e.which = e.keyCode;
      }
      try {
        // key: "ArrowLeft", keyCode: 37
        switch (e.which) {
          case 37: // 左
          case 38:
            if (!isInputting) {
              DocumentUtil.intervalChangePage(this.teduBoard, 'prev');
            }
            break;
            // case 38: // 上
            //   if (fileInfo.currentPageIndex > 0) {
            //     this.teduBoard.prevBoard();
            //   }
            //   break;
          case 39: // 右
          case 40:
            if (!isInputting) {
              DocumentUtil.intervalChangePage(this.teduBoard, 'next');
            }
            break;
            // case 40: // 下
            //   if (fileInfo.currentPageIndex + 1 < fileInfo.pageCount) {
            //     this.teduBoard.nextBoard();
            //     // setTimeout(() => {
            //     //   // 滑到下页顶部
            //     //   this.teduBoard.setScaleAnchor(0.5, 0);
            //     // }, 0);
            //   }
            //   break;
          default:
            break;
        }
      } catch (e) {
        console.error('Board:onKeyboardEvent error ', e);
      }
    },

    scaleLocalPage(delta) {
      const fid = this.teduBoard.getCurrentFile();
      const fileInfo = this.teduBoard.getFileInfo(fid);
      const type = `${fileInfo.title}`.toLowerCase();
      if (this.localScaleFileTypeReg.test(type)) {
        const boardScale = this.teduBoard.getBoardScale();
        if (delta > 0) {
          // 放大当前页面
          this.currentScaleRatio = boardScale + 20;
          this.teduBoard.setBoardScale(this.currentScaleRatio);
        } else {
          // 缩小当前页面
          this.currentScaleRatio = Math.max(boardScale - 20, 100);
          this.teduBoard.setBoardScale(this.currentScaleRatio);
        }
        this.teduBoard.setDataSyncEnable(false);
        console.warn('%c [ setDataSyncEnable ]-722', 'font-size:13px; background:pink; color:#bf2c9f;', false);
        // this.teduBoard.setDrawEnable(true);
        this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_ZOOM_DRAG);
      }
    },
    updateMouseToolBehavior() {
      // 2022.04 新增需求：http://tapd.oa.com/smallclassroom_xinlian/prong/stories/view/1020426766873341481?jump_count=1
      // 2024.09 老师默认能翻页，学生有白板权限也不能翻页
      const canTurnPage = !!this.canTurnPage;
      console.log('updateMouseToolBehavior, isTeacher', this.isTeacher, 'isAssistant', this.isAssistant, 'hasPermission', this.hasPermission, 'canTurnPage', canTurnPage);
      this.teduBoard.setMouseToolBehavior({
        turnPage: {
          h5PPT: canTurnPage, // 动态ppt点击是否翻页
          whiteBoard: canTurnPage, // 普通白板点击是否翻页
          imgPPT: canTurnPage, // 图片点击是否翻页
          imgFile: canTurnPage, // 图片文件（addImagesFile接口添加的文件）点击是否翻页
          audiovisual: true,
        },
      });
    },

    // 两点的距离
    getDistance(p1, p2) {
      const x = p2.pageX - p1.pageX;
      const y = p2.pageY - p1.pageY;
      return Math.sqrt((x * x) + (y * y));
    },

    // 两点的夹角
    getAngle(p1, p2) {
      const x = p1.pageX - p2.pageX;
      const y = p1.pageY - p2.pageY;
      return Math.atan2(y, x) * 180 / Math.PI;
    },

    // 获取中点
    getMidpoint(p1, p2) {
      const x = (p1.pageX + p2.pageX) / 2;
      const y = (p1.pageY + p2.pageY) / 2;
      return [x, y];
    },

    renderWaterMark() {
      const $boardWrap = this.$refs.wbp;
      if (this.teduBoard && $boardWrap) {   // 添加保护(避免未加载白板时异常)
        const waterMarkParams = TCIC.SDK.instance.getWaterMarkParam();
        // 如用户设置了三部分水印，则回放视频仅需保留白板区域左上角图片水印即可。文字水印无需显示。
        if (this.isRecordMode
            && WaterMark.checkParams(waterMarkParams, 'video')
            && WaterMark.checkParams(waterMarkParams, 'board')
            && WaterMark.checkParams(waterMarkParams, 'text')) {
          WaterMark.render($boardWrap, waterMarkParams, 'board');
        } else {
          WaterMark.render($boardWrap, waterMarkParams, 'board');
          if (this.isSmallScreen && waterMarkParams.text) {
            Object.assign(waterMarkParams.text, {
              width: 100,
              height: 100,
              fontSize: '12px',
            });
          }
          WaterMark.render($boardWrap, waterMarkParams, 'text');
        }
      }
    },

    async renderMarquee() {
      const $boardWrap = this.$refs.wbp;
      if (this.teduBoard && $boardWrap) {   // 添加保护(避免未加载白板时异常)
        const marqueeParam = TCIC.SDK.instance.getMarqueeParam();
        const userInfo = await TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId());
        Marquee.render($boardWrap, marqueeParam, userInfo);
      }
    },

    onComponentVisibilityChange(visible) {
      if (visible) {
        this.$nextTick(() => {
          this.renderWaterMark();
          this.renderMarquee();
        });
      }
    },
    onResize: Lodash.throttle(() => {
      const parentDiv = document.getElementById('white-board-parent');
      const parentWidth = parentDiv.clientWidth;
      const parentHeight = parentDiv.clientHeight;
      console.log(`===>>> Board : ${parentWidth} : ${parentHeight}`);
      if (parentWidth === 0 || parentHeight === 0) {
        return;
      }

      let boardWidth = 0;
      let boardHeight = 0;
      if (parentWidth * 9 / 16 > parentHeight) {
        boardWidth = parentHeight * 16 / 9;
        boardHeight = parentHeight;
      } else {
        boardWidth = parentWidth;
        boardHeight = boardWidth * 9 / 16;
      }
      const boardWrapDiv = document.getElementById('white-board');
      boardWrapDiv.style.width = `${boardWidth}px`;
      boardWrapDiv.style.height = `${boardHeight}px`;
    }, 300),

    // 获取本节课课件
    loadDefaultDocument() {
      const { schoolId } = TCIC.SDK.instance.getSchoolInfo();
      const { classId, teacherId } = TCIC.SDK.instance.getClassInfo();
      const params = {
        schoolId,
        classId,
        page: 1,
        limit: 20,
        permission: [0],
        owner: teacherId,
        keyword: '',
      };
      TCIC.SDK.instance.getDocumentList(params)
          .then((res) => {
            // @TODO 这里的bindType要改成1 ,测试时使用0
            // item.bindType === 1 &&
            const currentFile = res.documents.find(item => item.bindType === 1 && item.transcodeType === 1);
            if (currentFile) {
              const transcodeResult = currentFile.transcodeResult;
              this.defaultDocumentId = this.teduBoard.addTranscodeFile({
                title: currentFile.docName,
                pages: currentFile.pages || 10,
                resolution: ''.concat(currentFile.width, 'x').concat(currentFile.height),
                url: transcodeResult,
              });

              // 加载完成后, 重置到第一页, 强制触发信令同步
              this.teduBoard.gotoStep(this.teduBoard.getCurrentBoard(), 1);
              // 切换为鼠标工具
              this.setToolType(TEduBoard.TEduBoardToolType.TEDU_BOARD_TOOL_TYPE_MOUSE);

              // [新增] - 未开课时默认有白板权限，服务于课前预览默认课件
              // @see https://tcic-internal-cloud-class.doc.coding.io/#a807e1ebc1d47e688582c8b95dc66270
              if (!this.isClassStarted) {
                this.teduBoard.setDrawEnable(true);
              }
            }
          })
          .catch((e) => {
            console.error(e);
          });
    },
  },
};
</script>

<style lang="less">
.board-parent{
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--primary-color, #14181D);
  &.small-screen {
    @media screen and (orientation: landscape) {
      .video-js .vjs-control-bar{
        /* 420 = （横屏下方的白板工具宽度 + 工具条右侧的边距） * 2 */
        width: calc(100vmax - 420px);
        margin: 0 auto;
        bottom: 10px;
      }
    }
    .white-board-not-start {
      width: 100%;
      height: 100%;
      text-align: center;
      display: flex;
      align-items: center;
      justify-content: center;
      color: white;
      .not-start-icon {
        background-image: url('./assets/not_start.png');
        background-position: 50%;
        background-size: 100%;
        width: 10px;
        height: 14px;
        margin-right: 5px;
        opacity: .4;
      }
    }
  }
  .board-component {
    width: 100%;
    height: 100%;
    position: relative;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    overflow: hidden;
    background: var(--primary-color, #14181D);
    z-index: 0;

    iframe {
      &:not(.tic_board_h5_file) {
        pointer-events: none;
      }
    }
  }
  .white-board-disable-mask {
    position: absolute;
    width: 100%;
    height: 100%;
  }
}
</style>
