<template>
  <div
    ref="component"
    class="screenplayer-component"
  >
    <div
      v-show="!paused"
      id="screenplayer__view"
      ref="video"
      data-dom-name="screenplayer-video-dom"
      class="screenplayer__view"
    />
    <div
      v-if="paused"
      class="screenplayer__tips"
    >
      <i class="screenplayer__icon" />
      <span class="screenplayer__msg">{{ pauseMessage }}</span>
    </div>
    <div
      v-if="autoPlayVideo"
      class="video_auto_play"
      @click="autoPlayVideoHandler"
    >
      <div class="play_icon" />
    </div>
  </div>
</template>

<script>
import i18next from 'i18next';
import Constant from '@/util/Constant';
import BaseComponent from '@core/BaseComponent';
import Marquee from '@/util/Marquee';
import WaterMark from '@/util/WaterMark';

export default {

  components: {},
  extends: BaseComponent,

  data() {
    return {
      userId: '', // 屏幕共享用户id
      userName: '', // 屏幕共享用户的昵称
      selfUserId: '', // 当前登录用户id
      paused: false,
      sequence: 0,
      viewInited: false,    // 组件高宽初始化完成
      needRender: false,    // 需要渲染
      isRecordMode: false,    // 是否录制模式
      isScreenShareOpen: false,
      roleInfo: {},
      classState: TCIC.TClassStatus.Not_Start,
      autoPlayVideo: false,
      eventResume: () => {},
    };
  },
  computed: {
    pauseMessage() {
      if (this.userId === TCIC.SDK.instance.getClassInfo().teacherId) {
        return i18next.t('{{arg_0}}已暂停屏幕共享', { arg_0: this.roleInfo.teacher });
      }
      return i18next.t('{{arg_0}}己暂停屏幕共享', { arg_0: this.userName });
    },
  },

  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;

    // 课堂状态变更
    TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
      this.classState = TCIC.TClassStatus.Already_Start;
      const permissionList = TCIC.SDK.instance.getPermissionList();
      if (!this.isScreenShareOpen) {
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Visible, true).then(() => {
          this.processScreenShare(permissionList);
        });
      }
      console.log('[::this.classState] ', this.classState, permissionList);
    });
    // 课堂状态
    this.makeSureClassJoined(() => {
      this.selfUserId = TCIC.SDK.instance.getUserId();
    });

    // 监听课堂状态变更
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      console.log('[ShareToolbar] mounted] TCIC.TMainState.Class_Status', status);
      this.classStatus = status;
    });
    /** *
     * 权限变更时，需要重新绑定渲染
     * 注意：仅依赖这个事件，会出现BUG，老师端无操作时，没有同步状态事件会导致无法渲染屏幕共享状态
     */
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, (permissionList) => {
      console.log('TCIC.TMainEvent.Permission_Update:', permissionList);
      if (document.visibilityState === 'hidden') {
        TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Visible, true)
          .then(() => {  // 确保课堂可见才处理
            this.processScreenShare(permissionList);
          });
      } else {
        this.processScreenShare(permissionList);
      }
    });
    // 本地流监听屏幕分享状态变化
    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, (status) => {
      console.log('ScreenPlayer of screen share State::', status);
      this.isScreenShareOpen = (status < 2); // 屏幕共享中
      if (this.isScreenShareOpen) {
        this.startRenderScreen();
        this.renderMarquee();
        this.renderWaterMark();
      } else {
        this.stopRenderScreen();
      }
    });
    // 监听辅助流变化
    this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.SubStream_Changed, (params) => {
      if (params?.userId  && params.userId !== this.userId) {
        console.log(`SubStream_Changed, userId: ${params.userId}`);
        // this.userId = params.userId;
        // this.startRenderScreen();
      }
    });
    // 录制模式不显示左下角的QuickIM组件，【如直播课】
    this.addLifecycleTCICStateListener(Constant.TStateRecordMode, (recordMode) => {
      this.isRecordMode = recordMode;
    });
    const resizeObserver = new ResizeObserver((entries) => {
      this.onBoundingClientRectChange(entries[0].contentRect);
    });

    // 监听退出课堂状态,移除渲染
    this.addLifecycleTCICStateListener(TCIC.TMainState.Joined_TRTC, (joined) => {
      console.log('【joined class', joined);
      // 大班课、一对一课程，存在学生上下台，不需要移除屏幕共享渲染
      if (!joined && !TCIC.SDK.instance.isBigRoom() && !TCIC.SDK.instance.isOneOnOneClass()) {
        this.stopRenderScreen();
      }
    });
    resizeObserver.observe(this.$el);

    TCIC.SDK.instance.on(TCIC.TMainEvent.WaterMark_Update, this.renderWaterMark);
    TCIC.SDK.instance.on(TCIC.TMainEvent.Marquee_Update, this.renderMarquee);

    this.renderMarquee();
    this.renderWaterMark();
    TCIC.SDK.instance.on(TCIC.TTrtcEvent.AUTOPLAY_FAILED, this.autoPlayFiledHandler);
    TCIC.SDK.instance.on(TCIC.TTrtcEvent.AUTOPLAY_CONFIRM, this.autoPlayConfirmHandler);
  },

  beforeDestroy() {
    this.destroyMarquee();
    this.destroyWaterMark();
    TCIC.SDK.instance.unbindRemoteVideoDom(this.userId, TCIC.TTrtcVideoStreamType.Sub, this.sequence);
  },

  methods: {
    autoPlayVideoHandler() {
      if (this.autoPlayVideo) {
        this.autoPlayVideo = false;
        this.eventResume(); // 恢复播放
      }
    },
    autoPlayFiledHandler(result) {
      if (this.needRender && result.userId === this.userId) {
        if (TCIC.SDK.instance.isFeatureAvailable('AutoPlayFailed.ShowIcon')) {
          this.autoPlayVideo = true;
        }
        this.eventResume = result.resume;
      }
    },
    autoPlayConfirmHandler(result) {
      if (this.autoPlayVideo) {
        this.autoPlayVideo = false;
        this.eventResume(); // 恢复播放
      }
    },
    onBoundingClientRectChange(rect) {
      console.info(`ScreenPlayer::onBoundingClientRectChange=>${JSON.stringify(rect)}, ${this.needRender}`);
      if (rect && rect.width > 0 && rect.height > 0) {
        if (!this.viewInited && this.needRender) {
          this.$nextTick(() => {
            this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(this.userId, TCIC.TTrtcVideoStreamType.Sub, this.$refs.video, false);
          });
        } else {
          this.onResize();
        }
        this.viewInited = true;
      } else {
        this.viewInited = false;
      }
    },
    onResize() {
      if (this.viewInited && this.needRender) {    // 公开课尺寸变更时需要重新拉流
        TCIC.SDK.instance.unbindRemoteVideoDom(this.userId, TCIC.TTrtcVideoStreamType.Sub, this.sequence);
        this.$nextTick(() => {
          this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(this.userId, TCIC.TTrtcVideoStreamType.Sub, this.$refs.video, false);
        });
      }
    },
    startRenderScreen() {
      this.needRender = true;
      console.info(`ScreenPlayer::startRenderScreen=>${this.viewInited}, userId=>${this.userId}`);
      if (this.viewInited) {
        this.sequence = TCIC.SDK.instance.bindRemoteVideoDom(this.userId, TCIC.TTrtcVideoStreamType.Sub, this.$refs.video, false);
      }
    },
    stopRenderScreen() {
      this.needRender = false;
      TCIC.SDK.instance.unbindRemoteVideoDom(this.userId, TCIC.TTrtcVideoStreamType.Sub, this.sequence);
    },

    destroyWaterMark() {
      WaterMark.destroy(this.$refs.component, 'video');
    },
    destroyMarquee() {
      Marquee.destroy(this.$refs.component);
    },
    renderWaterMark() {
      if (!this.isRecordMode) {
        const waterMarkParams = TCIC.SDK.instance.getWaterMarkParam();
        WaterMark.render(this.$refs.component, waterMarkParams, 'video');
      }
    },
    async renderMarquee() {
      const $boardWrap = this.$refs.component;
      const marqueeParam = TCIC.SDK.instance.getMarqueeParam();
      const userInfo = await TCIC.SDK.instance.getUserInfo(TCIC.SDK.instance.getUserId());
      Marquee.render($boardWrap, marqueeParam, userInfo);
    },
    // 屏幕分享权限变更
    processScreenShare(permissionList) {
      // 遍历权限列表，找到具有屏幕分享权限并且正在屏幕分享的人
      // eslint-disable-next-line max-len
      const sharerPermissionList = permissionList.filter(permission => (permission.screen === 2 || permission.screen === 1)
        && permission.screenState < 2 // 0 分享中， 1 暂停
        && permission.userId !== this.selfUserId);

      // 公开课、互动班课三分屏布局下，web端分享屏幕，不显示screen-player-component组件
      // const threeLayout = TCIC.SDK.instance.isInteractClass() && TCIC.SDK.instance.getState(TCIC.TMainState.Class_Layout, TCIC.TClassLayout.Top) === TCIC.TClassLayout.Three;
      // const hideShare = sharerPermissionList.length > 0
      // && sharerPermissionList[0].platform === TCIC.TPlatform.Web;
      //  && !hideShare
      /**
       * 有人在分享屏幕，并且已经在上课中才开始渲染
       */
      if (sharerPermissionList.length > 0 && this.classState === TCIC.TClassStatus.Already_Start) {  // 有人正在屏幕分享，可能是进行中，也可能是暂停状态
        const permission = sharerPermissionList[0];
        console.log(`===>>> : processScreenShare : ${JSON.stringify(permission)}`);
        if (this.userId) {  // 之前已有人在分享
          if (this.userId !== permission.userId) {  // 分享者发生了变化，重新绑定渲染
            this.stopRenderScreen();
            this.userId = permission.userId;
            this.userName = permission.userName;
            this.paused = permission.screenState === 1;  // 初始化暂停状态
            this.show();
            TCIC.SDK.instance.setState(Constant.TStateScreenPlayerVisible, true);
            if (!this.paused) {   // 未暂停则开始渲染
              this.$nextTick(() => {
                this.startRenderScreen();
                this.renderMarquee();
                this.renderWaterMark();
              });
            }
          } else {  // 分享者未发生变化，检查暂停状态是否有变化
            const paused = permission.screenState === 1;
            if (paused !== this.paused) {
              this.paused = paused;
              // window.showToast(paused ? '屏幕分享已暂停' : '屏幕分享已恢复');
              if (this.paused) {  // 暂停状态变更需更新渲染状态
                this.stopRenderScreen();
              } else {
                this.$nextTick(() => {
                  this.startRenderScreen();
                  this.renderMarquee();
                  this.renderWaterMark();
              });
              }
            }
          }
        } else {  // 之前没有人在分享，绑定渲染
          this.userId = permission.userId;
          this.userName = permission.userName;
          this.paused = permission.screenState === 1;  // 初始化暂停状态
          this.show();
          TCIC.SDK.instance.setState(Constant.TStateScreenPlayerVisible, true);
          if (!this.paused) {   // 未暂停则开始渲染
            this.$nextTick(() => {
              this.startRenderScreen();
              this.renderMarquee();
              this.renderWaterMark();
            });
          }
        }
      } else {  // 已没有人正在屏幕分享
        if (this.userId) {  // 解除之前的绑定渲染
          this.stopRenderScreen();
          this.userId = '';
          this.userName = '';
          this.paused = false;  // 清除暂停状态
          this.hide();
          TCIC.SDK.instance.setState(Constant.TStateScreenPlayerVisible, false);
        }
      }
    },
  },
};
</script>

<style lang="less">

.screenplayer-component {
  position: relative;
  width: 100%;
  height: 100%;

  .video_auto_play {
    top: 0;
    position: absolute;
    width: 100%;
    height: 100%;
    display: flex;
    background-color: #22262E;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 99;
    opacity: 0.8;
    .play_icon {
      width: 50px;
      height: 50px;
      max-width: 70%;
      max-height: 70%;
      background-image: url('../video-component/assets/auto_play.svg');
      background-size: contain;
      background-repeat: no-repeat;
      background-position: 50%;
    }
  }

  .screenplayer__view {
    width: 100%;
    height: 100%;
    background: var(--primary-color, #14181D);

    .vcp-player {
      width: 100% !important;
      height: 100% !important;
      video {
        width: 100% !important;
        height: 100% !important;
      }
    }
    pointer-events: none;
  }

  .screenplayer__tips {
    position: absolute;
    height: 32px;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-50%);
    background: #1C2131;
    box-shadow: 0px 1px 10px 0px rgba(0, 0, 0, 0.3);
    border-radius: 4px;

    i.screenplayer__icon {
      margin: 8px;
      width: 16px;
      height: 16px;
      vertical-align: middle;
      content: url('./assets/screen_tips.png');
    }

    .screenplayer__msg {
      display: inline-block;
      vertical-align: middle;
      margin-right: 16px;
      font-size: 12px;
      color: #FFFFFF;
      line-height: 17px;
    }
  }
}


</style>
