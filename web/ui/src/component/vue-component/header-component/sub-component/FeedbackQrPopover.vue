<template>
  <el-popover
    id="wechat-popover"
    placement="bottom"
    width="168"
    trigger="hover"
    popper-class="wechat-feedback-popover"
  >
    <div class="wechat-qr-container">
      <div class="wechat-qr" />
      <span class="wechat-qr-text">{{ $t('欢迎扫码加入交流群，获取专属客户经理在线解答') }}</span>
    </div>
    <div
      slot="reference"
      class="icon-wechat-wrapper"
    >
      <div
        class="icon-wechat"
      />
      {{ $t('微信咨询') }}
    </div>
  </el-popover>
</template>

<script>
export default {
  data() {
    return {};
  },
  methods: {
  },
};
</script>

<style lang="less">
  .wechat-feedback-popover {
    background: #fff!important;
    border: transparent!important;
  }

  .wechat-qr-text {
    font-size: 12px;
    font-weight: 400;
    color: #4F586B;
    line-height: 18px;
  }

  .icon-wechat-wrapper {
    display: flex;
    align-items: center;
    background: rgb(48, 51, 59);
    color: #CFD4E5;
    height: 26px;
    border-radius: 12px;
    padding: 2px 10px;
    margin-left: 10px;
    white-space: nowrap;
    -webkit-app-region: no-drag;
    font-size: 12px;
  }

  .icon-wechat {
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-right: 8px;
    background-image: url('../assets/icon-wechat.svg');
    background-size: contain;
  }

  .wechat-qr {
    width: 144px;
    height: 144px;
    background-image: url('../assets/wechat-qr.png');
    background-size: cover;
    margin-bottom: 6px;
  }

</style>
