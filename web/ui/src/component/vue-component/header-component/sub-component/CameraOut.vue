<template>
  <div class="camera-out">
    <div class="camera-sub-component">
      <el-tooltip
        class="item"
        :disabled="(isMobile && !btnDisabled) || localComponent.active"
        :content="!isClassStarted ? translateTip.roomStartContent
          : (!isOnStage ? $t('请先上台再切换摄像头') : $t('摄像头'))"
      >
        <el-popover
          ref="camera-setting-popover"
          placement="bottom"
          width="300"
          trigger="manual"
          :visible-arrow="true"
          popper-class="header-camera-popover"
          @show="onShow"
        >
          <div>
            <div class="text-lable">
              {{ $t('摄像头') }}
            </div>
            <el-select
              v-model="selectCameraId"
              :placeholder="$t('请选择')"
              :no-data-text="$t('暂无相关设备')"
              :popper-append-to-body="false"
              @change="onCameraChange"
            >
              <el-option
                v-for="item in cameras"
                :key="item.deviceId"
                :label="item.label"
                :value="item.deviceId"
              />
            </el-select>
          </div>
          <video
            id="preview-video"
            autoplay
          />

          <button
            slot="reference"
            :ref="localComponent.name"
            :disabled="btnDisabled"
            class="header__button button--secondary"
            :class="{ active: localComponent.active }"
            @click="onButtonClick"
          >
            <div class="tool-item-wrap">
              <el-badge
                :value="localComponent.badge"
                :max="99"
                class="badge"
                :hidden="localComponent.badge === 0"
              >
                <i :class="['header__i', 'i--menu', cameraOn ? 'icon-camera-setiing' : 'icon-camera-off']" />
              </el-badge>
              <span class="header__btn-text">{{ $t('摄像头') }}</span>
              <i class="el-icon-caret-bottom downicon" />
            </div>
          </button>
        </el-popover>
      </el-tooltip>
    </div>
  </div>
</template>


<script>
import Camera from '@/component/vue-component/header-component/sub-component/Camera.vue';
export default {
    name: 'CameraOut',
    extends: Camera,
};
</script>
<style lang="less">
.camera-out{

  padding: 0 6px;
  position: relative;
  width: 60px;
  text-align: center;
  color: #fff;
  display: flex;
  align-content: center;
  align-items: center;

  button.header__button--start {
    background: #666 !important;
    color: #999;
    border-color: #666 !important;
  }

  .button--secondary{
    border-radius: 2px;
    padding: 4px 0;
    -webkit-app-region: no-drag;
    border: none;
    color: var(--btn--secondary--text-color, #fff);
    font-size: 11px;
    outline: none;
    cursor: pointer;
    display: flex;
    justify-content: space-around;
    min-width: 52px;
    width: 100%;

    .tool-item-wrap{
      height: 44px;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;

      .header__i.i--menu{
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        vertical-align: middle;
      }

      span{
        white-space: nowrap;
        font-size: 12px;
        line-height: 17px;
        text-align: center;
        color: #A3AEC7;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }

  .camera-sub-component {
    .icon-camera-setiing {
      background-image: url('../assets/icon-camera.svg');
    }
    .icon-camera-off {
      background-image: url('../assets/icon-camera-off.svg');
    }
    .tool-item-wrap {
      position: relative;
      .downicon {
        position: absolute;
        right: 2px;
        top: 50%;
        margin-top: -10px;
      }
    }
  }

  .header-camera-popover {
    .text-lable {
      margin-bottom: 10px;
      font-size: 12px;
    }

    .el-select {
      flex: 1;
      border: none;
      width: 100%;
      background: rgba(255, 255, 255, 0.08);
      .el-input__inner {
        background: transparent;
        border-radius: 0;
        border: 0;
        color: #fff;
      }

      &>.el-input {
        border: none;
      }

    }
    #preview-video {
      width: 100%;
      margin-top: 10px;
    }
  }
}

</style>
