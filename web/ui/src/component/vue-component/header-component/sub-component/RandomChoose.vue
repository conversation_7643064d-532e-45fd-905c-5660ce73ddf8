<template>
  <div class="random-choose-tool-sub-component">
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      class="item"
      placement="bottom"
      popper-class="tooltip-no-content"
    >
      <button
        class="header__button button--secondary"
        :class="{active: component.active}"
        @click="open"
      >
        <i class="header__i i--menu icon-tool" />{{ $t('随机选人') }}
      </button>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'RandomChooseToolSubComponent',
  components: {},
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      componentInstance: null,
    };
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    async open() {
      this.componentInstance = this.componentInstance
      || TCIC.SDK.instance.getComponent('random-choose-tool-component')
      || await TCIC.SDK.instance.loadComponent('random-choose-tool-component');
      TCIC.SDK.instance.updateComponent('random-choose-tool-component', {
        display: 'block',
        width: '438px',
        left: 'calc(50vw - 219px)',
        top: '20vh',
      });
      this.componentInstance.getVueInstance().onStart();
    },
  },
};
</script>
<style lang="less">
.random-choose-tool-sub-component {
	.icon-tool {
		background-image: url('../assets/ic_tool_random_choose.svg');
		background-size: 90% !important;
	}
}
</style>
