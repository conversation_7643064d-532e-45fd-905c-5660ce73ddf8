<template>
  <div class="speaker-out">
    <div class="speaker-sub-component">
      <el-tooltip
        class="item"
        :disabled="(isMobile && !btnDisabled) || localComponent.active"
        :content="!isClassStarted ? translateTip.roomStartContent
          : (!isOnStage ? $t('请先上台再切换麦克风') : $t('麦克风'))"
      >
        <el-popover
          ref="camera-setting-popover"
          placement="bottom"
          width="300"
          trigger="manual"
          :visible-arrow="true"
          popper-class="header-speaker-popover"
          @show="onShow"
        >
          <div style="margin-bottom: 10px;">
            <div class="text-lable">
              {{ $t('麦克风') }}
            </div>
            <el-select
              v-model="selectMicId"
              :placeholder="$t('请选择')"
              :no-data-text="$t('暂无相关设备')"
              :popper-append-to-body="false"
              @change="onMicsChange"
            >
              <el-option
                v-for="item in mics"
                :key="item.deviceId"
                :label="item.label"
                :value="item.deviceId"
              />
            </el-select>
          </div>
          <div>
            <div class="text-lable">
              {{ $t('输出') }}
            </div>
            <div class="volume">
              <div class="mic-icon" />
              <ul class="capacity">
                <li
                  v-for="item in 21"
                  :key="item"
                  class="item"
                  :class="{active: item < volumeLevel }"
                />
              </ul>
            </div>
          </div>
          <div>
            <div class="text-lable">
              {{ $t('扬声器') }}
            </div>
            <el-select
              v-model="selectSpeakerId"
              :placeholder="$t('请选择')"
              :no-data-text="$t('暂无相关设备')"
              :popper-append-to-body="false"
              @change="onSpeakerChange"
            >
              <el-option
                v-for="item in speakers"
                :key="item.deviceId"
                :label="item.label"
                :value="item.deviceId"
              />
            </el-select>
          </div>
          <button
            slot="reference"
            :ref="localComponent.name"
            :disabled="btnDisabled"
            class="header__button button--secondary"
            :class="{ active: localComponent.active }"
            @click="showSpeakerSetting"
          >
            <div class="tool-item-wrap">
              <i :class="['header__i', 'i--menu', micOn ? 'icon-speaker-setiing' : 'icon-speaker-setiing-off']" />
              <span class="header__btn-text">{{ $t('麦克风') }}</span>
              <i class="el-icon-caret-bottom downicon" />
            </div>
          </button>
        </el-popover>
      </el-tooltip>
    </div>
  </div>
</template>
<script>
import Speaker from '@/component/vue-component/header-component/sub-component/Speaker.vue';
export default {
    name: 'SpeakerOut',
    extends: Speaker,
};
</script>
<style lang="less">
@import "../../device-detect-component/theme/college-web.less";
@import "../../device-detect-component/theme/college-electron.less";

.speaker-out{
  padding: 0 6px;
  position: relative;
  width: 60px;
  text-align: center;
  color: #fff;
  display: flex;
  align-content: center;
  align-items: center;

  button.header__button--start {
    background: #666 !important;
    color: #999;
    border-color: #666 !important;
  }

  .button--secondary{
    border-radius: 2px;
    padding: 4px 0;
    -webkit-app-region: no-drag;
    border: none;
    color: var(--btn--secondary--text-color, #fff);
    font-size: 11px;
    outline: none;
    cursor: pointer;
    display: flex;
    justify-content: space-around;
    min-width: 52px;
    width: 100%;

    .tool-item-wrap{
      height: 44px;
      width: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 2px;

      .header__i.i--menu{
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        background-position: center;
        background-repeat: no-repeat;
        vertical-align: middle;
      }

      span{
        white-space: nowrap;
        font-size: 12px;
        line-height: 17px;
        text-align: center;
        color: #A3AEC7;
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }


  // Original
  .speaker-sub-component {
    .icon-speaker-setiing {
      background-image: url('../assets/icon-speaker.svg');
    }
    .icon-speaker-setiing-off {
      background-image: url('../assets/icon-speaker-off.svg');
    }
    .tool-item-wrap {
      position: relative;
      .downicon {
        position: absolute;
        right: 2px;
        top: 50%;
        margin-top: -10px;
      }
    }

  }
  .header-speaker-popover {
    .text-lable {
      margin-bottom: 10px;
      font-size: 12px;
    }

    .el-select {
      flex: 1;
      border: none;
      width: 100%;
      background: rgba(255, 255, 255, 0.08);
      .el-input__inner {
        background: transparent;
        border-radius: 0;
        border: 0;
        color: #fff;
      }

      &>.el-input {
        border: none;
      }

    }
    #preview-video {
      width: 100%;
      margin-top: 10px;
    }
    .volume {
      display: flex;
      flex-wrap: nowrap;
      padding-top: 4px;
      height: 29px;
      margin-bottom: 10px;
      &.disabled {
        opacity: .4;
      }

      .mic-icon {
        width: 16px;
        background: no-repeat center center / 100% auto url('../../device-detect-component/assets/microphone-outline.png');
      }

      .mic-volume-value {
        width: 35px;
        line-height: 16px;
        font-weight: bolder;
        color: var(--main-text-color, #fff);
        text-align: center;
      }

      .speaker-icon {
        cursor: pointer;
        width: 16px;
        background: no-repeat center center / 100% auto url('../../device-detect-component/assets/speaker-outline.png');
        &.disable {
          background: no-repeat center center / 100% auto url('../../device-detect-component/assets/speaker-off.png');
        }
      }

      .capacity {
        position: relative;
        flex-grow: 1;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 0 4px 0 13px;
        margin-right: 37px;

        .slider-wrapper {
          display: block;
          width: 100%;
          height: 100%;
        }

        .item {
          width: 4px;
          height: 100%;
          border-radius: 2px;
          background: #D8D8D8;
          justify-content: space-between;

          &.active {
            background: #4880FF;
          }
        }
      }
    }
  }
}


</style>
