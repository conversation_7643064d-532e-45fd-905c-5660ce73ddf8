<template>
  <div class="video-fullscreen-sub-component">
    <button
      class="header__button button--secondary header__right-button"
      @click="onClick"
    >
      <div class="tool-item-wrap">
        <i :class="['header__i i--menu icon-fullscreen', {'mobile': isMobile }]" />
        <span class="header__btn-text">{{ $t('全屏') }}</span>
      </div>
    </button>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'VideoFullscreenSubComponent',
  extends: BaseComponent,
  data() {
    return {
      isFullscreen: false,
      isHeaderHidden: false,
      isMobile: TCIC.SDK.instance.isMobile(),
    };
  },
  watch: {
    isHeaderHidden(val) {
      if (val) {
        document.body.classList.add('is-header-hidden');
      } else {
        document.body.classList.remove('is-header-hidden');
      }
    },
    isFullscreen(val) {
      if (val) {
        document.body.classList.add('video-fullscreen');
      } else {
        document.body.classList.remove('video-fullscreen');
      }
    },
  },
  mounted() {
    this.pointerEventListener = () => {
      if (this.isFullscreen) {
        this.isHeaderHidden = !this.isHeaderHidden;
      }
    };

    document.body.addEventListener('click', this.pointerEventListener);
  },
  beforeDestroy() {
    document.body.removeEventListener('click', this.pointerEventListener);
    document.body.classList.remove('is-header-hidden');
    document.body.classList.remove('video-fullscreen');
  },
  methods: {
    onClick(e) {
      e.stopPropagation();
      if (this.isFullscreen) {
        this.exitFullscreen();
      } else {
        this.enterFullscreen();
      }
    },
    enterFullscreen() {
      this.isFullscreen = true;
      this.isHeaderHidden = true;
    },
    exitFullscreen() {
      this.isFullscreen = false;
      this.isHeaderHidden = false;
    },
  },
};
</script>
<style lang="less">
.video-fullscreen-sub-component {
  .header__right-button .icon-fullscreen {
    width: 40px;
    height: 40px;
    background-image: url('../assets/new-icons/ic_fullscreen.svg');
  }
}

.video-fullscreen {
  screen-player-component {
    height: 100% !important;
    top: 0 !important;
  }

  vod-player-component {
    height: 100% !important;
    top: 0 !important;
  }

  board-component {
    height: 100% !important;
    top: 0 !important;
  }

  .exit-fullscreen-btn {
    display: block;
  }
}

.video-fullscreen.is-header-hidden {
  header-component {
    display: none !important;
  }
  footer-component {
    display: none !important;
  }
}
</style>
