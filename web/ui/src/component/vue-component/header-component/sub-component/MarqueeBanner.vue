<template>
  <div :class="['marquee-banner', { 'small-screen': isSmallScreen }]">
    <div class="marquee-wrapper">
      <div
        class="marquee-text"
          :style="animationStyle"
      >
        <div class="icon-placeholder">
          <i />
        </div>
        {{ text }}
        <span class="spacer">&nbsp;&nbsp;&nbsp;</span>
        <div class="icon-placeholder">
          <i />
        </div>
        {{ text }}
      </div>
      <div class="fade-mask-right" />
    </div>
  </div>
</template>


<script>
import BaseComponent from '@/component/core/BaseComponent';
export default {
  name: 'MarqueeBanner',
  extends: BaseComponent,
  props: {
    text: {
      type: String,
      default:
          '该平台专注提供线上音视频互动服务。为保护你的权益与安全，请警惕网络诈骗。',
    },
    speed: {
      type: Number,
      default: 60,
    },
  },
  computed: {
    animationStyle() {
      const len = this.text.length * 12 + 100;
      const duration = len / this.speed;
      return {
        animationDuration: `${duration}s`,
      };
    },
  },
};
</script>

<style scoped>.marquee-banner {
  width: 172px;
  height: 24px;
  background-color: #2b2e33;
  color: #cfd3dc;
  display: flex;
  align-items: center;
  overflow: hidden;
  border-radius: 12px;
  font-size: 12px;
  padding: 0 8px;
  margin-left: 10px;
  &.small-screen {
    width: 164px;
  }
}

.marquee-wrapper {
  flex: 1;
  overflow: hidden;
  white-space: nowrap;
  position: relative;
  display: flex;
  align-items: center;
}

.marquee-text {
  display: inline-flex;
  align-items: center;
  white-space: nowrap;
  animation: scroll-left linear infinite;
}

@keyframes scroll-left {
  0% {
    transform: translateX(0%);
  }
  100% {
    transform: translateX(-51%);
  }
}

.icon-placeholder {
  flex: 0 0 auto;
  width: 12px;
  height: 12px;
  margin-right: 6px;
  background-image: url('../assets/warning.svg');
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

.spacer {
  display: inline-block;
  width: 24px;
}

.fade-mask-right {
  position: absolute;
  top: 0;
  right: 0;
  width: 24px;
  height: 100%;
  pointer-events: none;
  background: linear-gradient(to right, transparent, #2b2e33);
}
</style>
