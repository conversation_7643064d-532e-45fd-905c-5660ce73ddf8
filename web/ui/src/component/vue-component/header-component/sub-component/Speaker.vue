<template>
  <div class="speaker-sub-component">
    <el-tooltip
      class="item"
      :disabled="(isMobile && !btnDisabled) || localComponent.active"
      :content="!isClassStarted ? translateTip.roomStartContent
        : (!isOnStage ? $t('请先上台再切换麦克风') : $t('麦克风'))"
    >
      <el-popover
        ref="speaker-setting-popover"
        placement="bottom"
        width="300"
        trigger="manual"
        :visible-arrow="true"
        popper-class="header-speaker-popover"
        @show="onShow"
      >
        <div style="margin-bottom: 10px;">
          <div class="text-lable">
            {{ $t('麦克风') }}
          </div>
          <el-select
            v-model="selectMicId"
            :placeholder="$t('请选择')"
            :no-data-text="$t('暂无相关设备')"
            :popper-append-to-body="false"
            @change="onMicsChange"
          >
            <el-option
              v-for="item in mics"
              :key="item.deviceId"
              :label="item.label"
              :value="item.deviceId"
            />
          </el-select>
        </div>
        <div>
          <div class="text-lable">
            {{ $t('输出') }}
          </div>
          <div class="volume">
            <div class="mic-icon">
              <IconMic />
            </div>
            <ul class="capacity">
              <li
                v-for="item in 21"
                :key="item"
                class="item"
                :class="{active: item < volumeLevel }"
              />
            </ul>
          </div>
        </div>
        <div>
          <div class="text-lable">
            {{ $t('扬声器') }}
          </div>
          <el-select
            v-model="selectSpeakerId"
            :placeholder="$t('请选择')"
            :no-data-text="$t('暂无相关设备')"
            :popper-append-to-body="false"
            @change="onSpeakerChange"
          >
            <el-option
              v-for="item in speakers"
              :key="item.deviceId"
              :label="item.label"
              :value="item.deviceId"
            />
          </el-select>
        </div>
        <button
          slot="reference"
          :ref="localComponent.name"
          :disabled="btnDisabled"
          class="header__button button--secondary"
          :class="{ active: localComponent.active }"
          @click="showSpeakerSetting"
        >
          <div class="tool-item-wrap">
            <i :class="['header__i', 'i--menu']" style="padding: 0!important">
              <IconSpeaker :off="!micOn" />
            </i>
            <span class="header__btn-text">{{ $t('麦克风') }}</span>
            <i class="el-icon-caret-bottom downicon" />
          </div>
        </button>
      </el-popover>
    </el-tooltip>
  </div>
</template>
<script>
import BaseComponent from '@core/BaseComponent';
import Constant from '@/util/Constant';
import Lodash from 'lodash';
import Media from '@/util/Media';
import i18next from 'i18next';
import DeviceUtil from '@/util/DeviceUtil';
import { IconMic } from '../../device-detect-component/assets/svg-component';
import IconSpeaker  from '../assets/svg-component/IconSpeaker.vue';
export default {
    name: 'SpeakerSubComponent',
    components: {
        IconMic,
        IconSpeaker,
    },
    extends: BaseComponent,
    props: {
        component: {
            type: Object,
            default: () => ({}),
        },
    },
    data() {
        return {
            localComponent: this.component,
            isMobile: TCIC.SDK.instance.isMobile(),
            isClassStarted: false,
            speakers: [],
            mics: [],
            selectSpeakerId: '',
            selectMicId: '',
            volumeLevel: 0,
            micOn: false,
            isOnStage: false,
            roomInfo: {},
            roleInfo: {},
          documentClickHandler: null,
        };
    },
    computed: {
        btnDisabled() {
            return !this.isClassStarted || !this.isOnStage;
        },
      translateTip() {
        return {
          roomStartContent: i18next.t('开始{{arg_0}}后才可切换麦克风', { arg_0: this.roomInfo.startRoom }),
        };
      },
    },
    mounted() {
        const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
        this.roleInfo = roleInfo;
        this.roomInfo = roomInfo;
        this.updateStatus();
        this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
            this.updateStatus();
        });
        this.addLifecycleTCICStateListener(Constant.TStateImmerseMode, this.onImmerseModeChange);
        this.addLifecycleTCICEventListener(TCIC.TTrtcEvent.Volume_Update, (result) => {
        // 先过滤id，避免因防抖导致音量不展示
        if (result && result.userId === TCIC.SDK.instance.getUserId()) {
          this.volumeUpdateListener(result);
        }
      });
        this.getDevice();

      this.addLifecycleTCICEventListener(Constant.TStateLoadingMicState,  ({ userId, type, value }) => {
        if (userId !== TCIC.SDK.instance.getUserId()) {
          return;
        }
        if (type === 'loadingOwnMic') {
          if (typeof value.open === 'boolean') {
            const deviceStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
            // eslint-disable-next-line vue/no-mutating-props, no-nested-ternary
            this.micOn = !!value.open;
          }
        }
      });

      this.isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
      this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
        this.isOnStage = status;
      });

      const currentMicState = DeviceUtil.selfDeviceState.mic;
      if (typeof currentMicState === 'boolean') {
        this.micOn = currentMicState;
      }
    },
    methods: {
        volumeUpdateListener: Lodash.debounce(function (result) {
            this.volumeLevel = Media.amplifier(result.volume, 22);
        }, 50),
        async onMicsChange(id) {
            this.selectMicId = id;
            TCIC.SDK.instance.reportLog('onMicsChange', `${id}`);
            await TCIC.SDK.instance.switchMic(this.selectMicId);
        },
        async onSpeakerChange(id) {
            this.selectSpeakerId = id;
            TCIC.SDK.instance.reportLog('onMicsChange', `${id}`);
            await TCIC.SDK.instance.switchSpeaker(this.selectSpeakerId);
        },
        getDevice() {
            TCIC.SDK.instance.getInstances().trtc._getDevices().then(async (devices) => {
                const tempSpeakers = [];
                const tempMics = [];
                let usedMicId = '';
                let usedSpeakerId = '';
                try {
                  usedMicId = await TCIC.SDK.instance.getMicDeviceId();
                } catch (e) {}
                try {
                  usedSpeakerId = await TCIC.SDK.instance.getSpeakerDeviceId();
                } catch (e) {}
                TCIC.SDK.instance.reportLog('getDevicesInHeader', `${JSON.stringify(devices)} usedMicId ${usedMicId} $usedSpeakerId ${usedSpeakerId}`);
                for (let index = 0; index < devices.length; index++) {
                    const device = devices[index];
                    // electron端 返回的数据格式和其他端不一样
                    if (TCIC.SDK.instance.isElectron()) {
                      if (device.type == 1) {
                        tempSpeakers.push({
                          deviceId: device.deviceId,
                          label: device.deviceName,
                        });
                      }
                      if (device.type == 0) {
                        tempMics.push({
                          deviceId: device.deviceId,
                          label: device.deviceName,
                        });
                      }
                    } else {
                      if (device.kind == 'audiooutput') {
                          tempSpeakers.push(device);
                      }
                      if (device.kind == 'audioinput') {
                          tempMics.push(device);
                      }
                    }
                }
                this.speakers = tempSpeakers;
                this.mics = tempMics;
                this.selectSpeakerId = usedSpeakerId;
                this.selectMicId = usedMicId;
            });
        },
        updateStatus() {
            const classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Not_Start);
            this.isClassStarted = (classStatus === TCIC.TClassStatus.Already_Start);
        },
        showSpeakerSetting(e) {
          TCIC.SDK.instance.reportLog('showSpeakerSetting', 'start');
          const button = e.currentTarget;
          const rect = button.getBoundingClientRect();
          const clickX = e.clientX - rect.left;

          const arrowWidth = 16;
          if (clickX > rect.width - arrowWidth) {
            TCIC.SDK.instance.reportLog('showSpeakerSetting', 'menu');
            this.$refs['speaker-setting-popover'].doToggle();
          } else {
            TCIC.SDK.instance.reportLog('showSpeakerSetting', 'toggle');
            this.toggleMic();
          }
        },
      async toggleMic() {
        console.log('HeaderComponent::enableMic=>', TCIC.SDK.instance.getUserId(), !this.micOn);
        TCIC.SDK.instance.reportLog('toggle_mic_from_speaker', `flag: ${!this.micOn}`);
        if (!TCIC.SDK.instance.isTeacherOrAssistant() && TCIC.SDK.instance.isCoTeachingClass()) {
          if (!this.micOn) {
            try {
              const userId = TCIC.SDK.instance.getUserId();
              const myPermission = TCIC.SDK.instance.getPermission(userId);
              // 学生如果没有权限，则直接返回
              if (!myPermission.mic) {
                window.showToast(i18next.t('{{arg_0}}关闭了你的麦克风权限，你可以举手申请打开', { arg_0: this.roleInfo.teacher }), 'error');
                return;
              }
              await TCIC.SDK.instance.requireResource('mic');
            } catch (err) {
              window.showToast(i18next.t(
                  '{{arg_0}}内打开麦克风数量超过限制，出于性能考虑，请您先联系{{arg_1}}，让其关闭部分{{arg_2}}的麦克风',
                  { arg_0: this.roomInfo.name, arg_1: this.roleInfo.teacher, arg_2: this.roleInfo.student },
              ), 'error');
              return;
            }
          } else {
            await TCIC.SDK.instance.releaseResource('mic');
          }
        }

        try {
          if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
            const userId = TCIC.SDK.instance.getUserId();
            const myPermission = TCIC.SDK.instance.getPermission(userId);
            if (!myPermission.mic) {
              await TCIC.SDK.instance.memberAction({
                userId,
                actionType: TCIC.TMemberActionType.Mic_Open,
              });
            }
          }
          TCIC.SDK.instance.muteLocalAudio(this.micOn);
        } catch (e) {
          console.error('startLocalAudio error => ', e);
          if (e && e.errorMsg) {
            window.showToast(`${e.errorMsg || e.errorDetail}`, 'error');
          } else {
            window.showToast(i18next.t('打开本地音频采集遇到一些问题'), 'error');
          }
          if (!TCIC.SDK.instance.isTeacher() && TCIC.SDK.instance.isCoTeachingClass()) {
            TCIC.SDK.instance.releaseResource('mic');
          }
        }

        const actualMicState = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start
            ? TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture) && TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Publish)
            : TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
        TCIC.SDK.instance.reportLog('enableMic-actualMicState', `${actualMicState}`);
        if (!this.micOn && !actualMicState) {
          try {
            const currentMicComponent = document.getElementById(`audio-${TCIC.SDK.instance.getUserId()}`);
            console.log(currentMicComponent.toString());
            console.log(currentMicComponent.toString());
            if (currentMicComponent) {
              await DeviceUtil.toggleLocalDeviceWithLoadingEvent(
                  'mic',
                  !this.micOn,
                  () => TCIC.SDK.instance.startLocalAudio(currentMicComponent),
                  {
                    caller: 'HeaderSpeakerComponent',
                    reason: `enableMic-${!this.micOn}`,
                    reportAction: 'startLocalAudio',
                  },
              );
              TCIC.SDK.instance.reportLog('enableMic-startLocalAudio', `${!this.micOn}`);
            }
          } catch (err) {
            TCIC.SDK.instance.reportLog('enableMic-startLocalAudio-error', `${!this.micOn}, ${err?.toString()}`);
            window.showToast(i18next.t('麦克风打开失败'));
          }
        }
      },
        onShow() {
            this.getDevice();

          this.documentClickHandler = (e) => {
            const popover = this.$refs['speaker-setting-popover']?.$el;
            const button = this.$refs[this.localComponent.name];
            if (popover && !popover.contains(e.target) && button && !button.contains(e.target)) {
              this.$refs['speaker-setting-popover'].doClose();
              document.removeEventListener('click', this.documentClickHandler);
              this.documentClickHandler = null;
            }
          };
          document.addEventListener('click', this.documentClickHandler);
        },
      onImmerseModeChange(hide) {
        if (hide) {
          this.$refs['speaker-setting-popover'].doClose();
          if (this.documentClickHandler) {
            document.removeEventListener('click', this.documentClickHandler);
            this.documentClickHandler = null;
          }
        }
      },
    },
};
</script>
<style lang="less">
@import "../../device-detect-component/theme/college-web.less";
@import "../../device-detect-component/theme/college-electron.less";
.light {
  .header-speaker-popover {
    .el-select {
      background: #f5f7fa;
    }
  }
}
.speaker-sub-component {
    .tool-item-wrap {
        position: relative;
        .downicon {
            position: absolute;
            right: 2px;
            top: 50%;
            margin-top: -10px;
        }
    }

}
.header-speaker-popover {
    .text-lable {
        margin-bottom: 10px;
        font-size: 12px;
    }

    .el-select {
        flex: 1;
        border: none;
        width: 100%;
        background: rgba(255, 255, 255, 0.08);
        .el-input__inner {
            background: transparent;
            border-radius: 0;
            border: 0;
            color: var(--text-color, #fff);
        }

        &>.el-input {
            border: none;
        }

    }
    #preview-video {
        width: 100%;
        margin-top: 10px;
    }
    .volume {
      display: flex;
      flex-wrap: nowrap;
      padding-top: 4px;
      height: 29px;
      margin-bottom: 10px;
      &.disabled {
        opacity: .4;
      }

      .mic-icon, .speaker-icon {
        width: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      .mic-volume-value {
        width: 35px;
        line-height: 16px;
        font-weight: bolder;
        color: var(--main-text-color, #fff);
        text-align: center;
      }

      .capacity {
        position: relative;
        flex-grow: 1;
        display: flex;
        flex-wrap: nowrap;
        justify-content: space-between;
        padding: 0 4px 0 13px;
        margin-right: 37px;

        .slider-wrapper {
          display: block;
          width: 100%;
          height: 100%;
        }

        .item {
          width: 4px;
          height: 100%;
          border-radius: 2px;
          background: #D8D8D8;
          justify-content: space-between;

          &.active {
            background: #4880FF;
          }
        }
      }
    }
}

</style>
