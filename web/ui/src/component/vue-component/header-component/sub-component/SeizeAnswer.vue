<template>
  <div class="seize-answer-sub-component">
    <!--V1.3.4暂时去掉提示，有文字:content="component.label"-->
    <el-tooltip
      :manual="interactiveIsTouchEvent()"
      class="item"
      placement="bottom"
      popper-class="tooltip-no-content"
    >
      <button
        class="header__button button--secondary"
        :class="{active: component.active}"
        @click="open"
      >
        <i class="header__i i--menu icon-timer-tool" />{{ $t('抢答器') }}
      </button>
    </el-tooltip>
  </div>
</template>

<script>
import BaseComponent from '@/component/core/BaseComponent';

export default {
  name: 'SizeAnswerToolSubComponent',
  components: {},
  extends: BaseComponent,
  props: {
    component: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      isMobile: TCIC.SDK.instance.isMobile(),
      componentInstance: null,
    };
  },
  mounted() {
  },
  beforeDestroy() {
  },
  methods: {
    async open() {
      this.componentInstance = this.componentInstance
          || TCIC.SDK.instance.getComponent('seize-answer-component')
          || await TCIC.SDK.instance.loadComponent('seize-answer-component');
      TCIC.SDK.instance.updateComponent('seize-answer-component', {
        display: 'block',
        width: '438px',
        height: '300px',
        left: 'calc(50vw - 219px)',
        top: '20vh',
        transform: 'none',
      });
      this.componentInstance.getVueInstance().onStart();
    },
  },
};
</script>
<style lang="less">
.seize-answer-sub-component {
  .icon-timer-tool {
    background-image: url('../assets/ic_tool_seize.svg');
    background-size: 90% !important;
  }
}
</style>
