<template>
  <el-dialog
    :title="$t('欢迎加入交流群')"
    :visible.sync="dialogVisible"
    width="300px"
    append-to-body
    center
    :show-close="false"
    custom-class="feedback-qr-dialog"
    :before-close="handleClose"
  >
    <div class="feedback-qr-text">{{ $t('专属客户经理在线解答') }}</div>
    <div class="feedback-qr-text">{{ $t('3分钟快速解决产品问题') }}</div>
    <div class="feedback-wechat" />
    <span
      slot="footer"
      class="dialog-footer"
    >
      <el-button
        class="close-btn"
        type="primary"
        @click="dialogVisible = false"
      >
        {{ $t('关闭') }}
      </el-button>
    </span>
  </el-dialog>
</template>

<script>
export default {
  data() {
    return {
      dialogVisible: true,
    };
  },
};
</script>

<style lang="less">
  .feedback-qr-dialog {
    margin-top: 30vh!important;
    border-radius: 8px!important;
    .dialog-footer{
      margin-top: 0;
    }
    .el-dialog__title {
      font-size: 16px;
      font-weight: 500;
      line-height: 20px;
      color: #1D2029;
    }
    .el-dialog__body {
      padding-top: 0!important;
    }
    .feedback-wechat {
      width: 185px;
      height: 185px;
      background-image: url('../assets/wechat-qr.png');
      background-size: cover;
      margin: 24px auto 0;
    }
    .close-btn {
      width: 88px;
      background: #FFF;
      border: 1px solid #3D7EFD;
      span {
        font-size: 14px;
        color: #3D7EFD;
      }
    }
    .close-btn:hover {
      background: #FFF;
    }
  }

  .feedback-qr-text {
    font-size: 14px;
    font-weight: 400;
    text-align: center;
    line-height: 19.6px;
    color: #8F93A1;
  }

</style>
