<template>
  <Box
    v-if="hasShown"
    :class="[
      'external-courseware-component',
      isResizing ? 'is-resizing' : '',
      isDragging ? 'is-dragging' : '',
      isExpanded ? 'is-expanded' : '',
    ]"
    :style="{
      width: `${width}px`,
      height: `${height}px`,
      transform: `translate(${resizingOffsetX}px, ${resizingOffsetY}px)`,
    }"
    expandable
    :is-expanded="isExpanded"
    @expand="expand"
    @collapse="collapse"
    @hide="hide"
  >
    <template #title>
      {{ title }}
    </template>
    <template #content>
      <div class="external-courseware-content">
        <iframe
          ref="iframe"
          :src="iframeUrl"
          class="external-courseware-frame"
          allowfullscreen="true"
        />
      </div>
      <div
        class="external-courseware-resize-handle bottom"
        :data-active="resizeParams.x === 0 && resizeParams.y === 1"
        @mousedown="(event) => startResize(event, { x: 0, y: 1 })"
      />
      <div
        class="external-courseware-resize-handle bottom-left"
        :data-active="resizeParams.x === -1 && resizeParams.y === 1"
        @mousedown="(event) => startResize(event, { x: -1, y: 1 })"
      />
      <div
        class="external-courseware-resize-handle bottom-right"
        :data-active="resizeParams.x === 1 && resizeParams.y === 1"
        @mousedown="(event) => startResize(event, { x: 1, y: 1 })"
      />
      <div
        class="external-courseware-resize-handle left"
        :data-active="resizeParams.x === -1 && resizeParams.y === 0"
        @mousedown="(event) => startResize(event, { x: -1, y: 0 })"
      />
      <div
        class="external-courseware-resize-handle right"
        :data-active="resizeParams.x === 1 && resizeParams.y === 0"
        @mousedown="(event) => startResize(event, { x: 1, y: 0 })"
      />
      <div
        class="external-courseware-resize-handle top"
        :data-active="resizeParams.x === 0 && resizeParams.y === -1"
        @mousedown="(event) => startResize(event, { x: 0, y: -1 })"
      />
      <div
        class="external-courseware-resize-handle top-left"
        :data-active="resizeParams.x === -1 && resizeParams.y === -1"
        @mousedown="(event) => startResize(event, { x: -1, y: -1 })"
      />
      <div
        class="external-courseware-resize-handle top-right"
        :data-active="resizeParams.x === 1 && resizeParams.y === -1"
        @mousedown="(event) => startResize(event, { x: 1, y: -1 })"
      />
    </template>
  </Box>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@core/BaseComponent';
import Box from '@/component/ui-component/box-component/Box';
import {
  initialize as pmInitialize,
  destroy as pmDestroy,
  invokeMethod as pmInvokeMethod,
  registerMethod as pmRegisterMethod,
  unregisterMethod as pmUnregisterMethod,
} from '@tencent/easy-postmessage';

export default {
  components: { Box },
  extends: BaseComponent,
  props: {},
  data() {
    return {
      hasShown: false, // 本浮窗打开过一次后置为 true
      iframeUrl: this.getIframeUrl(),
      coursewareText: this.getCoursewareText(),
      title: this.getTitle(),
      width: 800,
      height: 600,
      isResizing: false,
      isDragging: false,
      resizingOffsetX: 0, // 调整窗口尺寸时对左上角坐标产生的偏移量
      resizingOffsetY: 0,
      resizeParams: { x: 0, y: 0 }, // 当前的调整尺寸方向，1 表示右/下，-1 表示左/上
      isExpanded: false, // 是否全屏
    };
  },
  created() {
    pmInitialize();
    pmRegisterMethod('getTasks', async () => {
      const result = await TCIC.SDK.instance.getTasks(0);
      result.tasks.forEach((taskInfo) => {
        this.handleTaskUpdate(taskInfo);
      });
    });

    this.minWidth = 300;
    this.minHeight = 200;
    this.offsetX = 0;
    this.offsetY = 0;
  },
  beforeDestroy() {
    pmDestroy();
    pmUnregisterMethod('getTasks');
    if (this.isResizing) {
      this.stopDrag();
    }
  },
  async mounted() {
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.handleTaskUpdate);
    try {
      const hc = await TCIC.SDK.instance.waitComponentLoaded('header-component');
      const header = hc?.dom?.getVueInstance();
      if (header) {
        this.menu = header.$refs.header.menu.find(menu => menu.name === 'externalCourseware');
        this.menu.label = this.getTitle();
      } else {
        console.error('%c [ header ]-135', 'font-size:13px; background:pink; color:#bf2c9f;', header);
      }
    } catch (err) {
      TCIC.SDK.instance.reportException(
        'ExternalCoursewareComponent',
        {
          message: err.message,
          stack: err.stack,
        },
      );
    }
  },
  methods: {
    init() {
      this.$nextTick(() => {
        this.toggleComponentDrag(true, '.ui-box-header');
      });
    },
    show() {
      this.resetLayout();
      if (!this.hasShown) {
        this.hasShown = true;
        this.init();
      }
    },
    resetText() {
      this.coursewareText = this.getCoursewareText();
      this.title = this.getTitle();
      this.iframeUrl = this.getIframeUrl();
    },
    resetLayout(resetPosition = false) {
      const layoutParams = {
        display: 'block',
        style: 'overflow: visible',
        width: 'auto',
        height: 'auto',
        zIndex: 2000,
      };

      if (this.isExpanded) {
        layoutParams.left = '0';
        layoutParams.top = '48px';
        layoutParams.width = '100vw';
        layoutParams.height = 'calc(100vh - 48px)';
        layoutParams.transform = 'scale(1)';
      } else if (!this.hasShown) {
        layoutParams.left = `${Math.round((document.documentElement.clientWidth - this.width) / 2)}px`;
        layoutParams.top = `${Math.round((document.documentElement.clientHeight - this.height) / 2)}px`;
      }

      if (resetPosition) {
        this.offsetX = 0;
        this.offsetY = 0;
        layoutParams.transform = 'scale(1)';
        layoutParams.left = `${Math.round((document.documentElement.clientWidth - this.width) / 2)}px`;
        layoutParams.top = `${Math.round((document.documentElement.clientHeight - this.height) / 2)}px`;
      }

      TCIC.SDK.instance.updateComponent('external-courseware-component', layoutParams);
    },
    hide() {
      TCIC.SDK.instance.updateComponent('external-courseware-component', { display: 'none' });
    },
    toggle() {
      if (this.isVisible) {
        this.hide();
      } else {
        this.show();
      }
    },
    getCoursewareText() {
      const roomInfo = TCIC.SDK.instance.getNameConfig().roomInfo;

      return i18next.t('在线课件:课件', {
        courseware: roomInfo.courseware,
      });
    },
    getTitle() {
      const roomInfo = TCIC.SDK.instance.getNameConfig().roomInfo;

      return i18next.t('在线课件:标题', {
        courseware: roomInfo.courseware,
      });
    },
    getIframeUrl() {
      const params = {
        courseware: this.getCoursewareText(),
        title: this.getTitle(),
        userId: TCIC.SDK.instance.getUserId(),
        classId: TCIC.SDK.instance.getClassInfo().classId,
        token: TCIC.SDK.instance.getToken(),
        editable: TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant() ? '1' : '0',
        pushMode: 'postMessage',
        lng: TCIC.SDK.instance.getLanguage(),
      };

      return `./externalCourseware.html?${Object.entries(params)
        .map(([key, value]) => `${key}=${encodeURIComponent(value)}`)
        .join('&')
      }`;
    },
    handleTaskUpdate(taskInfo) {
      if (taskInfo.taskId === 'external-courseware') {
        try {
          if (this.$refs.iframe) {
            pmInvokeMethod(this.$refs.iframe, 'notifyTaskUpdate', {
              seq: taskInfo.seq,
              task_id: taskInfo.taskId,
              status: taskInfo.status,
              content: taskInfo.content,
              create_time: taskInfo.createTime,
              update_time: taskInfo.updateTime,
              expire_time: taskInfo.expireTime,
              binding_user: taskInfo.bindingUser,
            });
          }
        } catch (err) {
          console.error('[ExternalCourseware] notifyTaskUpdate fail', err);
        }

        try {
          let tabCount = 0;
          const content = JSON.parse(taskInfo.content);
          if (Array.isArray(content.tabs)) {
            tabCount = content.tabs.length;
          }

          this.menu.badge = tabCount;
        } catch (err) {
          console.error('[ExternalCourseware] parse content fail', err);
        }
      }
    },
    startResize(e, params) {
      if (this.isResizing) {
        return;
      }
      this.isResizing = true;
      this.resizeParams = params;

      this.startX = e.clientX;
      this.startY = e.clientY;
      this.startWidth = this.width;
      this.startHeight = this.height;

      document.documentElement.addEventListener('mousemove', this.updateResize, {
        capture: true,
      });
      document.documentElement.addEventListener('mouseup', this.stopResize, {
        capture: true,
      });
    },
    updateResize(e) {
      const deltaX = e.clientX - this.startX;
      const deltaY = e.clientY - this.startY;

      const beforeWidth = this.width;
      const beforeHeight = this.height;

      this.width = Math.max(this.minWidth, this.startWidth + deltaX * this.resizeParams.x);
      this.height = Math.max(this.minHeight, this.startHeight + deltaY * this.resizeParams.y);

      const deltaWidth = this.width - beforeWidth;
      const deltaHeight = this.height - beforeHeight;

      this.resizingOffsetX += this.resizeParams.x < 0 ? -deltaWidth : 0;
      this.resizingOffsetY += this.resizeParams.y < 0 ? -deltaHeight : 0;
    },
    stopResize() {
      this.isResizing = false;
      const { resizingOffsetX, resizingOffsetY } = this;

      if (resizingOffsetX !== 0 || resizingOffsetY !== 0) {
        this.offsetX += resizingOffsetX;
        this.offsetY += resizingOffsetY;
        TCIC.SDK.instance.updateComponent('external-courseware-component', {
          transform: `translate(${this.offsetX}px, ${this.offsetY}px)`,
        });
      }

      this.resizingOffsetX = 0;
      this.resizingOffsetY = 0;
      this.resizeParams = { x: 0, y: 0 };

      document.documentElement.removeEventListener('mousemove', this.updateResize, {
        capture: true,
      });
      document.documentElement.removeEventListener('mouseup', this.stopResize, {
        capture: true,
      });
    },
    onComponentDragStart() {
      this.isDragging = true;
    },
    onComponentDragEnd() {
      this.isDragging = false;
    },
    expand() {
      this.isExpanded = true;
      this.resetLayout();
    },
    collapse() {
      this.isExpanded = false;
      this.resetLayout(true);
    },
  },
};
</script>
<style lang="less">
.external-courseware-component {
  position: relative;
  user-select: none;
  overflow: visible;
  box-shadow: 0 0 20px 0 rgba(19,41,75,.2);

  .ui-box-header {
    position: relative;
    cursor: grab;
    z-index: 0;
  }

  &.is-dragging {
    .ui-box-header::after {
      content: '';
      position: fixed;
      left: -100vw;
      right: -100vw;
      top: -100vh;
      bottom: -100vh;
      z-index: 1;
    }
  }

  .ui-box-header-btn-group {
    z-index: 2;
  }

  &.ui-box .ui-box-content {
    overflow: hidden !important;
    border-radius: 0 !important;
    background: #fff !important;
  }

  .ui-box-header-btn {
    cursor: pointer;
  }

  .external-courseware-content {
    width: 100%;
    flex-grow: 1;
    flex-shrink: 1;
  }

  &.is-resizing {
    .external-courseware-content {
      /*  resize 期间避免 mousemove 事件被 iframe 捕获 */
      pointer-events: none;
    }

    .external-courseware-resize-handle {
      --handle-size: 24px;
    }
  }

  &.is-dragging {
    .ui-box-header {
      cursor: grabbing;
    }

    .external-courseware-content {
      /*  drag 期间避免 mousemove 事件被 iframe 捕获 */
      pointer-events: none;
    }
  }

  &.is-expanded {
    position: fixed;
    width: 100% !important;
    height: 100% !important;

    .ui-box-header {
      pointer-events: none;
    }

    .external-courseware-resize-handle {
      pointer-events: none;
    }

    .ui-box-header-btn {
      pointer-events: auto;
    }
  }

  .external-courseware-frame {
    border: none;
    width: 100%;
    height: 100%;
    background: #fff;
  }

  .external-courseware-resize-handle {
    position: absolute;
    z-index: 1;
    user-select: none;
    --handle-size: 16px;

    &[data-active] {
      left: -100vw !important;
      right: -100vw !important;
      top: -100vh !important;
      bottom: -100vh !important;
      width: auto !important;
      height: auto !important;
      z-index: 3 !important;
    }

    &.bottom,
    &.top {
      width: 100%;
      height: var(--handle-size);
      left: 0;
    }

    &.bottom-left,
    &.bottom-right,
    &.top-left,
    &.top-right {
      width: calc(var(--handle-size) * 2);
      height: calc(var(--handle-size) * 2);
      z-index: 2;
    }

    &.left,
    &.right {
      width: var(--handle-size);
      height: 100%;
      top: 0;
    }

    &.bottom {
      bottom: calc(var(--handle-size) / 2 * -1);
      cursor: row-resize;
    }

    &.bottom-left {
      left: calc(var(--handle-size) * -1);
      bottom: calc(var(--handle-size) * -1);
      cursor: sw-resize;
    }

    &.bottom-right {
      right: calc(var(--handle-size) * -1);
      bottom: calc(var(--handle-size) * -1);
      cursor: se-resize;
    }

    &.left {
      left: calc(var(--handle-size) / 2 * -1);
      cursor: col-resize;
    }

    &.right {
      right: calc(var(--handle-size) / 2 * -1);
      cursor: col-resize;
    }

    &.top {
      top: calc(var(--handle-size) / 2 * -1);
      cursor: row-resize;
    }

    &.top-left {
      left: calc(var(--handle-size) * -1);
      top: calc(var(--handle-size) * -1);
      cursor: nw-resize;
    }

    &.top-right {
      right: calc(var(--handle-size) * -1);
      top: calc(var(--handle-size) * -1);
      cursor: ne-resize;
    }
  }
}
</style>
