<template>
  <div
    class="class-reminder-component"
    :class="[{ 'small-screen': isSmallScreen,'full-screen': isFullScreen, 'header-visible': isHeaderVisible}, layoutClass, ]"
  >
    <div
      v-if="showTip && !isDeviceDetect"
      :class="['header__class-tips', {'header__mobile' : isSmallScreen}]"
    >
      <!-- 老师/助教 -->
      <template v-if="isTeacher || isAssistant">
        <interact-reminder-component-teacher-tip v-if="customElements.includes('interact-reminder-component-teacher-tip')" />
        <template v-else>
          <span class="tips-class-text">{{ translateTip.enterNumber }}</span>
          <span class="tips-class-number">{{ classMemberCount }}</span>
          <span class="tips-class-text">{{ translateTip.studentNumberTip }}</span>
        </template>
        <div class="class-operation">
          <el-button
            id="tips-class-start"
            size="mini"
            data-user-event="InteractClassReminder-start"
            type="primary"
            @click="startClass"
          >
            {{ translateTip.startRoom }}
          </el-button>
          <el-button
            id="tips-class-leave"
            data-user-event="InteractClassReminder-leave"
            class="btn-white"
            size="mini"
            @click="leaveClass"
          >
            {{ translateTip.leaveRoom }}
          </el-button>
        </div>
      </template>
      <!-- 学生端 -->
      <template v-else-if="teacherJoined">
        <span class="tips-class-text">{{ translateTip.arrivedTip }}</span>
      </template>
      <template v-else>
        <template v-if="realDuration >= 0 && isStudent">
          <span class="tips-class-text">{{ translateTip.teacherArrviedTip }}</span>
        </template>
        <template v-else-if="realDuration < 0 && duration > 86400">
          <span class="tips-class-text">{{ translateTip.startClassDate }}:</span>
          <span class="tips-class-text">{{ classStartTip }}</span>
        </template>
        <template v-else-if="hasSetDuration">
          <span class="tips-class-text">{{ translateTip.timeContent }}</span>
          <span class="tips-class-clock">{{ Math.floor(Math.floor(duration / 3600) / 10) }}</span>
          <span class="tips-class-clock">{{ Math.floor(duration / 3600) % 10 }}</span>
          <span class="tips-class-text">:</span>
          <span class="tips-class-clock">{{ Math.floor(Math.floor(duration % 3600 / 60) / 10) }}</span>
          <span class="tips-class-clock">{{ Math.floor(duration % 3600 / 60) % 10 }}</span>
          <span class="tips-class-text">:</span>
          <span class="tips-class-clock">{{ Math.floor(duration % 60 / 10) }}</span>
          <span class="tips-class-clock">{{ duration % 60 % 10 }}</span>
        </template>
        <div
          v-if="hasSetDuration"
          class="class-operation"
        >
          <el-button
            id="tips-class-leave"
            data-user-event="InteractClassReminder-leave"
            size="mini"
            type="primary"
            @click="leaveClass"
          >
            {{ translateTip.leaveRoom }}
          </el-button>
        </div>
      </template>
    </div>

    <transition-group name="fade">
      <template v-if="showCountDown">
        <div
          key="count-down-mask"
          class="count-down-mask"
        />
        <div
          key="count-down"
          class="count-down-wrapper"
        >
          <div class="inner">
            <span class="second">{{ countDown }}</span>
            <span class="unit">s</span>
            <p>{{ translateTip.prepareContent }}</p>
          </div>
        </div>
      </template>
    </transition-group>
    <FeedbackQrDialog v-if="showFeedbackQrDialog" />
  </div>
</template>
<script>
import i18next from '@/util/i18nextByKey';
import BaseComponent from '@/component/core/BaseComponent';
import FeedbackQrDialog from '../header-component/sub-component/FeedbackQrDialog.vue';
import Constant from '@/util/Constant';
import Util from '@/util/Util';

const defaultCountDownTime = 3;
export default {
  name: 'QuizComponent',
  components: {
    FeedbackQrDialog,
  },
  extends: BaseComponent,
  props: {
    custom: {
      type: String,
      default: '',
    },
  },
  data() {
    return {
      showCountDown: false,
      showFeedbackQrDialog: false,
      duration: 0,
      realDuration: 0,
      hasSetDuration: false,
      isTeacher: false,
      isAssistant: false,
      isStudent: false,
      isSupervisor: false,
      classMemberCount: 0,
      teacherJoined: false,
      classStatus: -1, // -1 表示还未赋值
      layoutClass: null,
      isDeviceDetect: TCIC.SDK.instance.getState(Constant.TStateDeviceDetect),
      countDown: 0,
      hideTip: false,
      isFullScreen: false,
      isHeaderVisible: false,
      roomInfo: {},
      roleInfo: {},
      classStartTip: '',
    };
  },
  computed: {
    showTip() {
      // hideTip 用作提前隐藏
      return this.classStatus > -1 && this.classStatus !== TCIC.TClassStatus.Already_Start && !TCIC.SDK.instance.isLiveClass() && !this.hideTip;
    },
    translateTip() {
      return {
        startRoom: this.roomInfo.startRoom,
        leaveRoom: this.roomInfo.leaveRoom,
        prepareContent: i18next.t('roomRemind.prepare', this.roomInfo),
        timeContent: i18next.t('roomRemind.timeRemain', this.roomInfo),
        enterNumber: i18next.t('{{room}}已到', { room: this.roomInfo.room }),
        studentNumberTip: i18next.t('位{{student}}', { student: this.roleInfo.student }),
        arrivedTip: i18next.t(
          '{{arg_0}}已进入{{arg_1}}，即将{{arg_2}}，请做好准备',
          { arg_0: this.roleInfo.teacher, arg_1: this.roomInfo.name, arg_2: this.roomInfo.startRoom },
        ),
        teacherArrviedTip: i18next.t('{{arg_0}}马上就来了，请稍候...', { arg_0: this.roleInfo.teacher }),
        startClassDate: i18next.t('上课时间'),
      };
    },
    customElements() {
      return this.custom?.split(',') || [];
    },
  },
  mounted() {
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;
    this.makeSureClassJoined(this.onJoinClass);
  },

  methods: {
    onJoinClass() {
      this.initData();
      this.bindEvent();
    },

    initData() {
      this.isAssistant = TCIC.SDK.instance.isAssistant();
      this.isTeacher = TCIC.SDK.instance.isTeacher();
      this.isSupervisor = TCIC.SDK.instance.isSupervisor();
      this.isStudent = TCIC.SDK.instance.isStudent();
      this.classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status);
      const classInfo = TCIC.SDK.instance.getClassInfo();

      this.duration = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Duration);
      this.classStartTime = classInfo.startTime; // 单位是秒
      this.classStartTip = Util.formatLocalDuration(this.classStartTime, 'YYYY/MM/DD HH:mm');

      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Duration, (duration) => {
        this.duration = Math.abs(duration);
        this.realDuration = duration;
        this.hasSetDuration = true;
      });
      const permissions = TCIC.SDK.instance.getPermissionList();
      this.permissionListUpdateHandler(permissions);


      TCIC.SDK.instance.registerState(Constant.TStateClassStartCountDowning, i18next.t('点击{{arg_0}}时的3s倒计时', { arg_0: this.roomInfo.startRoom }), false);
    },

    bindEvent() {
      // 课堂状态变更
      TCIC.SDK.instance.promiseState(TCIC.TMainState.Class_Status, TCIC.TClassStatus.Already_Start).then(() => {
        if (this.classStatus === TCIC.TClassStatus.Already_Start) {
          return;
        }
        this.classStartHandler();
      });

      // 人数变更
      this.addLifecycleTCICStateListener(TCIC.TMainState.Member_Count, this.memberNumberUpdateHandler);

      // 课堂布局状态变化
      this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Layout, this.updateLayout);

      // 老师是否已经进入课堂
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, this.permissionListUpdateHandler);
      // 设备检测完成后才显示
      this.addLifecycleTCICStateListener(Constant.TStateDeviceDetect, this.deviceDetectHandler);

      // 监听全屏状态变更
      this.addLifecycleTCICStateListener(Constant.TStateFullScreen, this.TStateFullScreenHandler);
      // 监听全屏状态变更
      this.addLifecycleTCICStateListener(Constant.TStateHeaderVisible, this.headerVisibleStateChangeHandler);
    },

    // 课堂成员变更
    memberNumberUpdateHandler(number) {
      this.classMemberCount = number;
    },

    startClass() {
      if (!this.classStatus !== TCIC.TClassStatus.Already_Start) {
        this.classStatus = TCIC.TClassStatus.Already_Start;
        TCIC.SDK.instance.startClass()
          .then(() => {
            this.classStartHandler();
          })
          .catch((error) => {
            this.classStatus = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status);
            window.showToast(i18next.t('{{arg_0}}失败: {{arg_1}}', { arg_0: this.roomInfo.startRoom, arg_1: error.errorMsg }), 'error');
            TCIC.SDK.instance.reportEvent('handle_start_class', error, -1);
          });
      }
    },

    leaveClass() {
      TCIC.SDK.instance.leaveClass();
    },

    classStartHandler() {
      if (!TCIC.SDK.instance.isInteractClass() && !TCIC.SDK.instance.isUnitedClass()) {
        // 非互动班课时直接返回
        return;
      }
      // 学生3s内不能发言
      if (!this.isTeacher) {
        TCIC.SDK.instance.setState('TStateDisableChat', true);
        setTimeout(() => {
          TCIC.SDK.instance.setState('TStateDisableChat', false);
        }, 3000);
      }

      if (this.isTeacher || TCIC.SDK.instance.isMobile()) {
        TCIC.SDK.instance.setState(Constant.TStateClassStartCountDowning, true);
        this.startCountDown(defaultCountDownTime);
        // 提前隐藏提示框
        this.hideTip = true;
      } else {
        this.classStatus = TCIC.TClassStatus.Already_Start;
      }
      if (!this.isTeacher && this.isSmallScreen) {  // 只有移动端（非老师角色）需要展示该提示
        window.showToast(i18next.t('{{startRoom}}啦~', { startRoom: this.roomInfo.startRoom }));
      }

      // 处理上课前添加的在线 H5 课件
      const teduBoard = TCIC.SDK.instance.getBoard();
      if (teduBoard) {
        const currentBoardId = teduBoard.getCurrentBoard();
        let onlineElIdMap = localStorage.getItem('onlineElIdMap');
        onlineElIdMap = onlineElIdMap ? JSON.parse(onlineElIdMap) : {};
        if (currentBoardId && onlineElIdMap[currentBoardId]) {
          teduBoard.deleteBoard(currentBoardId);
        }
      }
    },

    deviceDetectHandler(state) {
      this.isDeviceDetect = state;
    },

    updateLayout(layout) {
      this.layoutClass = layout;
    },

    startCountDown(time = 3) {
      this.countDown = time;
      this.showCountDown = !TCIC.SDK.instance.isLiveClass();
      setTimeout(() => {
        this.countDown -= 1;
        if (this.countDown <= 0) {
          this.$nextTick(() => {
            this.showCountDown = false;
            this.classStatus = TCIC.TClassStatus.Already_Start;
            TCIC.SDK.instance.setState(Constant.TStateClassStartCountDowning, false);
            // 两分钟后再展示反馈二维码
            setTimeout(() => {
              this.showFeedbackQrDialog = Util.shouldShowQrcode();
            }, 1000 * 2 * 60);
          });
        } else {
          this.startCountDown(this.countDown);
        }
      }, 1000);
    },

    TStateFullScreenHandler(flag) {
      this.isFullScreen = flag;
    },
    headerVisibleStateChangeHandler(flag) {
      this.isHeaderVisible = flag;
    },

    permissionListUpdateHandler(permissionList) {
      const teacherId = TCIC.SDK.instance.getClassInfo().teacherId;
      this.teacherJoined = permissionList.find(permission => permission.userId === teacherId);
    },
  },
};
</script>
<style lang="less">

.class-reminder-component {
  .header__class-tips {
    position: absolute;
    top: calc(64px + env(safe-area-inset-top));
    left: 50%;
    width: 296px;
    min-height: 55px;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.4);
    border-radius: 8px 8px 8px 8px;
    padding: 20px 16px;
    // margin-left: -30px;
    transition: all 0.2s;
    text-align: center;

    > * {
      vertical-align: middle;
    }


    .tips-class-text {
      font-size: 14px;
      line-height: 35px;
      font-weight: 500;
      color: #FFF;
    }

    .tips-class-number {
      color: #FFF;
      font-size: 20px;
      font-weight: 900;
      margin: 0 2px;
    }

    .tips-class-icon {
      margin: 8px 8px 8px 0;
      width: 16px;
      height: 16px;
      background-repeat: no-repeat;
      background-position: center;
      background-image: url("./assets/ic_tips.png");
    }

    .tips-class-clock {
      background: #fff;
      margin-left: 0;
      border-radius: 2px;
      width: 18px;
      height: 26px;
      line-height: 26px;
      color: #000;
      vertical-align: middle;
      font-size: 16px;
      font-weight: bold;
      display: inline-block;
      text-align: center;
    }

    .tips-class-start {
      float: right;
      margin: 5px 10px 5px 40px;
    }
  }

  &.top, &.double {
    .header__class-tips {
      top: 174px;
      transition: all 0.2s;

      &.header__mobile {
        top: 127px;
        transition: all 0.2s;
      }
    }

    &.full-screen {
      .header__class-tips {
        &.header__mobile {
          top: 2px;
          transition: all 0.2s;
        }
      }

      &.header-visible {
        .header__class-tips {
          &.header__mobile {
            top: 45px; /* 对齐header-component 高度 */
          }
        }
      }
    }
  }


  .fade-enter-active, .fade-leave-active {
    transition: opacity 1s;
  }

  .fade-enter, .fade-leave-to {
    opacity: 0;
  }

  .count-down-mask {
    background: rgba(0, 0, 0, 0.4);
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
  }

  .btn-white {
    color: #366EF4;
    background-color: #F3F3F3;
    border: 1px solid #366EF4;
    &:hover {
      border: 1px solid #366ef4;
    }
  }

  .count-down-wrapper {
    position: absolute;
    top: 0;
    bottom: 0;
    right: 0;
    left: 0;
    width: 200px;
    height: 200px;
    border-radius: 100%;
    background: #fff;
    margin: auto;
    text-align: center;
    display: flex;
    align-items: center;
    justify-content: center;

    .inner {
      padding-bottom: 20px;

      .second {
        font-size: 60px;
        color: #006EFF;
      }

      .unit {
        font-size: 24px;
        // vertical-align: sub;
        color: #006EFF;
      }

      p {
        font-size: 14px;
      }
    }
  }

  &.small-screen {
    .count-down-wrapper {
      transform: scale(0.75);
    }
  }

  .class-operation {
    margin-top: 16px;
  }
}
</style>
