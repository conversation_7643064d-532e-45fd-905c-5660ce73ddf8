<template>
  <div
    ref="member-list-ref"
    :class="['member-list-component', { 'small-screen': isSmallScreen }]"
  >
    <Box
      @hide="hide"
    >
      <template #title>
        <span>{{ roomInfo.memberList }}</span> <span class="count">({{ translateTip.totalMemberCountTip }})</span>
      </template>
      <template #content>
        <div class="content-wrapper">
          <div
            class="member-list__menu"
          >
            <div :class="['member-list__tabs', {'disabled': searchKey || isLoading}]">
              <span
                :class="['member-list__tab', {'active':showAllMember}]"
                @click="filterOffline(false)"
              >
                <span>{{ $t('全部') }}</span>
                <span class="count">({{ totalMemberCount }})</span>
              </span>
              <span
                :class="['member-list__tab', {'active':!showAllMember}]"
                @click="filterOffline(true)"
              >
                <span>{{ $t('离线') }}</span>
                <span class="count">({{ offlineMemberCount }})</span>
              </span>
            </div>
            <el-input
              v-model.trim="searchKey"
              class="course-ware-input"
              :placeholder="$t('输入名字搜索')"
              prefix-icon="el-icon-search"
              maxlength="10"
              clearable
              @input="searchName"
            />
          </div>
          <div class="member-list__table-wrap">
            <table
              v-if="tableHeader"
              :key="showAllMember ? 'all' : 'offline'"
              v-infinite-scroll="loadMore"
              infinite-scroll-immediate-check
              class="member-list__table"
            >
              <thead class="member-list__thead">
                <tr>
                  <template
                    v-for="(item, index) in tableHeader"
                  >
                    <th
                      v-if="tableColVisibleMap[item.key]"
                      :key="index"
                      :class="['member-list__th', item.class || '']"
                      :style="item.style || ''"
                      :title="item.label"
                    >
                      {{ item.label }}
                    </th>
                  </template>
                </tr>
              </thead>
              <tbody
                ref="memberlist-tbody"

                class="member-list__tbody"
              >
                <tr
                  v-for="(item, index) in renderMemberList"
                  :key="index"
                  :class="['member-list__tr', item.currentStatus === TStatuesOnline ? 'member-list__row-online' : 'member-list__row-offline' ]"
                >
                  <td
                    v-if="tableColVisibleMap.userName"
                    class="member-list__td member-list__user-name-wrap"
                    :title="item.userName"
                    @click="printMember(item)"
                  >
                    <span
                      :class="['device-icon', {'offline': item.currentStatus !== TStatuesOnline}, getPlatformStringFromCode(item.device).icon]"
                      :title="getPlatformStringFromCode(item.device).label"
                    />
                    <i
                      :class="[item.role === 'teacher' ? 'teacher-icon' : item.role === 'assistant' ? 'assistant-icon' : 'student-icon' , 'role-icon']"
                    >
                      <span>
                        {{ roleInfo[item.role] }}
                      </span>
                    </i>
                    <span
                      :class="['member-list__user-name', {'offline': item.currentStatus !== TStatuesOnline}, {'student': item.role === 'student'}]"
                    >
                      <HighlightText
                        :text="item.colorName"
                        class-name="highlight-text-overflow"
                      />
                    </span>
                  </td>
                  <td
                    v-if="tableColVisibleMap.handUp"
                    class="member-list__td member-list__handup-wrap"
                    :title="item.userName"
                  >
                    <!-- 举手 -->
                    <div
                      v-show="item.handUpTimes > 0 || item.handUpActive"
                      :class="getMemberIconClass(item, 'handup')"
                    >
                      <div
                        v-if="item.handUpActive"
                        class="text"
                      >
                        {{ $t('举手中') }}
                      </div>
                      <div
                        v-else
                        class="context"
                      >
                        {{ item.handUpTimes ? `x ${item.handUpTimes}` : '' }}
                      </div>
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.stage"
                    class="member-list__td"
                  >
                    <!-- 上台 -->
                    <div
                      class="icon-wrapper"
                    >
                      <div
                        v-if="item.role != 'teacher' && !isLive && isClassStarted && item.currentStatus === TStatuesOnline"
                        :class="[
                          getMemberIconClass(item, 'stage', !!item.loadingStateState)
                        ]"
                        @click="onMemberAction(item, item.stage ? TTypeStageDown : TTypeStageUp)"
                      />
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.mic"
                    class="member-list__td"
                  >
                    <!-- 麦克风 -->
                    <div class="icon-wrapper">
                      <el-tooltip
                        v-if="isClassStarted && item.currentStatus === TStatuesOnline && item.stage"
                        placement="bottom"
                        effect="dark"
                        :disabled="getDeviceShowState(item, 'mic') !== 'error'"
                        :content="micStatusTip"
                        :hide-after="2000"
                        :popper-class="getDeviceShowState(item, 'mic') ==='error' ? 'detect-error-tps': ''"
                      >
                        <div
                          :class="[
                            getMemberIconClass(item, 'mic', !!item.loadingMicState, selfMicOn)
                          ]"
                          @click="onMemberAction(item, isDeviceOpen(item, 'mic') ? TTypeMicClose : TTypeMicOpen)"
                        />
                      </el-tooltip>
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.camera"
                    class="member-list__td"
                  >
                    <!-- 摄像头 -->
                    <div class="icon-wrapper">
                      <el-tooltip
                        v-if="isClassStarted && item.currentStatus === TStatuesOnline && item.stage"
                        placement="bottom"
                        effect="dark"
                        :disabled="getDeviceShowState(item, 'camera') !== 'error'"
                        :content="cameraStatusTip"
                        :hide-after="2000"
                        :popper-class="getDeviceShowState(item, 'camera') ==='error' ? 'detect-error-tps': ''"
                      >
                        <div
                          :class="[
                            getMemberIconClass(item, 'camera', !!item.loadingCameraState, selfCameraOn)
                          ]"
                          @click="onMemberAction(item, isDeviceOpen(item, 'camera') ? TTypeCameraClose : TTypeCameraOpen)"
                        />
                      </el-tooltip>
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.board"
                    class="member-list__td"
                  >
                    <!-- 白板 -->
                    <div class="icon-wrapper">
                      <div
                        v-if="isClassStarted && item.currentStatus === TStatuesOnline && item.role != 'teacher'"
                        :class="[
                          getMemberIconClass(item, 'board',!!item.loadingBoardState)
                        ]"
                        @click="onMemberAction(item, item.board ? TTypeBoardDisable : TTypeBoardEnable)"
                      />
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.silence"
                    class="member-list__td"
                  >
                    <!-- 聊天 -->
                    <div
                      class="icon-wrapper"
                    >
                      <div
                        v-if="item.role != 'teacher' &&item.currentStatus === TStatuesOnline"
                        :class="[
                          getMemberIconClass(item, 'chat', !!item.loadingSpeakState)
                        ]"
                        @click="onClickChat(item)"
                      />
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.screen"
                    class="member-list__td"
                  >
                    <!-- 分享 -->
                    <div class="icon-wrapper">
                      <div
                        v-if="isClassStarted && item.currentStatus === TStatuesOnline && item.stage"
                        :class="[
                          getMemberIconClass(item, 'screen',!!item.loadingShareState),
                          canClickScreenIcon(item) ? '' : 'cursor-default',
                        ]"
                        @click="onMemberAction(item, item.screen !== 0 ? TTypeScreenShareClose : TTypeScreenShareOpen)"
                      />
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.trophy"
                    class="member-list__td"
                  >
                    <div class="icon-wrapper">
                      <div
                        v-if="isClassStarted && item.showTrophyIcon"
                        :class="[
                          getMemberIconClass(item, 'trophy'),
                        ]"
                        @click="sendTrophy(item)"
                      >
                        <div class="context">
                          {{ item.trophy ? `x ${item.trophy}` : '' }}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td
                    v-if="tableColVisibleMap.kick"
                    class="member-list__td"
                  >
                    <div
                      v-if="item.role != 'teacher'"
                      class="icon-wrapper"
                    >
                      <div
                        v-if="item.currentStatus === TStatuesOnline"
                        :class="[
                          getMemberIconClass(item, 'kick'),
                        ]"
                        @click="onKick(item)"
                      />
                    </div>
                  </td>
                </tr>
              </tbody>
              <div
                v-if="isLoading"
                class="bottom-tip"
              >
                {{ $t('加载中...') }}
              </div>
              <div
                v-if="!hasMore"
                class="bottom-tip"
              >
                {{ $t('没有更多了') }}
              </div>
            </table>
          </div>
          <div class="member-list-component-footer">
            <!-- 全员静音 -->
            <div
              v-if="!isLive && canMemberStageUp"
              class="mute-all"
            >
              <MuteAll />
            </div>
            <!-- 循环上台 -->
            <div
              v-if="!isLive && canMemberStageUp && isClassStarted && !isOneOnOneClass && !isCDNClass && !isBigRoom"
              :class="['carousel', {'mobile': isMobile}]"
            >
              <el-checkbox
                v-model="carousel"
                @change="onCarouselChange"
              >
                {{ carouselTips }}
              </el-checkbox>
              <div
                v-if="!isMobile && !isElectron"
                class="carousel-config"
                @click="onDisableClick"
              >
                <el-input-number
                  v-model="interval"
                  size="mini"
                  :step="1"
                  :min="10"
                  :max="99"
                  :disabled="carousel"
                  controls-position="right"
                  class="carousel-input"
                />
                <span class="carousel-tips">{{ $t('秒/次') }}</span>
              </div>
              <div
                v-if="isPad || isElectron"
                class="carousel-config"
                @click="onDisableClick"
              >
                <select
                  id="select_interval"
                  v-model="interval"
                >
                  <option
                    v-for="itemValue in intervalList"
                    :key="itemValue"
                    :value="itemValue"
                    :selected="itemValue === interval ? true : false"
                  >
                    {{ itemValue }}
                  </option>
                </select>
                <span class="carousel-tips">{{ $t('秒/次') }}</span>
              </div>
            </div>
          </div>
        </div>
      </template>
    </Box>
  </div>
</template>

<script>
import i18next from 'i18next';
import BaseComponent from '@/component/core/BaseComponent';
import Constant from '@/util/Constant';
import { getStartedStatus } from '@/util/class';
import DeviceUtil from '@/util/DeviceUtil';
import Box from '@/component/ui-component/box-component/Box';
import HighlightText from '@/component/ui-component/highlight-text-component/HighlightText';
import MuteAll from './sub-component/MuteAll';
import Lodash from 'lodash';
import MemberIcon from './MemberIcon';
import './MemberIcon.less';

export default {
  components: {
    MuteAll,
    Box,
    HighlightText,
  },
  extends: BaseComponent,
  mixins: [MemberIcon],
  data() {
    return {
      // TCIC常量重定义，保证在HTML可以直接绑定
      TTypeCameraOpen: TCIC.TMemberActionType.Camera_Open,
      TTypeCameraClose: TCIC.TMemberActionType.Camera_Close,
      TTypeMicOpen: TCIC.TMemberActionType.Mic_Open,
      TTypeMicClose: TCIC.TMemberActionType.Mic_Close,
      TTypeHandUp: TCIC.TMemberActionType.Hand_Up,
      TTypeHandUpCancel: TCIC.TMemberActionType.Hand_Up_Cancel,
      TTypeKickOut: TCIC.TMemberActionType.Kick_Out,
      TTypeBoardEnable: TCIC.TMemberActionType.Board_Enable,
      TTypeBoardDisable: TCIC.TMemberActionType.Board_Disable,
      TTypeSilence: TCIC.TMemberActionType.Silence,
      TTypeSilenceCancel: TCIC.TMemberActionType.Silence_Cancel,
      TTypeStageUp: TCIC.TMemberActionType.Stage_Up,
      TTypeStageDown: TCIC.TMemberActionType.Stage_Down,
      TTypeKickOutForever: TCIC.TMemberActionType.Kick_Out_Forever,
      TTypeScreenShareOpen: TCIC.TMemberActionType.Screen_Share_Open,
      TTypeScreenShareClose: TCIC.TMemberActionType.Screen_Share_Close,
      TStatuesOnline: TCIC.TMemberStatus.Online,
      TDeviceStatusUnknown: TCIC.TDeviceStatus.Unknown,
      TDeviceStatusOpen: TCIC.TDeviceStatus.Open,
      // onJoinClass时初始化表头
      hasJoinedClass: false,
      tableHeader: null,
      tableColVisibleMap: null,
      // 其他变量定义
      arrowOffset: 0,
      memberList: [],
      isLoading: false,
      hasMore: true,
      intervalList: [10, 20, 30, 40, 50, 60, 70, 80, 90],
      pageIndex: 1,
      pageCount: 1,
      // 是否是直播课
      isLive: false,
      isCDNClass: false,    // 是否CDN课
      isOneOnOneClass: false, // 是否是1v1课
      isVideoOnlyClass: false, // 是否纯视频课
      canMemberStageUp: true, // 成员是否能上台
      isBigRoom: false, // 是否是大班课
      // 是否已经开始上课
      isClassStarted: false,
      // 是否可见
      isVisible: false,
      // 奖杯TaskId
      trophyTaskId: 'trophy',
      trophyList: [],
      isSendingTrophy: false,
      totalMemberCount: 0,           // 成员总人数
      offlineMemberCount: 0,    // 离线人数
      showAllMember: true,      // 是否展示所有成员
      searchKey: '',        // 搜索关键字
      pageSize: 10,          // 每一页显示人数
      popoverSTO: null,
      carousel: false,
      carouselTips: i18next.t('循环上台'),
      carouselTimer: null,    // 文案定时器
      interval: 30,
      isMobile: false,    // 是否移动端
      isElectron: false,  // 是否桌面端
      isPad: false,      // 是否pad
      roleInfo: {},
      roomInfo: {},
      filterSystemUser: true, // 过滤系统机器人帐号信息
      isMuteAll: false,
      selfCameraOn: false,
      selfMicOn: false,
      isOnStage: false,
      isScreenShareOpen: false,
    };
  },

  computed: {
    isTeacher() {
      return TCIC.SDK.instance.isTeacher();
    },
    translateTip() {
      return {
        totalMemberCountTip: i18next.t('共{{arg_0}}人', { arg_0: this.totalMemberCount }),
      };
    },
    micStatusTip() {
      let tip = i18next.t('设备不可用（损坏、未授权等）');
      const errCode = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
      switch (errCode) {
        case -1001: // 打开失败
          tip = i18next.t('麦克风打开失败，请检查系统设置');
          break;
        case -1002: // 找不到设备
          tip = i18next.t('找不到可用麦克风');
          break;
        case -1003: // 未授权
          tip = i18next.t('麦克风权限未开启，请前往开启');
          break;
        case -1004: // 设备被占用
          tip = i18next.t('麦克风被占用，请关闭其他软件');
          break;
        case 2: // 已关闭
          tip = i18next.t('麦克风已关闭，请开启');
          break;
        default:
          break;
      }
      return tip;
    },
    cameraStatusTip() {
      let tip = i18next.t('设备不可用（损坏、未授权等）');
      const errCode = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
      switch (errCode) {
        case -1001: // 打开失败
          tip = i18next.t('摄像头打开失败，请检查系统设置');
          break;
        case -1002: // 找不到设备
          tip = i18next.t('找不到可用摄像头');
          break;
        case -1003: // 未授权
          tip = i18next.t('摄像头权限未开启，请前往开启');
          break;
        case -1004: // 设备被占用
          tip = i18next.t('摄像头被占用，请关闭其他软件');
          break;
        case 2: // 已关闭
          tip = i18next.t('摄像头已关闭，请开启');
          break;
        default:
          break;
      }
      return tip;
    },
    renderMemberList() {
      const { memberList, trophyList } = this;
      const classInfo = TCIC.SDK.instance.getClassInfo();
      const everyoneTrophy = trophyList.find(item => item.userId === 'everyone');
      return memberList.map((member) => {
        const renderMember = { ...member };
        const trophy = trophyList.find(item => item.userId === member.userId);
        renderMember.trophy = (trophy ? trophy.count : 0) + (everyoneTrophy ? everyoneTrophy.count : 0);
        if (this.searchKey.length > 0) {
          renderMember.colorName = `${member.userName.replace(this.searchKey, `<span class="em">${this.searchKey}</span>`)}`;
        } else {
          renderMember.colorName = `${member.userName}`;
        }
        switch (true) {
          case classInfo.assistants.includes(member.userId):
            renderMember.role = 'assistant';
            renderMember.showTrophyIcon = false;
            break;
          case member.userId === classInfo.teacherId:
            renderMember.role = 'teacher';
            renderMember.showTrophyIcon = false;
            break;
          case member.role === 3:
            renderMember.role = 'supervisor';
            renderMember.showTrophyIcon = false;
            break;
          default:
            renderMember.role = 'student';
            renderMember.showTrophyIcon = true;
            break;
        }
        return renderMember;
      }).filter(item => item.role !== 'supervisor')
      .sort((a, b) => {
        if (a.role === 'teacher') {
          return -1;
        }
        // 在线的在前面
        if (a.currentStatus === TCIC.TMemberStatus.Online && b.currentStatus !== TCIC.TMemberStatus.Online) {
          return -1;
        }
      });
    },
  },
  async mounted() {
    if (this.isTCICComponent) {
      // 允许拖动
      this.toggleComponentDrag(true, '.member-list-component-header');
    }
    const { roleInfo, roomInfo } = TCIC.SDK.instance.getRoleInfo();
    this.roleInfo = roleInfo;
    this.roomInfo = roomInfo;

    this.isMobile = TCIC.SDK.instance.isMobile();
    this.isElectron = TCIC.SDK.instance.isElectron();
    this.isPad = TCIC.SDK.instance.isPad();
    this.userId = TCIC.SDK.instance.getUserId();
    this.hasMemberListPermission = TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant() || TCIC.SDK.instance.isSupervisor();
    this.isClassStarted = getStartedStatus();
    this.getMemberList();
    this.addLifecycleTCICStateListener(TCIC.TMainState.Class_Status, (status) => {
      const isClassStarted = status === TCIC.TClassStatus.Already_Start;
      if (isClassStarted === this.isClassStarted) {
        return;
      }
      this.isClassStarted = isClassStarted;
      this.updateTableColVisible({
        screen: !this.isLive && !this.isCDNClass && this.isClassStarted,
      });
    });

    this.addLifecycleTCICStateListener(TCIC.TMainState.Screen_Share, (status) => {
      this.isScreenShareOpen = status < 2;
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingCameraState, ({ value, userId, type }) => {
      const isSelf = userId === TCIC.SDK.instance.getUserId();
      if (!isSelf && this.hasMemberListPermission) { // 是否有花名册权限
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[MemberList] TStateLoadingCameraState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        console.log(`[MemberList] TStateLoadingCameraState ${userId} ${type} ${JSON.stringify(value)}`);
        if (typeof value?.open === 'boolean') {
          let newCameraState = value.open ? TCIC.TDeviceStatus.Open : TCIC.TDeviceStatus.Closed;
          if (!value.open) {
            if (typeof value.deviceState === 'number') {
              // 学生返回的设备状态
              newCameraState = value.deviceState;
            } else if (userId === this.userId) {
              // 自己的设备状态
              newCameraState = TCIC.SDK.instance.getState(TCIC.TMainState.Video_Device_Status);
            }
          }
          console.log(`[MemberList] TStateLoadingCameraState, ${userId}, isSelf ${userId === this.userId}, cameraState ${member.cameraState} -> ${newCameraState}`);
          member.cameraState = newCameraState;
        }
        member.loadingCameraState = value?.loading ? value : null;
        // 直接修改数组项不会触发刷新，改用 splice
        // this.memberList[index] = member;
        this.memberList.splice(index, 1, member);
      }

      if (isSelf && type === 'loadingOwnCamera') {
        if (typeof value.open === 'boolean') {
          // eslint-disable-next-line vue/no-mutating-props, no-nested-ternary
          this.selfCameraOn = !!value.open;
        }
      }
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingMicState, ({ value, userId, type }) => {
      const isSelf = userId === TCIC.SDK.instance.getUserId();
      if (!isSelf && this.hasMemberListPermission) { // 是否有花名册权限
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[MemberList] TStateLoadingMicState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        console.log(`[MemberList] TStateLoadingMicState ${userId} ${type} ${JSON.stringify(value)}`);
        member.loadingMicState = value?.loading ? value : null;
        if (typeof value?.open === 'boolean') {
          let newMicState = value.open ? TCIC.TDeviceStatus.Open : TCIC.TDeviceStatus.Closed;
          if (!value.open) {
            if (typeof value.deviceState === 'number') {
              // 学生返回的设备状态
              newMicState = value.deviceState;
            } else if (userId === this.userId) {
              // 自己的设备状态
              newMicState = TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Device_Status);
            }
          }
          console.log(`[MemberList] TStateLoadingMicState, ${userId}, isSelf ${userId === this.userId}, micState ${member.cameraState} -> ${newMicState}`);
          member.micState = newMicState;
        }
        // 直接修改数组项不会触发刷新，改用 splice
        // this.memberList[index] = member;
        this.memberList.splice(index, 1, member);
      }
      if (isSelf && type === 'loadingOwnMic') {
        if (typeof value.open === 'boolean') {
          // eslint-disable-next-line vue/no-mutating-props, no-nested-ternary
          this.selfMicOn = !!value.open;
        }
      }
    });
    // add other loading
    this.addLifecycleTCICEventListener(Constant.TStateLoadingBoardState, ({ value, userId, type }) => {
      if (this.hasMemberListPermission) {
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[MemberList] TStateLoadingBoardState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        member.loadingBoardState = value?.loading ? true : null;
        console.log(`[MemberList] TStateLoadingBoardState ${userId} ${type} ${JSON.stringify(value)} ${member.loadingBoardState}`);
        this.memberList.splice(index, 1, member);
      };
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingStageState, ({ value, userId, type }) => {
      if (this.hasMemberListPermission) {
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[MemberList] TStateLoadingStageState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        member.loadingStateState = value?.loading ? true : null;
        console.log(`[MemberList] TStateLoadingStageState ${userId} ${type} ${JSON.stringify(value)} ${member.loadingStateState}`);
        this.memberList.splice(index, 1, member);
      };
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingSpeakState, ({ value, userId, type }) => {
      if (this.hasMemberListPermission) {
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[MemberList] TStateLoadingSpeakState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        member.loadingSpeakState = value?.loading ? true : null;
        console.log(`[MemberList] TStateLoadingSpeakState ${userId} ${type} ${JSON.stringify(value)} ${member.loadingSpeakState}`);
        this.memberList.splice(index, 1, member);
      };
    });
    this.addLifecycleTCICEventListener(Constant.TStateLoadingShareState, ({ value, userId, type }) => {
      if (this.hasMemberListPermission) {
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          console.log(`[MemberList] TStateLoadingShareState but user not found ${userId}`);
          return;
        }
        const member = this.memberList[index];
        member.loadingShareState = value?.loading ? true : null;
        console.log(`[MemberList] TStateLoadingShareState ${userId} ${type} ${JSON.stringify(value)} ${member.loadingShareState}`);
        this.memberList.splice(index, 1, member);
      }
    });

    const currentMicState = DeviceUtil.selfDeviceState.camera;
    if (typeof currentMicState === 'boolean') {
      this.selfCameraOn = currentMicState;
    }

    this.isMuteAll = TCIC.SDK.instance.getState(TCIC.TMainState.Silence_Mode, TCIC.TClassSilenceMode.Free_Chat) === TCIC.TClassSilenceMode.All_Mute;
    this.addLifecycleTCICStateListener(TCIC.TMainState.Silence_Mode, (value) => {
      this.isMuteAll = value === TCIC.TClassSilenceMode.All_Mute;
    });

    this.isOnStage = TCIC.SDK.instance.getState(TCIC.TMainState.Stage_Status, false);
    this.addLifecycleTCICStateListener(TCIC.TMainState.Stage_Status, (status) => {
      this.isOnStage = status;
    });

    // this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List, (memberList) => {
    //   // 过滤线上录制用户，不展示到花名册中
    //   if (this.filterSystemUser) {
    //     this.memberList = this.removeLoadingStatus(memberList.filter(item => !/webrecord_\d+_\d+/g.test(item.userName)));
    //   } else {
    //     this.memberList = this.removeLoadingStatus(memberList);
    //   }
    //   this.pageCount = TCIC.SDK.instance.getState(TCIC.TMainState.Member_List_Page_Count);    // 避免因未变更而不通知
    //   this.updatePopper(200);
    // }, { noEmitWhileSubscribe: true });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List_Page_Count, (pageCount) => {
      this.pageCount = pageCount;
    }, { noEmitWhileSubscribe: true });
    this.addLifecycleTCICStateListener(Constant.TStateHeaderMemberActive, (isVisible) => {
      this.isVisible = isVisible;
      if (isVisible) {    // 重新显示后重置内容
        this.pageIndex = 1;
        this.hasMore = true;
        this.showAllMember = true;
        this.searchKey = '';
        this.getMemberList();
      }
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List_Total_Member_Count, (count) => {
      this.totalMemberCount = count;
    });
    this.addLifecycleTCICStateListener(TCIC.TMainState.Member_List_Offline_Member_Count, (count) => {
      this.offlineMemberCount = count;
    });
    // 监听奖杯发放事件
    this.addLifecycleTCICEventListener(TCIC.TMainEvent.Task_Updated, this.onTrophyTaskUpdate);
    this.handleMemberUpdate();

    this.makeSureClassJoined(this.onJoinClass);
    // 初始化循环上台状态
    this.carousel = TCIC.SDK.instance.getState(Constant.TStateCarousel, false);
    this.addLifecycleTCICStateListener(Constant.TStateCarousel, (flag) => { // 同步循环同步状态
      this.carousel = flag;
    });
    this.interval = TCIC.SDK.instance.getState(Constant.TStateCarouselInterval, 30);
    this.addLifecycleTCICStateListener(Constant.TStateCarouselInterval, (interval) => { // 同步循环同步状态
      this.interval = interval;
    });
    this.$EventBus.$on('member-action', (user, actionType) => this.onMemberAction(user, actionType));
  },
  beforeDestroy() {
    this.isVisible = false;
  },
  methods: {
    async toggleSelfCamera() {
      const userId = TCIC.SDK.instance.getUserId();
      const myPermission = TCIC.SDK.instance.getPermission(userId);
      if (!TCIC.SDK.instance.isTeacherOrAssistant() && !myPermission.camera) {
        // 学生如果没有权限，则直接返回
        window.showToast(i18next.t('{{arg_0}}关闭了你的摄像头权限，你可以举手申请打开', { arg_0: this.roleInfo.teacher }), 'error');
        return;
      }
      if (this.selfCameraOn) {
        DeviceUtil.toggleLocalDeviceWithLoadingEvent(
            'camera',
            false,
            () => TCIC.SDK.instance.stopLocalVideo(),
            {
              caller: 'Header',
              reason: 'enableCamera-false',
              reportAction: 'stopLocalVideo',
            },
        );
      } else {
        if (this.isScreenShareOpen
            && TCIC.SDK.instance.isCollegeClass()
            && TCIC.SDK.instance.isWeb()) {
          window.showToast(i18next.t('你正在进行屏幕共享，暂时无法开启摄像头'));
          return;
        }
        try {
          if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
            if (!myPermission.camera) {
              await TCIC.SDK.instance.memberAction({
                userId,
                actionType: TCIC.TMemberActionType.Camera_Open,
              });
            }
          }
          TCIC.SDK.instance.reportLog('startLocalVideo', '[CTSettingComponent] enableCamera true');
          const currentVideoComponent = document.getElementById(`video-${TCIC.SDK.instance.getUserId()}`);
          if (currentVideoComponent) {
            DeviceUtil.toggleLocalDeviceWithLoadingEvent(
                'camera',
                true,
                () => TCIC.SDK.instance.startLocalVideo(currentVideoComponent),
                {
                  caller: 'Header',
                  reason: 'enableCamera-true',
                  reportAction: 'startLocalVideo',
                },
            );
          }
        } catch (e) {
          if (e && e.errorMsg) {
            window.showToast(`${e.errorMsg || e.errorDetail}`, 'error');
          } else {
            window.showToast(i18next.t('打开本地视频采集遇到一些问题'), 'error');
          }
        }
      }
    },
    async toggleSelfMic() {
      console.log('MemberList::toggleSelfMic=>', TCIC.SDK.instance.getUserId(), !this.selfMicOn);
      if (!TCIC.SDK.instance.isTeacherOrAssistant() && TCIC.SDK.instance.isCoTeachingClass()) {
        if (!this.selfMicOn) {
          try {
            const userId = TCIC.SDK.instance.getUserId();
            const myPermission = TCIC.SDK.instance.getPermission(userId);
            // 学生如果没有权限，则直接返回
            if (!myPermission.mic) {
              window.showToast(i18next.t('{{arg_0}}关闭了你的麦克风权限，你可以举手申请打开', { arg_0: this.roleInfo.teacher }), 'error');
              return;
            }
            await TCIC.SDK.instance.requireResource('mic');
          } catch (err) {
            window.showToast(i18next.t(
                '{{arg_0}}内打开麦克风数量超过限制，出于性能考虑，请您先联系{{arg_1}}，让其关闭部分{{arg_2}}的麦克风',
                { arg_0: this.roomInfo.name, arg_1: this.roleInfo.teacher, arg_2: this.roleInfo.student },
            ), 'error');
            return;
          }
        } else {
          await TCIC.SDK.instance.releaseResource('mic');
        }
      }

      try {
        if (TCIC.SDK.instance.isTeacher() || TCIC.SDK.instance.isAssistant()) {
          const userId = TCIC.SDK.instance.getUserId();
          const myPermission = TCIC.SDK.instance.getPermission(userId);
          if (!myPermission.mic) {
            await TCIC.SDK.instance.memberAction({
              userId,
              actionType: TCIC.TMemberActionType.Mic_Open,
            });
          }
        }
        TCIC.SDK.instance.muteLocalAudio(this.selfMicOn);
      } catch (e) {
        console.error('startLocalAudio error => ', e);
        if (e && e.errorMsg) {
          window.showToast(`${e.errorMsg || e.errorDetail}`, 'error');
        } else {
          window.showToast(i18next.t('打开本地音频采集遇到一些问题'), 'error');
        }
        if (!TCIC.SDK.instance.isTeacher() && TCIC.SDK.instance.isCoTeachingClass()) {
          TCIC.SDK.instance.releaseResource('mic');
        }
      }

      const actualMicState = TCIC.SDK.instance.getState(TCIC.TMainState.Class_Status) === TCIC.TClassStatus.Already_Start
          ? TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture) && TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Publish)
          : TCIC.SDK.instance.getState(TCIC.TMainState.Audio_Capture);
      TCIC.SDK.instance.reportLog('enableMic-actualMicState', `${actualMicState}`);
      if (!this.selfMicOn && !actualMicState) {
        try {
          const currentMicComponent = document.getElementById(`audio-${TCIC.SDK.instance.getUserId()}`);
          console.log(currentMicComponent.toString());
          console.log(currentMicComponent.toString());
          if (currentMicComponent) {
            await DeviceUtil.toggleLocalDeviceWithLoadingEvent(
                'mic',
                !this.selfMicOn,
                () => TCIC.SDK.instance.startLocalAudio(currentMicComponent),
                {
                  caller: 'HeaderSpeakerComponent',
                  reason: `enableMic-${!this.selfMicOn}`,
                  reportAction: 'startLocalAudio',
                },
            );
            TCIC.SDK.instance.reportLog('enableMic-startLocalAudio', `${!this.selfMicOn}`);
          }
        } catch (err) {
          TCIC.SDK.instance.reportLog('enableMic-startLocalAudio-error', `${!this.selfMicOn}, ${err?.toString()}`);
          window.showToast(i18next.t('麦克风打开失败'));
        }
      }
    },
    onClickChat(item) {
      if (!this.isMuteAll) {
        this.onMemberAction(item, item.silence ? this.TTypeSilenceCancel : this.TTypeSilence);
      } else {
        window.showToast(i18next.t('当前处于全员禁言状态，无法允许单个学生发言'), 'error');
      }
    },
    handleMemberUpdate() {
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Hand_Up_Update, (res) => {
        const { hand_ups: handUps = [] } = res;
        if (handUps?.length > 0) {
          handUps.forEach((handUp) => {
            const userId = handUp.user_id;
            const index = this.memberList.findIndex(item => item.userId === userId);
            if (index !== -1) {
              this.memberList.splice(index, 1, {
                ...this.memberList[index],
                handUpTimes: this.memberList[index].handUpTimes + 1,
              });
            }
          });
        }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Join, (userId) => {
        if (this.showAllMember) {
          this.updateUser(userId);
        } else {
          // 当前展示离线列表，如果列表中有这个人，则移除
          const index = this.memberList.findIndex(item => item.userId === userId);
          console.log(`[MemberList] offline tab: ${userId}`, index);
          if (index !== -1) {
            this.memberList.splice(index, 1);
          }
        }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Exit, (userId) => {
        if (this.showAllMember) {
          const index = this.memberList.findIndex(item => item.userId === userId);
          if (index !== -1) {
            this.memberList[index].currentStatus = TCIC.TMemberStatus.Offline;
          }
        } else {
          this.updateUser(userId);
        }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Stage_Up, ({ userId }) => {
          const index = this.memberList.findIndex(item => item.userId === userId);
          if (index === -1) {
            // 没在列表中，不处理
          } else {
            this.memberList[index].stage = true;
          }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Stage_Down, ({ userId }) => {
          const index = this.memberList.findIndex(item => item.userId === userId);
          if (index === -1) {
            // 没在列表中，不处理
          } else {
            this.memberList[index].stage = false;
          }
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Member_Info_Update, (memberInfo) => {
        const index = this.memberList.findIndex(item => item.userId === memberInfo.userId);
          if (index === -1) {
            // 没在列表中，不处理
          } else {
            this.memberList.splice(index, 1, { ...this.memberList[index], ...memberInfo });
          }
        console.log(`[MemberList] TMainEvent.Member_Info_Update ${memberInfo.userId}`, memberInfo);
      });
      this.addLifecycleTCICEventListener(TCIC.TMainEvent.Permission_Update, (permissionInfo) => {
        console.log(`[MemberList] TMainEvent.Permission_Update ${permissionInfo.userId}`, permissionInfo);
        for (const permissionItem of permissionInfo) {
          const memberIndex = this.memberList.findIndex(item => item.userId === permissionItem.userId);
          if (memberIndex !== -1) {
            const memberData = this.memberList[memberIndex];
            if (memberData.board !== permissionItem.board) {
              memberData.loadingBoardState = false;
            }
            this.memberList.splice(memberIndex, 1, {
              ...this.memberList[memberIndex],
              ...permissionItem,
            });
          }
        }
      });
    },
    updateUser(userId) {
      // 对于圆桌会议的台下观众，不展示上下台
      if (TCIC.SDK.instance.isRoundTable() && !(
        TCIC.SDK.instance.isTeacher(userId)
        || TCIC.SDK.instance.isAssistant(userId)
        || TCIC.SDK.instance.isRoundTableGuest(userId))
      ) {
        return;
      }
      this.getMember(userId).then((user) => {
        console.log(`[MemberList] member update ${userId}`, user);
        const index = this.memberList.findIndex(item => item.userId === userId);
        if (index === -1) {
          this.memberList.push(user);
        } else {
          this.memberList.splice(index, 1, user);
        }
      });
    },
    removeLoadingStatus(memberList) {
      return memberList.map((item) => {
        item.loadingBoardState = null;
        item.loadingStateState = null;
        item.loadingCameraState = null;
        item.loadingMicState = null;
        item.loadingSpeakState = null;
        item.loadingShareState = null;
        return item;
      });
    },
    onJoinClass() {
      const classInfo = TCIC.SDK.instance.getClassInfo();
      this.isLive = TCIC.SDK.instance.isLiveClass();
      this.isOneOnOneClass = TCIC.SDK.instance.isOneOnOneClass();
      this.isVideoOnlyClass = TCIC.SDK.instance.isVideoOnlyClass();
      this.isBigRoom = TCIC.SDK.instance.isBigRoom();
      this.isCDNClass = TCIC.SDK.instance.isUnitedLiveClass();
      this.canMemberStageUp = TCIC.SDK.instance.getClassInfo().maxRtcMember !== 0;
      this.hasJoinedClass = true;
      this.initTableHeader();
      this.getMemberList();
      // 初始化奖杯数据
      this.initTrophyCount();
    },
    initTableHeader() {
      if (!this.hasJoinedClass) {
        return;
      }
      this.tableHeader = [
        { key: 'userName', label: i18next.t('姓名'), class: 'member-list__user-name-wrap', style: 'text-align: left' },
        { key: 'handUp', label: i18next.t('举手'), class: 'member-list__handup-wrap' },
        { key: 'stage', label: i18next.t('上台') },
        { key: 'mic', label: i18next.t('语音') },
        { key: 'camera', label: i18next.t('视频') },
        { key: 'board', label: i18next.t('白板') },
        { key: 'silence', label: i18next.t('禁言') },
        { key: 'screen', label: i18next.t('共享') },
        { key: 'trophy', label: i18next.t('奖励') },
        { key: 'kick', label: i18next.t('移出') },
      ];
      this.tableColVisibleMap = {
        userName: true,
        handUp: !this.isLive && !this.isCDNClass,
        stage: !this.isLive && !this.isCDNClass,
        mic: !this.isLive && !this.isCDNClass,
        camera: !this.isLive && !this.isCDNClass,
        board: !this.isLive && !this.isCDNClass && !this.isVideoOnlyClass,
        silence: true,
        screen: !this.isLive && !this.isCDNClass,
        trophy: !this.isLive,
        kick: true,
      };
    },
    updateTableColVisible(data) {
      if (this.tableColVisibleMap) {
        this.tableColVisibleMap = {
          ...this.tableColVisibleMap,
          ...data,
        };
      }
    },
    printMember(user) {
      // uglifyjs生产环境会去掉console，这里加上 window 强制保留
      window.console.log('[MemberList] printMember', user);
    },
    onDisableClick: Lodash.throttle(function () {
      if (this.carousel) {
        window.showToast(i18next.t('请先结束当前循环上台，再修改设置！'));
      }
    }, 500, {
      leading: true,
      trailing: false,
    }),
    onCarouselChange(flag, event) {    // 循环上台状态变更
      const carouselCom = TCIC.SDK.instance.getComponent('carousel-component');
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (classInfo && classInfo.maxRtcMember < 1) {
        this.carousel = false;
        event.target.checked = false;
        window.showToast(i18next.t('当前{{arg_0}}不允许{{arg_1}}上台', { arg_0: this.roomInfo.name, arg_1: this.roleInfo.student }));
        return;
      }
      if (carouselCom && carouselCom.getVueInstance()) {
        if (flag) TCIC.SDK.instance.setState(Constant.TStateCarouselInterval, this.interval);
        carouselCom.getVueInstance().updateInterval(flag ? this.interval * 1000 : 0)
          .catch((err) => {
            this.carousel = false;
            event.target.checked = false;
            if (err.errorCode === 10632) {
              window.showToast(i18next.t('当前人数较少，无法开启循环上台功能'));
            } else {
              window.showToast(i18next.t('开启循环上台功能失败'));
            }
          });
        // if (!carouselCom.getVueInstance().updateInterval(flag ? this.interval * 1000 : 0)) {
        //   this.carousel = false;
        //   event.target.checked = false;
        //   window.showToast(i18next.t('当前人数较少，无法开启循环上台功能'));
        //   return;
        // };
      } else {
        this.carousel = false;
        event.target.checked = false;
        window.showToast(i18next.t('开启循环上台功能失败'));
        return;
      }
      this.carouselTips = flag ? i18next.t('已开启') : i18next.t('已关闭');
      if (this.carouselTimer) {
        clearTimeout(this.carouselTimer);
      }
      this.carouselTimer = setTimeout(() => {
        this.carouselTips = i18next.t('循环上台');
        this.carouselTimer = null;
      }, 3000);
    },
    onKick(user) {
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
      } else {
        TCIC.SDK.instance.showMessageBox('', i18next.t(
          '是否确认将{{arg_0}}移出{{arg_1}}？',
          { arg_0: user.userName, arg_1: this.roomInfo.name },
        ), [i18next.t('确定'), i18next.t('取消')], (index, options) => {
          if (index === 0) {
            if (options[0]) {
              this.onMemberAction(user, TCIC.TMemberActionType.Kick_Out_Forever);
            } else {
              this.onMemberAction(user, TCIC.TMemberActionType.Kick_Out);
            }
          }
        }, [{ text: i18next.t('不允许该{{arg_0}}再次加入该{{arg_1}}', { arg_0: this.roleInfo?.student, arg_1: this.roomInfo?.name }), value: false }]);
      }
    },
    canClickScreenIcon(user) {
      if (user.role === 'teacher') {
        // 老师一直有屏幕共享权限
        return false;
      }
      if (user.role === 'assistant') {
        // 助教从1.8.0开始一直有屏幕共享权限，但是旧版本用户可能关闭权限，新版本要支持打开
        return user.screen === 0;
      }
      return true;
    },
    async onMemberAction(user, actionType) {
      console.log(`[MemberList] onMemberAction ${user.role} ${user.userId} ${actionType}`);
      TCIC.SDK.instance.reportLog('[MemberList] onMemberAction', `[MemberList] onMemberAction ${user.role} ${user.userId} ${actionType}`);
      // if (actionType === TCIC.TMemberActionType.Mic_Open) {
      //   // if (this.getDeviceShowState(user, 'mic') === 'error') {
      //   //   // 设备异常，不能开启
      //   //   console.log(`[MemberList] can not open mic when device error, ${user.role} ${user.userId} ${user.micState}`);
      //   //   return;
      //   // }
      // }
      // if (actionType === TCIC.TMemberActionType.Camera_Open) {
      //   // if (this.getDeviceShowState(user, 'camera') === 'error') {
      //   //   // 设备异常，不能开启
      //   //   console.log(`[MemberList] can not open camera when device error, ${user.role} ${user.userId} ${user.cameraState}`);
      //   //   return;
      //   // }
      // }
      if (actionType === TCIC.TMemberActionType.Screen_Share_Open || actionType === TCIC.TMemberActionType.Screen_Share_Close) {
        if (!this.canClickScreenIcon(user)) {
          // 不能修改屏幕共享权限
          return;
        }
      }
      const classInfo = TCIC.SDK.instance.getClassInfo();
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {  // 不允许操作离线学员
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
        return;
      }
      const stageActions = [  // 上台相关操作
        TCIC.TMemberActionType.Stage_Up,
        TCIC.TMemberActionType.Stage_Down,
        TCIC.TMemberActionType.Camera_Open,
        TCIC.TMemberActionType.Camera_Close,
        TCIC.TMemberActionType.Mic_Open,
        TCIC.TMemberActionType.Mic_Close,
        TCIC.TMemberActionType.Screen_Share_Open,
        TCIC.TMemberActionType.Screen_Share_Close,
      ];
      if (stageActions.includes(actionType)) {  // 如果是上台相关功能
        if (this.isLive) {  // 直播课不允许上台
          window.showToast(i18next.t('直播课不支持{{arg_0}}上台', { arg_0: this.roleInfo.student }));
          return;
        }
        if (classInfo.maxRtcMember === 0) {  // 1v0非老师不允许上台
          if (user.role !== 'teacher') {
            window.showToast(i18next.t('当前{{arg_0}}设定上台人数为0，成员无法上台', { arg_0: this.roomInfo.name }));
            return;
          }
        }
        if (TCIC.SDK.instance.getState(Constant.TStateCarousel)) {   // 循环上台时禁用上下台
          if (actionType === TCIC.TMemberActionType.Stage_Up || actionType === TCIC.TMemberActionType.Stage_Down) {
            window.showToast(i18next.t('请先结束循环上台'));
            return;
          }
        }
      }

      const boardActions = [  // 涂鸦相关操作
        TCIC.TMemberActionType.Board_Enable,
        TCIC.TMemberActionType.Board_Disable,
      ];
      if (!this.isClassStarted) {  // 未开始上课，禁用上台及涂鸦相关操作
        if (stageActions.includes(actionType) || boardActions.includes(actionType)) {
          console.log(`[MemberList] onMemberAction ${user.userId} ${actionType}, class not started, return`);
          // window.showToast('上课前禁止该操作');
          return;
        }
      }
      // 先邀请上台在授予屏幕共享权限
      if (!user.stage && actionType === TCIC.TMemberActionType.Screen_Share_Open) {
        window.showToast(i18next.t('用户未上台无法使用屏幕共享功能'));
        return;
      }
      // 小程序不允许授权涂鸦
      if (user.device === TCIC.TDevice.Miniprogram && actionType === TCIC.TMemberActionType.Board_Enable) {
        window.showToast(i18next.t('小程序用户无法使用涂鸦功能'));
        return;
      }
      // 小程序不允许使用屏幕共享功能
      if (user.device === TCIC.TDevice.Miniprogram && actionType === TCIC.TMemberActionType.Screen_Share_Open) {
        window.showToast(i18next.t('小程序用户无法使用屏幕共享功能'));
        return;
      }
      // 移动端不允许使用屏幕共享功能
      if (user.device === TCIC.TDevice.Phone && actionType === TCIC.TMemberActionType.Screen_Share_Open) {
        window.showToast(i18next.t('移动端用户无法使用屏幕共享功能'));
        return;
      }

      let deviceType;
      let flag;
      let waitIMInstruction = false;

      const userId = TCIC.SDK.instance.getUserId();
      const isSelf = userId === user.userId;
      switch (actionType) {
        case TCIC.TMemberActionType.Camera_Open:
        case TCIC.TMemberActionType.Camera_Close:
          {
            if (isSelf) {
              await this.toggleSelfCamera();
              return;
            }
            const loadingState = user?.loadingCameraState;
            if (!!loadingState?.loading) {
              console.log(`[MemberList] onMemberAction ${user.userId} ${actionType} when ${loadingState.loading}, return`);
              // 不用显示toast，以免误点toast导致花名册关闭
              // window.showToast(user.userId === this.userId ? i18next.t('稍等片刻，请勿重复操作'): i18next.t('正在授权，请稍等'));
              return;
            }
            deviceType = 'camera';
            flag = actionType === TCIC.TMemberActionType.Camera_Open;
          }
          break;
        case TCIC.TMemberActionType.Mic_Open:
        case TCIC.TMemberActionType.Mic_Close:
          {
            if (isSelf) {
              await this.toggleSelfMic();
              return;
            }
            const loadingState = user?.loadingMicState;
            if (!!loadingState?.loading) {
              console.log(`[MemberList] onMemberAction ${user.userId} ${actionType} when ${loadingState.loading}, return`);
              // 不用显示toast，以免误点toast导致花名册关闭
              // window.showToast(user.userId === this.userId ? i18next.t('稍等片刻，请勿重复操作'): i18next.t('正在授权，请稍等'));
              return;
            }
            deviceType = 'mic';
            flag = actionType === TCIC.TMemberActionType.Mic_Open;
          }
          break;
        case TCIC.TMemberActionType.Board_Disable:
        case TCIC.TMemberActionType.Board_Enable:
          {
            deviceType = 'board';
            waitIMInstruction = true;
            flag = actionType === TCIC.TMemberActionType.Board_Enable;
          }
          break;
        case TCIC.TMemberActionType.Stage_Up:
        case TCIC.TMemberActionType.Stage_Down:
        {
            deviceType = 'stage';
            flag = actionType === TCIC.TMemberActionType.Stage_Up;
        }
          break;

        case TCIC.TMemberActionType.Silence:
        case TCIC.TMemberActionType.Silence_Cancel:
        {
            deviceType = 'speak';
            flag = actionType === TCIC.TMemberActionType.Silence;
        }
          break;
        case TCIC.TMemberActionType.Screen_Share_Open:
        case TCIC.TMemberActionType.Screen_Share_Close:
        {
            deviceType = 'share';
            flag = actionType === TCIC.TMemberActionType.Screen_Share_Open;
        }
          break;
      }
      const promise = deviceType
        ? DeviceUtil.toggleRemoteDeviceWithLoadingEvent(
          user.userId,
          deviceType,
          flag,
          () => TCIC.SDK.instance.memberAction({
            userId: user.userId,
            actionType,
          }),
          {
            caller: 'MemberList',
            reason: `memberAction-${actionType}`,
            action: `onMemberAction-${deviceType}-${flag}`,
            waitIMInstruction,
          },
        )
        : TCIC.SDK.instance.memberAction({
          userId: user.userId,
          actionType,
        });

      try {
        await promise;
      } catch (err) {
        window.showToast(err.errorMsg, 'error');
      }
    },
    loadMore() {
      if (this.isLoading || !this.hasMore) {
        console.log('loadMore', this.isLoading, this.hasMore);
        return;
      }
      this.pageIndex += 1;
      this.getMemberList();
    },
    getMemberList() {
      this.isLoading = true;
      let filterType = TCIC.TMemberType.All;
      if (!this.showAllMember && this.searchKey.length === 0) { // 不搜索且选择离线tab才会查询离线用户
        filterType = TCIC.TMemberType.Offline;
      }
      // 构建查询参数
      const filter = {
        page: this.pageIndex,
        limit: this.pageSize,
        type: filterType,
        keyword: this.searchKey,
      };
      TCIC.SDK.instance.getClassMemberList(filter).then((res) => {
        console.log('getMemberList', res);
        for (const member of res.members) {
          const index = this.memberList.findIndex(item => item.userId === member.userId);
            if (index !== -1) {
              this.memberList.splice(index, 1, member); // 触发Vue的响应式更新
            } else {
              this.memberList.push(member);
            }
            // this.memberList.push(member);
        }
        this.isLoading = false;
        if (this.memberList.length >= res.total) {
          console.log('getMemberList', this.memberList, res.total);
          this.hasMore = false;
        }
        TCIC.SDK.instance.setState(
          TCIC.TMainState.Member_List_Total_Member_Count,
          res.memberNumber,
          'TStore',
        );
      });
    },
    getMember(userId) {
      return TCIC.SDK.instance.getUserProfile(userId, false).then((res) => {
        console.log('[memberList] getMember', res);
        const memberInfo = new TCIC.TMemberInfo();
        memberInfo.deserialize(res);
        return memberInfo;
      });
    },
    getPlatformStringFromCode(device) {
      // 将平台码转换成可阅读的文本
      switch (device) {
        case TCIC.TDevice.Windows:
        case TCIC.TDevice.Mac:
          return {
            icon: 'pc',
            label: i18next.t('电脑'),
          };
        case TCIC.TDevice.Pad:
          return {
            icon: 'pad',
            label: i18next.t('平板'),
          };
        case TCIC.TDevice.Phone:
          return {
            icon: 'phone',
            label: i18next.t('手机'),
          };
        case TCIC.TDevice.TV:
          return {
            icon: 'pc',
            label: 'TV',
          };
        case TCIC.TDevice.Miniprogram:
          return {
            icon: 'mini',
            label: i18next.t('小程序'),
          };
        default:
          return {
            icon: 'unknow',
            label: i18next.t('未知'),
          };
      }
    },
    onTrophyTaskUpdate(taskInfo) {
      if (taskInfo.taskId === this.trophyTaskId) {
        const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
        this.trophyList = trophyComponent.getVueInstance().getList();
      }
    },
    initTrophyCount() {
      const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
      if (trophyComponent) {
        this.trophyList = trophyComponent.getVueInstance().getList();
      } else {
        console.error('trophy-component has not loaded yet');
      }
    },
    sendTrophy(user) {
      if (!this.$hasRole(['teacher', 'assistant', 'supervisor'])) {
        window.showToast(i18next.t('不可操作'));
        return;
      }
      if (user.currentStatus !== TCIC.TMemberStatus.Online) {
        window.showToast(i18next.t('该操作只对在线{{arg_0}}有效', { arg_0: this.roleInfo.student }));
        return;
      }
      if (!this.isClassStarted) {  // 未开始上课，禁用发送奖杯相关操作
        // window.showToast('上课前禁止该操作');
        return;
      }
      const trophyComponent = TCIC.SDK.instance.getComponent('trophy-component');
      trophyComponent.getVueInstance().distributeWithThrottle(user.userId);
    },
    searchName: Lodash.debounce(function (val) {
      this.searchKey = val.trim();
      this.pageIndex = 1;
      this.hasMore = true;
      this.memberList = [];
      this.getMemberList();
    }, 400),
    filterOffline(flag) {
      if (!this.searchKey) {
        this.showAllMember = !flag;
        this.pageIndex = 1;   // 切换TAB时重置页码
        this.hasMore = true;
        this.memberList = [];
        this.getMemberList();
      }
    },
    updatePopper(delay = 800) {    // 通知父组件刷新位置
      // 延时刷新popper(延时500ms仍能复现不更新的情况)
      clearTimeout(this.popoverSTO);
      this.popoverSTO = setTimeout(() => {
        const parentPopper = this.$parent;
        if (parentPopper && parentPopper.updatePopper && this.isVisible) {
          parentPopper.updatePopper();
        }
      }, delay);
    },
  },
};

</script>

<style lang="less">
@menuHeight: 50px;
@footerHeight: 80px;
.member-list-component {
  position: fixed;
  max-width: 100vw;
  width: 700px;
  right: 0;
  top: 64px;
  bottom: 0;
  transform-origin: right top;

  &.small-screen {
    top: 0;
  }
  .highlight-text-overflow {
    width: 90%;
    display: inline-block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .member-list__menu {
    padding-top: 4px;
    text-align: left;
    box-sizing: border-box;
    .member-list__tabs {
      display: inline-block;
      margin-left: 16px;
      padding: 14px;
      height: @menuHeight;
      background: rgba(0, 0, 0, 0.1);
      border-radius: 4px;

      &.disabled {
        opacity: 0.3;
      }

      span.member-list__tab {
        padding: 5px;
        line-height: 22px;
        font-size: 16px;
        font-weight: 500;
        color: #8A9099;

        &.active {
          color: #FFFFFF;
          background-color: #006EFF;
          border-radius: 4px;
        }
      }
    }

    .el-input {
      width: 242px;
      height: 50px;
      float: right;
      right: 16px;

      input.el-input__inner {
        padding-left: 50px;
        height: 100%;
        background: rgba(0, 0, 0, 0.1);
        border: none;
      }

      input::-webkit-input-placeholder {
        font-weight: 400;
        color: #8A9099;
        line-height: 22px
      }

      .el-input__prefix {
        left: 0px;
        margin: 13px 10px 13px 16px;
      }

      i.el-input__icon.el-icon-search {
        width: 24px;
        height: 24px;
        content: url('./assets/member-list__search.png');
      }
    }
  }


  .member-list__table-wrap {
    position: relative;
    padding: 0;
    box-sizing: border-box;
    height: calc(100% - @menuHeight - @footerHeight);
    padding-top: 24px;
    flex: 1;

    .member-list__table {
      border: 0;
      display: block;
      border-collapse: collapse;
      font-size: 14px;
      overflow-y: auto;
      white-space: nowrap;
      height: 100%;
      .member-list__thead {
        display: table;
        width: calc(100% - 16px);
        table-layout: fixed;
        margin-bottom: 15px;

        .member-list__th {
          box-sizing: border-box;
          font-size: 14px;
          font-weight: inherit;
          max-height: 40px;
          color: #A1A8B4;
          text-align: center;
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .bottom-tip {
        height: 30px;
        line-height: 30px;
        text-align: center;
        color: #8A9099;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        margin: auto;
      }

      .member-list__tbody {
        color: white;
        display: block;
        overflow-y: auto;
        // height: 400px;
        overflow-x: hidden;
        border-top: 1px solid rgba(184, 184, 184, 0.1);
        position: relative;
        padding: 12px 16px 12px 0;


        .member-list__tr {
          display: table;
          width: 100%;
          table-layout: fixed;

          .member-list__row-online {
            color: #ffffff;
          }

          .member-list__row-offline {
            color: rgba(#a1a8b4, 0.8);
          }

          &.disabled {
            .member-list__td {
              opacity: .5;

              &.member-list__user-name-wrap {
                opacity: 1
              }
            }
          }

          .member-list__td {
            text-align: center;
            padding: 4px 0;
            vertical-align: middle;
            position: relative;
            background: #191d2c;
            min-width:54px;
            .icon-wrapper {
              padding: 18px 12px;
              background: rgb(25, 29, 44);
              display: flex;
              justify-content: center;
              height: 60px;
            }

            .context {
              transform: scale(0.7);
              font-family: Number;
              position: absolute;
              bottom: 6px;
              left: 0;
              right: 0;
            }

            // icon 相关移到单独的less，FloatMemberList复用
          }

        }
      }

      .member-list__handup-wrap {
        width: 80px;
      }

      .member-list__user-name-wrap {
        overflow: hidden;
        line-height: 24px;
        max-width: 160px;
        min-width: 80px;
        width: 160px;
        text-align: left !important;
        padding-left: 16px !important;

        .member-list__user-name {
          font-size: 14px;
          white-space: nowrap;
          text-overflow: ellipsis;
          overflow: hidden;
          height: 24px;
          line-height: 24px;
          width: calc(100% - 60px);
          text-align: left;
          display: inline-block;
          vertical-align: middle;
          .student {
            width: calc(100% - 20px);
          }

          .em {
            color: #006EFF;
          }

          &.offline {
            color: #8A9099;
          }
        }
        .role-icon{
          display: inline-flex;
          align-items: center;
          justify-content: center;
          flex-shrink: 0;
          padding: 0 4px;
          border-radius: 2px;
          height: 20px;
          &.assistant-icon{
            background: linear-gradient(152deg, #23BE82 0%, #08AE6E 94%);
            span{
              color: #fff;
            }
          }
          &.teacher-icon{
            background: linear-gradient(152deg, #00A6FF 0%, #006DFF 94%);
            span{
              color: #fff;
            }
          }
          &.student-icon{
            border: 1px solid rgba(255,255,255,0.40);
            border-radius: 2px;
          }
          span{
            font-size: 12px;
              color: rgba(255,255,255,0.60);
            }
        }


        .device-icon {
          width: 16px;
          height: 16px;
          background-size: 100% auto;
          display: inline-block;
          vertical-align: middle;

          &.pc {
            background: url('./assets/device-pc.svg') no-repeat center;

            &.offline {
              background: url('./assets/device-pc-off.svg') no-repeat center;
            }
          }

          &.phone {
            background: url('./assets/device-phone.svg') no-repeat center;

            &.offline {
              background: url('./assets/device-phone-off.svg') no-repeat center;
            }
          }

          &.pad {
            background: url('./assets/device-pad.svg') no-repeat center;

            &.offline {
              background: url('./assets/device-pad-off.svg') no-repeat center;
            }
          }

          &.mini {
            background: url('../member-list-component/assets/device-mini.svg') no-repeat center;

            &.offline {
              background: url('../member-list-component/assets/device-mini-off.svg') no-repeat center;
            }
          }

          &.unknow {
            background: url('./assets/device-unknow.svg') no-repeat center;

            &.offline {
              background: url('./assets/device-unknow-off.svg') no-repeat center;
            }
          }
        }
      }

    }
  }

  .ui-box {
    height: 100%;
    .ui-box-content{
      overflow: hidden;
    }
  }

  .content-wrapper{
    height: 100%;
  }
  .member-list-component-footer {
    border-top: 1px solid rgba(184, 184, 184, 0.1);
    display: flex;
    padding: 16px;
    // background: #1c2131;
    position: relative;
    z-index: 2;
    height: @footerHeight;
    flex-direction: row;
    justify-content: center;
    .mute-all {
      display: flex;
      padding: 0;
      margin-right: 10px;
    }

    .carousel {
      display: flex;
      padding: 20px 0;
      justify-content: center;
      align-items: center;

      .el-checkbox__label {
        color: var(--text-color, #fff);
      }

      &.mobile {
        padding: 24px 0;
      }
      .carousel-config {
        display: flex;
        justify-content: center;
        align-items: center;
        height: 30px;
        .carousel-input {
          width: 35px;

          input {
            padding-left: 5px;
            padding-right: 0px;
            font-size: 14px;
            text-align: left;
            border: none;
            background: #000;
          }

          span.el-input-number__decrease, span.el-input-number__increase {
            border: none;
            width: 12px;
            background-color: transparent;

            .el-icon-arrow-up:before {
              content: '\25B2';
            }
            .el-icon-arrow-down:before {
              content: '\25BC';
            }
          }
        }
      }

      .carousel-tips {
        color: var(--text-color, #fff);
        font-size: 14px;
      }
    }

    .paginate {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      align-items: center;

      .el-pagination {
        button {
          padding-left: 5px;
          background: transparent;

          .el-icon {
            width: 20px;

            &.el-icon-arrow-left {
              content: url('./assets/ic_menu_left.svg')
            }

            &.el-icon-arrow-right {
              content: url('./assets/ic_menu_right.svg')
            }
          }
        }

        button[disabled] {
          .el-icon {
            &.el-icon-arrow-left {
              content: url('./assets/ic_menu_left_disable.svg')
            }

            &.el-icon-arrow-right {
              content: url('./assets/ic_menu_right_disable.svg')
            }
          }
        }

        .el-pager {
          li {
            font-size: 16px;
            font-weight: 500;
            background: transparent;
          }
        }
      }
    }
  }

}
</style>
