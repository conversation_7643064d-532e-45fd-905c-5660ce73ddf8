<template>
  <div
    v-if="isFeatureAvaiable"
    class="custom-feature"
  >
    <slot />
  </div>
</template>

<script>
export default {
  props: {
    feature: String,
  },
  data() {
    return  {
      isFeatureAvaiable: true,
    };
  },
  mounted() {
    if (TCIC.SDK.instance.isFeatureAvailable(this.feature)) {
      this.isFeatureAvaiable = true;
    } else {
      this.isFeatureAvaiable = false;
    }
  },
};
</script>

<style lang="less">
.custom-feature{
  width: 100%;
  height: 100%;
}
</style>
