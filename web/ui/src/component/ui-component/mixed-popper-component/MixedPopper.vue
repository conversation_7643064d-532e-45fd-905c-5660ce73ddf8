<template>
  <el-popover
    v-if="type === 'popover'"
    ref="popover"
    v-bind="popoverAttrs"
    :class="customClass"
    v-on="popoverListeners"
  >
    <template #reference>
      <slot name="reference" />
    </template>
    <slot />
    <slot name="footer" />
  </el-popover>
  <span v-else-if="type === 'dialog'">
    <span @click="innerVisible ? close() : open()">
      <slot name="reference" />
    </span>
    <el-dialog
      ref="dialog"
      v-bind="dialogAttrs"
      :visible="innerVisible"
      :class="customClass"
      v-on="dialogListeners"
      @update:visible="handleVisibleChange"
    >
      <template #title>
        <slot name="title" />
      </template>
      <slot />
      <template #footer>
        <slot name="footer" />
      </template>
    </el-dialog>
  </span>
  <span v-else-if="type === 'drawer'">
    <span @click="innerVisible ? close() : open()">
      <slot name="reference" />
    </span>
    <MobileDrawer
      ref="drawer"
      v-bind="drawerAttrs"
      :visible="innerVisible"
      :class="customClass"
      :wrapper-closable="wrapperClosable"
      v-on="drawerListeners"
      @update:visible="handleVisibleChange"
    >
      <template #title>
        <slot name="title" />
      </template>
      <slot />
      <template #footer>
        <slot name="footer" />
      </template>
    </MobileDrawer>
  </span>
  <div v-else />
</template>

<script>
import MobileDrawer from '../mobile-drawer-component/MobileDrawer';

export default {
  name: 'MixedPopper',
  components: {
    MobileDrawer,
  },
  model: {
    prop: 'forceShow',
  },
  props: {
    customClass: {
      type: [String, Array],
      default: '',
    },
    type: {
      type: String,
      default: 'popover',
      validator: value => ['popover', 'dialog', 'drawer'].includes(value),
    },
    visible: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: '',
    },
    forceShow: {
      type: Boolean,
      default: false,
    },
    wrapperClosable: {
      type: Boolean,
      default: true,
    },
  },
  data() {
    return {
      innerVisible: this.visible,
      popoverWatched: false,
      ...this.deriveAttrs(),
      ...this.deriveListeners(),
    };
  },
  watch: {
    visible(value) {
      if (this.type !== 'drawer' && this.wrapperClosable) this.innerVisible = value;
      if (this.type === 'popover') {
        if (!this.$refs.popover) {
          return;
        }
        if (value) {
          this.$refs.popover.doShow();
        } else {
          this.$refs.popover.doClose();
        }
      }
      if (this.type === 'drawer') {
        this.innerVisible = value;
      }
    },
    innerVisible(value) {
      if (value) {
        this.$emit('show');
      } else {
        this.$emit('hide');
      }
    },
    type: {
      handler(value) {
        if (value === 'popover' && !this.popoverWatched) {
          this.$nextTick(() => {
            if (!this.$refs.popover) {
              return;
            }
            this.popoverWatched = true;
            this.$refs.popover.$on('show', () => {
              this.handleVisibleChange(true);
            });
            this.$refs.popover.$on('hide', () => {
              this.handleVisibleChange(false);
            });
          });
        }
      },
      immediate: true,
    },
  },
  mounted() {
    this.$watch('$attrs', () => {
      const { popoverAttrs, dialogAttrs, drawerAttrs } = this.deriveAttrs();
      this.popoverAttrs = popoverAttrs;
      this.dialogAttrs = dialogAttrs;
      this.drawerAttrs = drawerAttrs;
    }, {
      deep: true,
    });
  },
  methods: {
    deriveAttrs() {
      return {
        popoverAttrs: {
          ...Object.entries(this.$attrs).reduce((acc, [key, value]) => {
            if (key.startsWith('popover-')) {
              acc[key.replace('popover-', '').toLowerCase()] = value;
            }
            return acc;
          }, {}),
        },
        dialogAttrs: {
          title: this.title,
          ...Object.entries(this.$attrs).reduce((acc, [key, value]) => {
            if (key.startsWith('dialog-')) {
              acc[key.replace('dialog-', '').toLowerCase()] = value;
            }
            return acc;
          }, {}),
        },
        drawerAttrs: {
          title: this.title,
          ...Object.entries(this.$attrs).reduce((acc, [key, value]) => {
            if (key.startsWith('drawer-')) {
              acc[key.replace('drawer-', '').toLowerCase()] = value;
            }
            return acc;
          }, {}),
        },
      };
    },
    deriveListeners() {
      return {
        popoverListeners: {
          ...Object.entries(this.$listeners).reduce((acc, [key, value]) => {
            if (key.startsWith('popover-')) {
              acc[key.replace('popover-', '').toLowerCase()] = value;
            }
            return acc;
          }, {}),
        },
        dialogListeners: {
          ...Object.entries(this.$listeners).reduce((acc, [key, value]) => {
            if (key.startsWith('dialog-')) {
              acc[key.replace('dialog-', '').toLowerCase()] = value;
            }
            return acc;
          }, {}),
        },
        drawerListeners: {
          ...Object.entries(this.$listeners).reduce((acc, [key, value]) => {
            if (key.startsWith('drawer-')) {
              acc[key.replace('drawer-', '').toLowerCase()] = value;
            }
            return acc;
          }, {}),
        },
      };
    },
    handleVisibleChange(value) {
      this.innerVisible = value;
      this.$emit('update:visible', value);
    },
    open() {
      this.handleVisibleChange(true);
    },
    close() {
      this.handleVisibleChange(false);
    },
  },
};
</script>
