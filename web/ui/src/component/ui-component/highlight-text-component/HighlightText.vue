<template>
  <span :class="className">
    <span
      v-for="(v, i) in highlightedText"
      :key="i"
      :class="[{'em': v.highlight}]"
    >{{ v.text }}</span>
  </span>
</template>


<script>
export default {
  name: 'HighlightText',
  props: {
    text: {
      type: String,
      default: '',
    },
    className: {
      type: String,
      default: '',
    },
  },
  computed: {
    highlightedText() {
      return this.parseHighLight(this.text) || [{}];
    },
  },
  methods: {
    parseHighLight(str) {
      const regex = /<[^>]+>|[^<>]+/g;
      const matches = str.match(regex);
      return matches.map((item, index) => {
        if (matches[index - 1] === '<span class="em">' && matches[index + 1] === '</span>') {
         return {
          text: item,
          highlight: true,
         };
        }
        if (item !== '<span class="em">' && item !== '</span>') {
          return {
            text: item,
            highlight: false,
          };
        }
        return {
          text: '',
          highlight: false,
        };
      });
    },
  },
};

</script>
