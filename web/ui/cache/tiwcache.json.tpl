{
    "resources": [
        {
            "version": "#FILENAME#",
            "sdkAppId": "0",
            "schoolId": "0",
            "url": "https://#DOMAIN#/#VERSION#/cache/offline_#FILENAME#.zip",
            "files": {
            },
            "regexes": {
                "https:\\/\\/dev\\-class\\.qcloudclass\\.com\\/#VERSION#\\/cache/(.+)": "/unmatch/$1",
                "https:\\/\\/dev\\-class\\.qcloudclass\\.com\\/#VERSION#\\/(.+)": "$1",
                "https:\\/\\/test\\-class\\.qcloudclass\\.com\\/#VERSION#\\/cache/(.+)": "/unmatch/$1",
                "https:\\/\\/test\\-class\\.qcloudclass\\.com\\/#VERSION#\\/(.+)": "$1",
                "https:\\/\\/pre\\-class\\.qcloudclass\\.com\\/#VERSION#\\/cache/(.+)": "/unmatch/$1",
                "https:\\/\\/pre\\-class\\.qcloudclass\\.com\\/#VERSION#\\/(.+)": "$1",
                "https:\\/\\/class\\.qcloudclass\\.com\\/#VERSION#\\/cache/(.+)": "/unmatch/$1",
                "https:\\/\\/class\\.qcloudclass\\.com\\/#VERSION#\\/(.+)": "$1",
                "https:\\/\\/res\\.qcloudclass\\.com\\/assets\\/(.+)": "/static/assets/$1",
                "https:\\/\\/res\\.qcloudtiw\\.com\\/board\\/(.+)": "/static/libs/board/$1",
                "https:\\/\\/log\\.qcloudtiw\\.com\\/(.+)": "/static/log/$1"
            },
            "media": {
                "fragSize": 524288,
                "supportFomats": "mp4|rmvb|avi|mov|flv|wmv|m3u8|mpeg|mp3|wav|pcm|aac|ogg"
            },
            "expired": 200,
            "devBuildTime": #TIMESTAMP#,
            "domains": {
                "www.xxx.com": [
                    "www.aaa.com"
                ]
            },
            "hostnames": {
                "class.qcloudclass.com": true,
                "test-class.qcloudclass.com": true,
                "pre-class.qcloudclass.com": true,
                "dev-class.qcloudclass.com": true,
                "res.qcloudclass.com": true,
                "res.qcloudtiw.com": true,
                "log.qcloudtiw.com": false
            }
        }
    ],
    "interval": #INTERVAL#,
    "forceUpdate": #FORCEUPDATE#,
    "basePath": #TCIC_WEB_BASE_PATH#
}
