const path = require('path');
const util = require('../util');

const xlsxFilename = 'locales.xlsx';
const outputFile = path.resolve(__dirname, xlsxFilename);

const sdkPath = path.resolve(__dirname, '../../../sdk');
const uiPath = path.resolve(__dirname, '../../../ui');
const sheets = []; // { name, data }[]
const lngs = util.lngs;

const wordsTotalJson = require(`${sdkPath}/src/constants/words.json`);
sheets.push({ name: 'words', data: wordsTotalJson });

const loginTotalJson = {};
for (const lng of lngs) {
  loginTotalJson[lng] = require(`${uiPath}/src/pages/login/locales/${lng}.json`);
}
sheets.push({ name: 'login', data: loginTotalJson });

const sdkTotalJson = {};
for (const lng of lngs) {
  sdkTotalJson[lng] = require(`${uiPath}/static/tcic/locales/sdk/${lng}.json`);
}
sheets.push({ name: 'sdk', data: sdkTotalJson });

const uiTotalJson = {};
for (const lng of lngs) {
  uiTotalJson[lng] = require(`${uiPath}/static/tcic/locales/ui/${lng}.json`);
}
sheets.push({ name: 'ui', data: uiTotalJson });

util.makeXlsxFile(sheets, outputFile);
