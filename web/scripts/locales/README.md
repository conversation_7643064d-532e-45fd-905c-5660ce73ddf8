# Web 端

Web 端多语言配置包括以下部分：
- words
  sdk/src/constants/words.json
- login
  ui/src/pages/login/locales/{lng}.json
- sdk
  ui/static/tcic/locales/sdk/{lng}.json
- ui
  ui/static/tcic/locales/ui/{lng}.json

对应词条文档

https://doc.weixin.qq.com/sheet/e3_AKYA7gbNACcHB9IqP6yS1eqqqTik0?scode=AJEAIQdfAAoPzew106AH8AygbdAFw&tab=wickkl

## 日常更新

更新流程：
1. 开发修改词条文档，将需要翻译的词条状态改为“待更新翻译”，通知产品
2. 产品翻译完成后更新词条文档，将需要翻译的词条状态改为“已更新待合入”，通知开发
3. 开发更新代码，清除词条状态，部署环境给产品验证

注意：
- 为方便维护，配置文件中的词条行号和文档中的行号应保持一致

## 全量更新

如果需要批量更新大量词条（比如新增一种语言），可以将全量词条从在线文档复制到本地 excel，利用脚本解析后再更新到目标 json，注意要手动对比后再修改代码中的词条

```
// $locales 代表脚本地址

// step1: 把现有配置转成本地excel，输出 $locales/web/locales.xlsx
node $locales/web/json2xlsx.js

// step2: 根据在线文档更新本地excel，如新增语言，需要在每个tab都增加一列

// step3: 把本地excel转成json，输出 $locales/output/{sheet}/{lng}.json
node $locales/web/xlsx2json.js
```

# Server 端

Server 端在七彩石的配置仅支持zh/en，其他语言统一通过前端配置适配，因为不确定国外访问七彩石cdn是否正常，所以配置先放在static：
- zh/en
  七彩石，后台同学配置
- other languages
  ui/static/tcic/locales/errcode/{lng}.json

对应词条文档

https://doc.weixin.qq.com/sheet/e3_AKYA7gbNACcHB9IqP6yS1eqqqTik0?scode=AJEAIQdfAAo4q1DwzdAH8AygbdAFw&tab=bu97iq

词条更新流程同 Web 端

少量词条直接修改配置文件，批量更新大量词条可以将全量词条从在线文档复制到本地 excel，利用脚本解析后再更新到目标 json，注意要手动对比后再修改代码中的词条

```
// $locales 代表脚本地址

// step1: 把现有配置转成本地excel，输出 $locales/server/ErrorCode.xlsx
node $locales/server/json2xlsx.js

// step2: 根据在线文档更新本地excel，如新增语言，需要在每个tab都增加一列

// step3: 把本地excel转成json，输出 $locales/output/errorcode/{lng}.json
node $locales/server/xlsx2json.js
```
