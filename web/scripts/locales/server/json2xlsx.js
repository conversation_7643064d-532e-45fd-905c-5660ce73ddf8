const path = require('path');
const util = require('../util');

const xlsxFilename = 'ErrorCode.xlsx';
const outputFile = path.resolve(__dirname, xlsxFilename);

const uiPath = path.resolve(__dirname, '../../../ui');
const sheets = []; // { name, data }[]
const lngs = util.lngs;

const errcodeTotalJson = {};
for (const lng of lngs) {
  errcodeTotalJson[lng] = require(`${uiPath}/static/tcic/locales/errorcode/${lng}.json`);
}
sheets.push({ name: 'errorcode', data: errcodeTotalJson });

util.makeXlsxFile(sheets, outputFile);
