const path = require('path');
const fs = require('fs');
const nodeXlsx = require('node-xlsx');
const xlsx = require('xlsx');

// 这个顺序最好和在线词条文档保持一致，方便复制，在线文档地址见 README.md
const lngs = ['zh', 'en', 'zh-TW', 'ko', 'ja'];

function deleteFolderRecursive(folderPath) {
  if (!folderPath) {
    throw 'delete folder error, no folderPath';
  }
  if (fs.existsSync(folderPath)) {
    fs.readdirSync(folderPath).forEach((file) => {
      const curPath = path.join(folderPath, file);
      if (fs.lstatSync(curPath).isDirectory()) {
        deleteFolderRecursive(curPath);
      } else {
        fs.unlinkSync(curPath);
      }
    });
    fs.rmdirSync(folderPath);
  }
}

function parseSheet(sheet, outputPath, options) {
  if (!sheet) {
    throw 'parseSheet error, no sheet';
  }
  if (!outputPath) {
    throw 'parseSheet error, no outputPath';
  }
  const itemList = sheet.data;
  const header = itemList.shift(); // ['key', 'zh', ...]
  const len = itemList.length;
  console.log('sheet', sheet.name);
  console.log('header', header.join('|'));
  console.log('item length', len);

  const sheetConfigMap = options?.sheetConfigMap || {};
  const sheetConfig = sheetConfigMap[sheet.name] || {};

  const key2Index = {};
  const outData = {};
  header.forEach((lng, index) => {
    if (index === 0) {
      return;
    }
    outData[lng] = {};
  });

  for (let i = 0; i < len; i++) {
    const item = itemList[i];
    const key = item[0];
    if (key2Index[key] !== undefined) {
      // key 重复
      const existedIndex = key2Index[key];
      // + 2 是因为xlsx的行号从1开始，以及还有一行header
      console.warn(`sheet ${sheet.name} item warn: key existed`, existedIndex + 2, itemList[existedIndex], i + 2, item);
      continue;
    }
    // 记录key对应的index
    key2Index[key] = i;
    header.forEach((lng, colIndex) => {
      if (colIndex === 0) {
        return;
      }
      let value = item[colIndex];
      value = value !== undefined ? String(value) : '';
      if (!value) {
        // value 为空
        // + 2 是因为xlsx的行号从1开始，以及还有一行header
        console.warn(`sheet ${sheet.name} item warn: value empty`, i + 2, key, lng, item[colIndex]);
        // 降级
        const bakLng = /^zh/.test(lng) ? 'zh' : 'en';
        if (outData[bakLng][key]) {
          value = outData[bakLng][key];
        }
      }
      outData[lng][key] = value;
    });
  }

  const folderName = sheet.name || 'Sheet';
  console.log('clear folder', folderName);
  const folderPath = path.join(outputPath, folderName);
  deleteFolderRecursive(folderPath);
  fs.mkdirSync(folderPath);

  if (sheetConfig.singleJson) {
    const filename = `${folderName}/${folderName}.json`;
    console.log(`writing ${filename} ...`);
    fs.writeFile(
      path.join(outputPath, filename),
      // eslint-disable-next-line
      JSON.stringify(outData, null, 2) + '\n', // i18next 扫描出来的文件结尾有换行，这里保持一致
      'utf-8',
      (err) => {
        if (!err) {
          console.log(`write ${filename} success`);
        } else {
          console.log(`write ${filename} error`, err);
        }
      },
    );
  } else {
    header.forEach((lng, index) => {
      if (index === 0) {
        return;
      }
      const filename = `${folderName}/${lng}.json`;
      console.log(`writing ${filename} ...`);
      fs.writeFile(
        path.join(outputPath, filename),
        // eslint-disable-next-line
        JSON.stringify(outData[lng], null, 2) + '\n', // i18next 扫描出来的文件结尾有换行，这里保持一致
        'utf-8',
        (err) => {
          if (!err) {
            console.log(`write ${filename} success`);
          } else {
            console.log(`write ${filename} error`, err);
          }
        },
      );
    });
  }
}

/**
 * 将 xlsx 文件转为多个 json 文件
 * @param {string} xlsxFile 要解析的文件
 * @param {string} outputPath 输出目录
 * @param {object} options { sheetConfigMap?: { [sheetName: string]: { singleJson?: boolean } } }
 */
function parseXlsxFile(xlsxFile, outputPath, options) {
  if (!xlsxFile || !fs.existsSync(xlsxFile)) {
    throw 'parseXlsxFile error, no xlsxFile';
  }
  if (!outputPath) {
    throw 'parseXlsxFile error, no outputPath';
  }
  if (!fs.existsSync(outputPath)) {
    fs.mkdirSync(outputPath);
  }
  console.log('parse xlsx file', xlsxFile, outputPath);
  const sheetList = nodeXlsx.parse(xlsxFile);
  sheetList.forEach(sheet => parseSheet(sheet, outputPath, options));
}

function bookAppendSheet(book, sheetName, lngJson) {
  console.log('add xlsx sheet', sheetName);
  const sheetJson = Object.keys(lngJson.zh).map((key) => {
    const item = { key };
    for (const lng of lngs) {
      item[lng] = lngJson[lng][key] || '';
    }
    return item;
  });
  console.log('item length', sheetJson.length);
  xlsx.utils.book_append_sheet(book, xlsx.utils.json_to_sheet(sheetJson), sheetName);
}

/**
 * 将多个 json 文件转为 xlsx 文件
 * @param {array} sheetList { name: string; data: { [lng: sring]: { [key: string]: string } } }[]
 * @param {string} outputFile 输出文件
 */
function makeXlsxFile(sheetList, outputFile) {
  if (!sheetList || !sheetList.filter(sheet => !!sheet.name).length) {
    throw 'makeXlsxFile error, no valid sheet';
  }
  if (!outputFile) {
    throw 'makeXlsxFile error, no outputFile';
  }
  console.log('make xlsx file', outputFile);
  console.log('clear old file');
  if (fs.existsSync(outputFile)) {
    fs.unlinkSync(outputFile);
  }
  const book = xlsx.utils.book_new();
  sheetList.forEach(sheet => bookAppendSheet(book, sheet.name, sheet.data));
  console.log('write new file');
  xlsx.writeFile(book, outputFile);
}

module.exports = {
  lngs,
  parseXlsxFile,
  makeXlsxFile,
};
