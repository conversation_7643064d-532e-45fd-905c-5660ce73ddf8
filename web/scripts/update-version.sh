#!/bin/bash
# 文件名：update_version.sh
# 功能：批量更新版本号（支持version.js和两个package.json）

# 参数校验
if [ -z "$1" ]; then
  echo "ERROR: 必须传入新版本号参数"
  echo "用法: $0 <new_version>"
  exit 1
fi
new_version=$1

# 获取当前版本号（从version.js提取）
old_version=$(grep "const mainVersion" web/version.js | sed -E "s/.*'(.*)'.*/\1/")

# 替换版本号（跨平台兼容写法）
# 1. 更新version.js
sed -i '' "s/const mainVersion = '${old_version}';/const mainVersion = '${new_version}';/" web/version.js

# 2. 更新sdk/package.json
sed -i '' "s/\"version\": \"${old_version}\"/\"version\": \"${new_version}\"/" web/sdk/package.json

# 3. 更新ui/package.json 
sed -i '' "s/\"version\": \"${old_version}\"/\"version\": \"${new_version}\"/" web/ui/package.json

echo "版本号已从 ${old_version} 更新为 ${new_version}"