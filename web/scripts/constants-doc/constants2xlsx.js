const path = require('path');
const fs = require('fs');
const xlsx = require('xlsx');

const xlsxFilename = 'constants.xlsx';
const outputPath = path.resolve(__dirname, 'output');

const constantsPath = path.resolve(__dirname, '../../sdk/src/constants');
function parseFile(filename) {
  const filepath = path.resolve(constantsPath, filename);
  return new Promise((resolve, reject) => {
    fs.readFile(filepath, 'utf8', function (err, content) {
      if (err) {
        reject(`${filename} read error`);
        return;
      }
      const res = content.match(/export\s+enum\s+(\w+)\s+\{([\s\S]*)\}/);
      if (!res) {
        reject(`${filename} has no enum`);
        return;
      }
      const enumName = res[1];
      const enumContent = res[2];
      // console.log(`${filename} enumName`, enumName);
  
      // 简单点，多行注释的才导出
      const items = enumContent.match(/\/\*((.|\r\n|\n)*?)\*\/\s+(\w+)\s+=\s+\'([\w@-]+)\'/g);
      // console.log(`${filename} items`, items);
      if (!items) {
        reject(`${filename} has no enum item`);
        return;
      }
      const enumItems = [];
      items.forEach((item, index) => {
        const itemRes = item.match(/\/\*((.|\r\n|\n)*?)\*\/\s+(\w+)\s+=\s+\'([\w@-]+)\'/);
        const itemComment = itemRes[1].replace(/^[\s\*]*/, '').replace(/[\s\*]*$/, '');
        const itemName = itemRes[3];
        const itemValue = itemRes[4];
        // console.log(`${filename} item ${index}`, itemName, itemValue, itemComment);
        enumItems.push({
          name: itemName,
          value: itemValue,
          comment: itemComment,
        });
      });
      console.log(`${filename} parse end, enumName ${enumName}, enumItems.length ${enumItems.length}`);
      resolve({
        enumName,
        enumItems,
      });
    });
  });
}

const fileList = [
  'main-event.ts',
  'main-state.ts',
];
Promise.all(fileList.map(filename => parseFile(filename)))
  .then((resList) => {
    console.log('clear old file', xlsxFilename);
    const outputFile = path.resolve(outputPath, xlsxFilename);
    if (fs.existsSync(outputFile)) {
      fs.unlinkSync(outputFile);
    }

    console.log('make xlsx book');
    const book = xlsx.utils.book_new();
    resList.forEach(({ enumName, enumItems }) => {
      xlsx.utils.book_append_sheet(book, xlsx.utils.json_to_sheet(enumItems), enumName);
    });

    console.log('write new file', xlsxFilename);
    if (!fs.existsSync(outputPath)) {
      fs.mkdirSync(outputPath);
    }
    xlsx.writeFile(book, outputFile);
  })
  .catch((err) => {
    console.error('constants2xlsx error', err);
  });
