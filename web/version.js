/**
 * sdk 和 ui 共用
 * mainVersion格式 {major}.{minor}.{patch} | {major}.{minor}.{patch}-{preRelease}，格式说明详见 https://semver.org/
 * eg:
 * 1.8.0-beta
 * 1.8.0-rc.1
 * 1.8.0
 */
const mainVersion = '1.9.0';
const [_total, coreVersion, _tail, preRelease] = mainVersion.match(/^(\d+\.\d+\.\d+)(\-([\w-.]+))?$/);

module.exports = {
  pkgVersion: coreVersion,
  coreVersion,
  preRelease,
  mainVersion,
  recordPageVersion: 'latest', // 后台拼录制url用，录制url格式： https://record.qcloudclass.com/{recordPageVersion}/class.html
};
