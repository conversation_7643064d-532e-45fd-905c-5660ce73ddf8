import { TMainState } from '../../constants';
import { TMain } from '../tmain';

// 第一次运行的延迟时间
const FIRST_RUN_DELAY = 10000;

// 初始运行间隔
const INITIAL_RUN_INTERVAL = 5000;
// 失败延迟增量
const FAIL_DELAY_STEP = 2000;
// 最大运行间隔
const MAX_RUN_INTERVAL = 15000;

export const setIMFallbackHandler = (handler: () => Promise<void>) => {
  let timer: any = null;
  let abortFn: null | (() => void) = null;
  let failCount = 0;
  let running = false;

  // 运行间隔 = 初始运行间隔 + 失败次数 * 失败延迟增量；（不超过最大运行间隔）
  const getInterval = () => Math.min(MAX_RUN_INTERVAL, INITIAL_RUN_INTERVAL + FAIL_DELAY_STEP * failCount);

  const runHandler = () => {
    let timeoutTimer: any = null;

    let aborted = false;
    abortFn = () => {
      aborted = true;
    };

    TMain.instance.reportLog('im_fallback_handler', '');

    Promise.race([
      handler(),
      new Promise((_, reject) => {
        // 超时
        timeoutTimer = setTimeout(() => {
          reject(new Error('timeout'));
        }, getInterval());
      }),
    ])
      .then(() => {
        clearTimeout(timeoutTimer);
        if (!aborted) {
          failCount = 0;
          timer = setTimeout(runHandler, getInterval());
        }
      })
      .catch(() => {
        clearTimeout(timeoutTimer);
        if (!aborted) {
          failCount += 1;
          timer = setTimeout(runHandler, getInterval());
        }
      });
  };

  const startFallbackHandleTimer = () => {
    stopFallbackHandleTimer();
    timer = setTimeout(runHandler, FIRST_RUN_DELAY);
  };

  const stopFallbackHandleTimer = () => {
    if (abortFn) {
      abortFn();
      abortFn = null;
    }

    if (timer) {
      clearTimeout(timer);
      timer = null;
    }
  };

  const handleStateChange = () => {
    const joinedClass = TMain.instance.getState(TMainState.Joined_Class);
    const networkBroken = TMain.instance.getState(TMainState.Network_Broken);
    const imCmdReady = TMain.instance.getState(TMainState.IM_Cmd_Ready);

    const expectRunning = !!joinedClass && !networkBroken && !imCmdReady;

    if (expectRunning !== running) {
      if (expectRunning) {
        startFallbackHandleTimer();
        running = true;
        TMain.instance.reportLog('im_fallback_enable', '');
      } else {
        stopFallbackHandleTimer();
        running = false;
        TMain.instance.reportLog('im_fallback_disable', '');
      }
    }
  };

  TMain.instance.subscribeState(TMainState.IM_Cmd_Ready, handleStateChange);
  TMain.instance.subscribeState(TMainState.Joined_Class, handleStateChange);
  TMain.instance.subscribeState(TMainState.Network_Broken, handleStateChange);
};
