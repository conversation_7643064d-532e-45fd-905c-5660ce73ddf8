import i18next from 'i18next';
import { TEvent } from '../../base/tevent';
import { TLoggerModule } from '../../base/tmodule';
import { TSession } from '../tsession';
import {
  TLiveBase,
  TLiveAction,
  TLiveEvent,
} from './live_base';
import {
  TMain,
} from '../tmain';
import { TTrtcEvent } from '../trtc/ttrtc_base';


// @ts-ignore
const AudioContext = window.AudioContext || window.webkitAudioContext;
// Prior to version 66 Google Chrome only supported up to six audio contexts per tab at a time.
// to avoid running out of context limits, we use a shared context for all SoundMeters.
let context: any = null;

export class SoundMeter extends TLoggerModule {
  private readonly context_: any;
  private instant_: number;
  private slow_: number;
  private clip_: number;
  private readonly script_: any;
  private mic_: MediaStreamAudioSourceNode;
  constructor() {
    super();
    if (!context) {
      context = new AudioContext();
    }
    this.context_ = context;
    this.instant_ = 0.0;
    this.slow_ = 0.0;
    this.clip_ = 0.0;
    this.script_ = this.context_.createScriptProcessor(2048, 1, 1);
    this.script_.onaudioprocess = (event: any) => {
      const input = event.inputBuffer.getChannelData(0);
      let i;
      let sum = 0.0;
      let clipcount = 0;
      for (i = 0; i < input.length; ++i) {
        sum += input[i] * input[i];
        if (Math.abs(input[i]) > 0.99) {
          clipcount += 1;
        }
      }
      this.instant_ = Math.sqrt(sum / input.length);
      this.slow_ = 0.95 * this.slow_ + 0.05 * this.instant_;
      this.clip_ = clipcount / input.length;
    };
  }

  connectToSource(track: MediaStreamTrack) {
    try {
      const stream = new MediaStream();
      stream.addTrack(track);
      this.mic_ = this.context_.createMediaStreamSource(stream);
      this.mic_.connect(this.script_);
      // necessary to make sample run, but should not be.
      this.script_.connect(this.context_.destination);
    } catch (error) {
      this._error('SoundMeter', `soundMeter connectToSource error: ${error}`);
    }
  }

  stop() {
    this.mic_.disconnect();
    this.script_.disconnect();
  }

  /**
   * AudioContext 受浏览器自动播放策略限制，当恢复播放时
   * 需要 AudioContext.resume 或者重新创建 AudioContext
   * 否则会导致，音频恢复播放后，有声音，但是因 AudioContext 不可用，
   * 导致 getAudioLevel 值为0
   */
  resume() {
    if (this.context_) {
      this.context_.resume();
    }
  }

  getVolume() {
    return this.instant_ * 100;
  }
}

export class TLiveWeb extends TLiveBase {
  // TcPlayer Lite 文档： https://cloud.tencent.com/document/product/881/20207#.E5.AE.9E.E4.BE.8B.E6.96.B9.E6.B3.95.E5.88.97.E8.A1.A8
  private static _permissionStreams = new Set();

  private static innerGetStreamId(url: string) {
    return url.split(/[/?]/)[4]?.replace('.', '_');
  }

  // 授权音视频
  private static _permissionAV(player: any, playErr: any) {
    TLiveWeb._permissionStreams.add(player);
    return () => {
      const isEmpty = TLiveWeb._permissionStreams.size === 0;
      if (isEmpty) {
        return;
      }
      console.log('[TLiveWeb] _permissionAV playErr', TLiveWeb._permissionStreams.size, playErr);
      TMain.instance.reportLog('liveAuthorization', '[TLiveWeb] showMessageBox');
      TLiveWeb._permissionStreams.forEach((stream: any) => {
        // @ts-ignore
        if (TSession.instance.isWeb() && window['WeixinJSBridge']) { // 微信内嵌环境下特殊处理
          try {
            // @ts-ignore
            window['WeixinJSBridge'].invoke('getNetworkType', {}, () => {
              stream.play();
            });
          } catch (e: any) {
            console.error('doWeixinPlay error', e.message);
            stream.play();
          }
        } else {
          if (stream.paused) {
            stream.play();
          }
        }
      });
      TLiveWeb._permissionStreams.clear();
      TEvent.instance.notify(
        TTrtcEvent.AUTOPLAY_CONFIRM,
        'all',
        false,
      );
    };
  }

  private TCPlayer: any = (window as any).TCPlayer;
  private playerMap = new Map();
  private soundMeterInterval: any;
  private volumeInterval: number;
  private _lastReportStatTime: number;

  public constructor() {
    super();
  }

  /**
   * 开始播放
   * @param dom 播放视频的Dom
   * @param url 播放的URL
   * @param fullMode 是否全屏模式
   * @param muteMode 是否静音模式
   * @Test TCIC.SDK.instance.startPlay(temp1, 'webrtc://liveplay16408.qcloudclass.com/live/1400313729_106478822_mix');
   */
  // eslint-disable-next-line max-len
  public startPlay(dom: HTMLElement, url: string, fullMode: boolean, muteMode: boolean, playUserId: string, isSub: boolean): Promise<void> {
    const streamId = TLiveWeb.innerGetStreamId(url);
    const userId = TMain.instance.getUserId();
    if (url.startsWith('webrtc:')) {
      const ch = url.includes('?') ? '&' : '?';
      url += `${ch}uid=${userId}`;
    }
    this._info('startPlay', `domName: ${dom?.dataset?.domName}, url: ${url}, userId ${playUserId} hasStreamId ${this.playerMap.has(streamId)}`);
    dom.id = streamId;
    // url = 'webrtc://global-lebtest-play.myqcloud.com/live/lebtest?txSecret=f22a813b284137ed10d3259a7b5c224b&txTime=69f1eb8c';
    if (!this.playerMap.has(streamId)) {
      const options = {
        sources: [{
          src: url, // 播放地址
        }],
        autoplay: true,
        controls: false,
        muted: muteMode,
      };
      const videoDom = document.createElement('video');
      const vid = `tcplayer__video_${streamId}`;
      videoDom.setAttribute('playsinline', '');
      videoDom.setAttribute('webkit-playsinline', '');
      videoDom.setAttribute('id', vid);
      videoDom.setAttribute('style', 'width: 100%; height: 100%;');
      dom.append(videoDom);
      const player = this.TCPlayer(vid, options); // 第一个参数需video或者video的id
      // 自动播放被阻止统一这里处理
      player.on('blocked', (err: any) => {
        const callback = TLiveWeb._permissionAV(player, err.message);
        TEvent.instance.notify(
          TTrtcEvent.AUTOPLAY_FAILED,
          {
            userId: playUserId,
            resume: callback,
            isSub,
          },
          false,
        );
      });
      player.on('error', (err: any) => {
        this._error('LivePlayerError', `Common Error, streamId: ${streamId}, errorInfo: ${err.toString()}, url: ${url}`);
      });
      player.on('webrtcstats', (msg: any) => {
        this.reportWebRTCStat(msg);
      });
      player.volume(0.5); // 0-1 可选音量
      player.streamId = streamId;
      player.soundMeterInterval = null;
      player.soundMeter = null;
      player.videoId = vid;
      if (TSession.instance.isWeb()) {
        // @ts-ignore
        if (window['WeixinJSBridge']) { // 微信内嵌环境下特殊处理
          try {
            // @ts-ignore
            window['WeixinJSBridge'].invoke('getNetworkType', {}, () => {
              // 微信里直接操作video element
              const videoEl = player.children_[0];
              videoEl?.play().catch((err: any) => {
                if (err?.name === 'NotAllowedError') {
                  const callback = TLiveWeb._permissionAV(player, err);
                  TEvent.instance.notify(
                    TTrtcEvent.AUTOPLAY_FAILED,
                    {
                      userId: playUserId,
                      resume: callback,
                      isSub,
                    },
                    false,
                  );
                }
                this._error('LivePlayerError', `Wechat web play error, streamId: ${streamId}, errorInfo: ${err.toString()}, url: ${url}`);
              });
              // player.play();
            });
          } catch (err: any) {
            if (err?.name === 'NotAllowedError') {
              const callback = TLiveWeb._permissionAV(player, err);
              TEvent.instance.notify(
                TTrtcEvent.AUTOPLAY_FAILED,
                {
                  userId: playUserId,
                  resume: callback,
                  isSub,
                },
                false,
              );
            }
          }
        } else {
          try {
            player.play();
          } catch (err) {
            this._error('LivePlayerError', `web play error, streamId: ${streamId}, errorInfo: ${err.toString()}, url: ${url}`);
          }
        }
      }

      this.playerMap.set(streamId, player);
    }
    return this.newPromise(TLiveAction.LiveStartPlay, streamId, false, 0);
  }


  /**
   * 停止播放
   * @param url 停止播放的URL
   */
  public stopPlay(url: string): Promise<void> {
    const streamId = TLiveWeb.innerGetStreamId(url);
    this._info('stopPlay', `url: ${url}, hasStreamId ${this.playerMap.has(streamId)}`);
    if (this.playerMap.has(streamId)) {
      const player = this.playerMap.get(streamId);
      if (player.sto) {
        clearTimeout(player.sto);
      }
      player.dispose();
      this.playerMap.delete(streamId);
    }
    return Promise.resolve(undefined);
  }

  // 上报webrtc stat
  public reportWebRTCStat(msg: any) {
    if (!this._lastReportStatTime) {
      this._lastReportStatTime = +new Date();
    }
    const now = +new Date();
    if (now - this._lastReportStatTime > 10000) {
      this._lastReportStatTime = now;
      if (msg && msg.data) {
        try {
          const audio = msg.data.audio;
          const json = JSON.stringify(msg.data);
          // 音频level==0或者bitrate==0说明有问题
          if (audio && audio.audioLevel === 0) {
            this._error('LivePlayerAudioLevel', json);
          }
          if (audio && audio.bitrate === 0) {
            this._error('LivePlayerAudioBitrate', json);
          }
          this._info('LivePlayerStat', json);
        } catch (e) {
          console.error(e);
        }
      }
    }
  }

  public enableVolumeEvaluation(interval: number): Promise<void> {
    this.volumeInterval = interval;
    this.playerMap.forEach((player) => {
      if (interval === 0) {
        cancelAnimationFrame(player.soundMeterInterval);
      } else {
        this.requestAnimationFrame(player, () => {
          if (player.soundMeter) {
            const volume = player.soundMeter.getVolume();
            TEvent.instance.notify(TLiveEvent.Volume_Update, {
              streamId: player.streamId,
              volume,
            }, false);
          }
        });
      }
    });
    return Promise.resolve(undefined);
  }

  public startSoundMeter(player: any) {
    if (!player.soundMeter && player.video && player.video.webrtc && player.video.webrtc.stream) {
      const audioTracks = player.video.webrtc.stream.getAudioTracks();
      if (audioTracks.length > 0) {
        // lazy init to preserve resource
        player.soundMeter = new SoundMeter();
        player.soundMeter.connectToSource(audioTracks[0]);
      } else {
        console.error('no track');
      }
    }
    return player.soundMeter;
  }

  public stopSoundMeter(player: any) {
    if (player.soundMeter) {
      player.soundMeter.stop();
      player.soundMeter = null;
    }
  }

  public resumeSoundMeter(player: any) {
    if (player.soundMeter) {
      player.soundMeter.resume();
    }
  }

  public requestAnimationFrame(player: any, func: Function) {
    const that = this;
    function animeLoop() {
      func();
      if (that.volumeInterval) {
        setTimeout(() => {
          player.soundMeterInterval = requestAnimationFrame(animeLoop);
        }, that.volumeInterval);
      }
    }
    animeLoop();
  }

  protected _getClassName() {
    return 'TLiveWeb';
  }
}
