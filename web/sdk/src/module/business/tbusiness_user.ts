import { TModel } from '../../base/tmodel';
import { TModule } from '../../base/tmodule';
import { TPlatform, TDeviceStatus } from '../../constants';
import { TBusiness } from './tbusiness';
import { TScreenState } from './tbusiness_member';

/**
 * 权限信息
 */
export class TPermissionInfo extends TModel {
  /**
   * 用户 ID
   */
  public userId = '';

  /**
   * 用户昵称
   */
  public userName = '';

  /**
   * 是否上台
   */
  public stage = false;

  /**
   * 摄像头权限
   */
  public camera = false;

  /**
   * 麦克风权限
   */
  public mic = false;

  /**
   * 屏幕共享权限（该权限只有在调用授权接口明确授权给指定用户后才会有，包括老师也是如此）
   * 0 没有权限；1 分享屏幕或窗口权限，2 分享播片权限，3 分享辅助摄像头权限
   */
  public screen = 0;

  /**
   * 白板权限
   */
  public board = false;

  /**
   * 麦克风状态
   */
  public micState = TDeviceStatus.Unknown;

  /**
   * 摄像头状态
   */
  public cameraState = TDeviceStatus.Unknown;

  /**
   * 屏幕共享状态
   */
  public screenState = TScreenState.Stopped;

  /**
   * 页面可见状态
   */
  public visibleState = 0;

  /**
   * 当前平台
   */
  public platform = TPlatform.Unknown;

  /**
   * 进课堂时间
   */
  public lastEnterTime?: number;
  /**
   * 操作权限用户的id
   */
  public operatorId?: string = '';
  /**
   * @ignore
   */
  public serialize() {
    // TStore模块中合并成员信息时会用到，不确定的值不要添加进来
    return {
      userId: this.userId,
      stage: this.stage,
      camera: this.camera,
      mic: this.mic,
      screen: this.screen,
      board: this.board,
      micState: this.micState,
      cameraState: this.cameraState,
      screenState: this.screenState,
      platform: this.platform,
      operatorId: this.operatorId,
    };
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    this.userId = json.user_id;
    this.userName = json.user_name || json.name;  // Permission列表里该字段为name，这里做下特别兼容
    this.stage = !!json.stage;
    this.camera = !!json.camera;
    this.mic = !!json.mic;
    this.screen = json.screen;
    this.board = !!json.board;
    this.micState = json.mic_state;
    this.cameraState = json.camera_state;
    this.screenState = json.screen_state;
    this.platform = json.platform;
    this.visibleState = json.visible_state;
    this.operatorId = json.operator_id;
    if (json.hasOwnProperty('last_enter_time')) {
      this.lastEnterTime = json.last_enter_time;
    }
  }
}


/**
 * 举手信息
 */
export interface THandupInfo {
/**
   * 是否举手
   */
  handUp?: boolean;
  /**
  * 举手是否处于活跃状态（每次收到举手请求后3秒内为活跃状态）
  */
  handUpActive?: boolean;
  /**
  * 课堂举手次数
  */
  handUpTimes?: number;
}

/**
 * 用户信息
 * @param {string} userId 用户 ID
 * @param {string} nickname 用户昵称
 * @param {string} userName 用户名
 * @param {string} avatar 头像地址
 * @param {number} schoolId 学校 ID
 * @param {boolean} onlineStatus 是否在线
 */
export class TUserInfo extends TModel {
  public userId = '';
  public userName = '';
  public nickname = '';
  public avatar = '';
  public schoolId = 0;
  public role = 0;
  public onlineStatus = true;

  /**
   * @ignore
   */
  public deserialize(json: any) {
    this.userId = json.user_id ?? json.userId;
    this.schoolId = json.school_id;
    this.userName = json.userName ?? json.nickname;
    this.nickname = json.nickname ?? json.userName;
    this.avatar = json.avatar;
    this.role = json.role;
    this.onlineStatus = json.onlineStatus ?? true;
  }

  /**
   * @ignore
   */
  public serialize(): any {
    throw new Error('Method not implemented.');
  }
}

export class TGetUserListResult extends TModel {
  public users: TUserInfo[] = [];

  /**
   * @ignore
   */
  public serialize() {
    throw new Error('Method not implemented.');
  }

  /**
   * @ignore
   */
  public deserialize(json: any) {
    const usersJson = json.users;
    if (usersJson) {
      usersJson.forEach((itemJson: any) => {
        const user = new TUserInfo();
        user.deserialize(itemJson);
        this.users.push(user);
      });
    }
  }
}

export class TBusinessUser extends TModule {
  public static get instance(): TBusinessUser {
    return this.getInstance();
  }

  /**
   * 获取用户信息（仅限本人）
   * @param {string}                  token       token
   */
  public getUserInfo(token: string): Promise<TUserInfo> {
    const param = {};
    const userInfo = new TUserInfo();
    const url = 'user/info';
    return TBusiness.instance.request(url, param, token, userInfo);
  }

  /**
   * 获取多个用户信息
   * @param {string[]}                userIds     用户列表
   * @param {string}                  token       token
   */
  public getUserList(userIds: string[], token: string): Promise<TGetUserListResult> {
    if (userIds.length === 0) {
      return Promise.resolve(new TGetUserListResult());
    }
    const param = {
      user_ids: userIds,
    };
    const result = new TGetUserListResult();
    const url = 'user/list';
    return TBusiness.instance.request(url, param, token, result);
  }

  /**
   * 修改用户信息
   * @param {number}                  userInfo        用户信息
   * @param {string}                  token           token
   */
  public modifyUserProfile(userInfo: TUserInfo, token: string): Promise<null> {
    const param: any = {
      nickname: userInfo.nickname,
      avatar: userInfo.avatar,
    };
    const url = 'user/modifyProfile';
    return TBusiness.instance.request(url, param, token, null);
  }
}


