// import { Action } from 'cos-js-sdk-v5';
// 小程序中不能使用这个db
// #!if MP_ENV === 'false'
import TCosFile from './tcosfile';
import { TIndexDb } from './tindexeddb';
// #!endif
import { TRequest } from './trequest';
import { TSession } from './tsession';
import { TUtil } from './tutil_inner';
import { TDeviceStatus, TMainEvent, TMainState } from '../constants';
import { TState } from './tstate';
import { TEvent } from '../base/tevent';

declare let PRODUCTION: boolean;
declare let MP_ENV: boolean;
class TDefaultConfig {
  public configUrl = 'https://log.qcloudtiw.com/config/tiwlogger-server-config';
  public reportInterval = 2;
  public reportLevel: TLevel = TLevel.INFO;
  public logLevel: TLevel = TLevel.INFO;
  public logMaxSize = 300;
  public reportUrl = 'https://report-log-lv1.api.qcloud.com/nv.cgi';
  public cosSignUrl = 'https://api.my-imcloud.com/v4/ilvb_edu/cos_token';
  public configCheckInterval = 360;
  public logExpiredTime = 7;
  public blackActions: string[] = [];
}

class TUserConfig {
  public userId = '';
  public reportLevel: TLevel = TLevel.INFO;
  public logLevel: TLevel = TLevel.INFO;
  public uploadLog = false;
  public logFiles: string[] = [];
}

class TAppConfig {
  public sdkAppId = 0;
  public reportLevel: TLevel = TLevel.INFO;
  public logLevel: TLevel = TLevel.INFO;
  public blackActions: string[] = [];
  public userConfigs: Map<string, TUserConfig> = new Map<string, TUserConfig>();
}


class TServerConfig {
  private static _instance: any = null;

  public static get instance(): TServerConfig {
    if (TServerConfig._instance === null) {
      TServerConfig._instance = new TServerConfig();
    }
    return TServerConfig._instance;
  }

  public defaultConfig: TDefaultConfig = new TDefaultConfig();
  public _appConfigs: Map<number, TAppConfig> = new Map<number, TAppConfig>();

  private _looper: any = null;

  constructor() {
    // 主动获取一次服务器配置
    this._getServerConfig();
    this._startLooper();
  }

  public destroy() {
    this._stopLooper();
  }


  public needLog(sdkAppId: number, userId: string, level: TLevel): boolean {
    if (!PRODUCTION) {
      return true;
    }
    const appConfig = this._appConfigs.get(sdkAppId);
    if (!appConfig) {
      return level >= this.defaultConfig.logLevel;
    }
    const userConfig = appConfig.userConfigs.get(userId);
    if (!userConfig) {
      // 不存在用户配置，以应用配置为主
      return level >= appConfig.logLevel;
    }
    return level >= userConfig.logLevel;
  }

  public needReport(sdkAppId: number, userId: string, level: TLevel, action: string): boolean {
    const appConfig = this._appConfigs.get(sdkAppId);
    const lowerAction = action.toLowerCase();
    if (!appConfig) {
      if (!this.defaultConfig.blackActions.find(item => item === lowerAction)) {
        return level >= this.defaultConfig.reportLevel;
      }
      // action在上报黑名单中
      return false;
    }
    if (!this.defaultConfig.blackActions.find(item => item === lowerAction)
      && !appConfig.blackActions.find(item => item === lowerAction)) {
      const userConfig = appConfig.userConfigs.get(userId);
      if (!userConfig) {
        // 不存在用户配置，以应用配置为主
        return level >= appConfig.reportLevel;
      }
      return level >= userConfig.reportLevel;
    }
    // action在上报黑名单中
    return false;
  }

  private _resetConfig(response: string) {
    const json = JSON.parse(response);

    const defaultJson = json.default_config;
    const defaultConfig = new TDefaultConfig();
    defaultConfig.reportLevel = defaultJson.report_level;
    defaultConfig.logLevel = defaultJson.log_level;
    defaultConfig.logMaxSize = defaultJson.log_max_size;
    defaultConfig.logExpiredTime = defaultJson.log_expired_time;
    defaultConfig.reportUrl = defaultJson.report_url;
    defaultConfig.cosSignUrl = defaultJson.cos_sign_url;
    defaultConfig.configCheckInterval = defaultJson.config_check_interval;
    defaultConfig.reportInterval = defaultJson.report_interval;
    const actionsJson = defaultJson.black_actions;
    actionsJson.forEach((action: string) => {
      defaultConfig.blackActions.push(action.toLowerCase());
    });
    this.defaultConfig = defaultConfig;

    const appsJson = json.app_configs;
    appsJson.forEach((app: any) => {
      const appConfig = new TAppConfig();
      appConfig.sdkAppId = app.sdk_app_id;
      appConfig.reportLevel = app.report_level;
      appConfig.logLevel = app.log_level;
      const actionsJson = app.black_actions;
      actionsJson.forEach((action: string) => {
        appConfig.blackActions.push(action.toLowerCase());
      });
      const usersJson = app.user_configs;
      usersJson.forEach((user: any) => {
        const userConfig = new TUserConfig();
        userConfig.userId = user.user_id;
        userConfig.logLevel = user.log_level;
        userConfig.reportLevel = user.report_level;
        userConfig.uploadLog = user.upload_log;
        const filesJson = user.log_files;
        filesJson.forEach((file: string) => {
          userConfig.logFiles.push(file);
        });
        appConfig.userConfigs.set(userConfig.userId, userConfig);
      });
      this._appConfigs.delete(appConfig.sdkAppId);
      this._appConfigs.set(appConfig.sdkAppId, appConfig);
    });
  }

  private _getServerConfig() {
    TRequest.instance.httpRequest('GET', this.defaultConfig.configUrl)
      .then((resp) => {
        this._resetConfig(resp.data);
      });
  }

  private _startLooper() {
    this._looper = setInterval(() => {
      this._getServerConfig();
    }, this.defaultConfig.configCheckInterval * 1000);
  }

  private _stopLooper() {
    clearInterval(this._looper);
  }
}

enum TNetType {
  NONE = 1,
  G2,
  G3,
  G4,
  G5,
  WIFI,
}


class TReportParamExt {
  private static _netType2String(type: TNetType): string {
    switch (type) {
      case TNetType.NONE:
        return 'none';
      case TNetType.G2:
        return '2g';
      case TNetType.G3:
        return '3g';
      case TNetType.G4:
        return '4g';
      case TNetType.G5:
        return '5g';
      case TNetType.WIFI:
      default:
        return 'wifi';
    }
  }

  private static _level2String(level: TLevel): string {
    switch (level) {
      case TLevel.DEBUG:
        return 'debug';
      case TLevel.INFO:
        return 'info';
      case TLevel.WARN:
        return 'warn';
      case TLevel.ERROR:
        return 'error';
      default:
        return 'error';
    }
  }

  // 设备信息
  public devPlatform = '';  //  平台
  public devType = '';  //  设备类型
  public devSysVersion = '';  // 系统版本
  public devAndroidSdkVersion = ''; // android SDK  版本
  public devUserAgent = ''; // 浏览器agent
  // 应用信息
  public appModule = '';  // 模块
  public appNativeSdkVersion = '';  //  optional : 移动端native TCICSDK 版本
  public appWebSdkVersion = ''; // H5 TCICSDK 版本
  public appWebUIVersion = ''; // H5 TCICUI 版本
  public appBusiness = '';  // 业务
  public appSdkAppId = 0;  // SDK AppID
  public appNewEnterId = 0; // 学校ID
  public schoolId = 0; // 真实的从后台取的学校ID
  public appRoomId = 0; // 房间号
  public appUserId = '';  // 当前用户Id
  public appUserRole = '';  // 当前用户角色
  public appLanguage = ''; // 上报当前语言
  // 状态信息
  public statsuAppCpu = 0;  // optional : app cpu
  public statusSysCpu = 0;  // optional : sys cpu
  public statusAppMem = 0;  // optional : app mem
  public statusAvailableMem = 0; // optional : 可用 mem
  public statusCamera = TDeviceStatus.Open; // optional : cam状态
  public statusMic = TDeviceStatus.Open; // optional : mic状态
  // 网络信息
  public netType: TNetType = TNetType.NONE; // 网络类型
  public netBandwidth = 0;  // optional : 带宽
  public netRtt = 0;  // optional : rtt 时延
  public networkQuality = 1; // optional : 网络质量
  // 错误信息
  public errorCode = 0; // 错误码
  public errorDesc = ''; // 错误描述
  public errorStack = ''; // 出错堆栈
  // 行文信息
  public actionName = ''; // 日志行为
  public actionParam = ''; // 日志行为参数
  public actionCost = 0; // actionName统计到的耗时
  public actionExt = '';  // actionName的扩展信息
  public actionType = ''; // actionName的类型

  // 其他信息
  public reportLevel: TLevel = TLevel.INFO; // 日志级别
  public reportTime = 0;  // 日志时间
  public reportSessionId = '';  // 会话ID : 每次进房都会生成一次，可以看用户是否有重进
  public reportGlobalRandom = ''; // 设备唯一标识，可以看用户是否更换过设备
  public reportLifeId = TSession.instance.getLifeID();  // sessionId是传进来的，electron版reload之后没变，另外加个字段区分

  public Serialize(): string {
    const json = {
      dev_platform: this.devPlatform,
      dev_type: this.devType,
      dev_sys_version: this.devSysVersion,
      dev_android_sdk_version: this.devAndroidSdkVersion,
      dev_user_agent: this.devUserAgent,

      app_module: this.appModule,
      report_module: this.appModule,
      app_native_sdk_version: this.appNativeSdkVersion,
      app_web_sdk_version: this.appWebSdkVersion,
      app_web_ui_version: this.appWebUIVersion,
      app_business: this.appBusiness,
      app_sdkappid: this.appSdkAppId,
      app_new_enter_id: this.appNewEnterId,
      app_school_id: this.schoolId,
      app_room_id: this.appRoomId,
      app_user_id: this.appUserId,
      app_user_role: this.appUserRole,
      app_language: this.appLanguage,

      status_app_cpu: this.statsuAppCpu,
      status_sys_cpu: this.statusSysCpu,
      status_app_mem: this.statusAppMem,
      status_available_mem: this.statusAvailableMem,
      status_camera: this.statusCamera,
      status_mic: this.statusMic,

      net_type: TReportParamExt._netType2String(this.netType),
      net_bandwidth: this.netBandwidth,
      net_rtt: this.netRtt,
      net_quality: this.networkQuality,

      error_code: this.errorCode,
      error_desc: this.errorDesc,
      error_stack: this.errorStack,

      action_name: this.actionName,
      action_param: this.actionParam,
      action_cost: this.actionCost,
      action_ext: this.actionExt,
      // action类型
      action_type: this.actionType,

      report_level: TReportParamExt._level2String(this.reportLevel),
      report_time: this.reportTime,
      report_session_id: this.reportSessionId,
      report_global_random: this.reportGlobalRandom,
      report_life_id: this.reportLifeId,
    };
    return JSON.stringify(json);
  }
}

/**
 * 日志和上报级别
 */
export enum TLevel {
  DEBUG = 1,
  INFO,
  WARN,
  ERROR
}

/**
 * 模块配置
 */
export class TLoggerConfig {
  // native sdk 版本号
  public nativeSdkVersion = '';
  // web sdk 版本号
  public webSdkVersion = '';
  // web ui 版本号
  public webUIVersion = '';
  // 业务标识，作为索引日志的唯一标识，不同业务请保证唯一性
  public business = 'tcic_sdk';
  // 学校 ID
  public schoolId = 0;
  // 真实的学校 ID, 从后台取得
  public realSchoolId = 0;
  // 应用唯一标识
  public sdkAppId = 0;
  // 房间 ID
  public roomId = 0;
  // 用户 ID
  public userId = '';
  // 语言
  public language = '';
  // 会话 ID（内部字段）
  public sessionId = '';
  // 设备 ID（内部字段）
  public globalRandom = '';
}

export class TLoggerParam {
  public module?: string = '';
  public action?: string = '';
  public param?: string = '';
  public ext?: string = '';
  public code?: number = 0;
  public desc?: string = '';
  public stack?: string = '';
  public report?: boolean = true;
}

// 过滤一些不需要了解又一直频繁打印的控制台log，只是不打印到console，不影响上报
const excludeLogMap: { [key: string]: boolean } = {};
[
  'innerLog',
  'member/heartbeat',
  'tcic@statistics@network-quality',
  'tcic@trtc@network-quality',
  'tcic@trtc@network-statistis',
  'trtc_getTRTCStats',
  'classHeartBeat',
  'getTRTCStats',
  '_updateAllComponents@create',
  '_updateAllComponents@remove',
  '_updateAllComponents@update',
  'updateComponent',
].forEach(key => excludeLogMap[key] = true);


export enum ActionType {
  User_Event = 'User_Event', // 用户主动操作的行为
  User_State = 'User_State', // 用户状态
  Class_Main_Event = 'Class_Main_Event', // 课堂生命周期内各种事件
  TRTC_Event = 'TRTC_Event', // trtc相关
  TIM_Event = 'TIM_Event', // tim相关
  Board_Event = 'Board_Event', // 白板周期内各种事件
  UI_Event = 'UI_Event', // UI组件的事件
  Other = 'Other' // 其他
}
const actionTypeMap: { [key: string]: string } = {
  muteRemoteAudio: ActionType.User_Event,
  muteLocalAudio: ActionType.User_Event,
  stopLocalAudio: ActionType.User_Event,
  startLocalAudio: ActionType.User_Event,
  muteLocalVideo: ActionType.User_Event,
  muteRemoteVideo: ActionType.User_Event,
  stopLocalVideo: ActionType.User_Event,
  startLocalVideo: ActionType.User_Event,
  muteAll: ActionType.User_Event,
  muteVideoAll: ActionType.User_Event,
  muteAllRemoteAudio: ActionType.User_Event,
  stopRemoteVideo: ActionType.User_Event,
  muteAudio: ActionType.User_Event,
  startRemoteVideo: ActionType.User_Event,

  // 'tcic@trtc@audio-changed': ActionType.User_Event,
  // 'tcic@trtc@video-changed': ActionType.User_Event,

  msgBoxBtnClick: ActionType.User_Event,
  showToast: ActionType.User_Event,

  setMicVolume: ActionType.User_Event,
  start_class: ActionType.User_Event,
  'tcic@tmain@class-leave': ActionType.User_Event,
  end_class: ActionType.User_Event,
  switchCamera: ActionType.User_Event,
  switchMic: ActionType.User_Event,
  switchSpeaker: ActionType.User_Event,
  setSpeakerVolume: ActionType.User_Event,
  setMicrophoneVolume: ActionType.User_Event,

  setVirtualImg: ActionType.User_Event,
  setAvatar: ActionType.User_Event,
  setBeautyParam: ActionType.User_Event,
  sendGroupTextMessage: ActionType.User_Event,

  board: ActionType.Board_Event,
  CallBoardMethod: ActionType.Board_Event,
  boardTool: ActionType.Board_Event,
  BoardWarning: ActionType.Board_Event,
  grant_board: ActionType.Board_Event,
  revoke_board: ActionType.Board_Event,
  BoardWindowInit: ActionType.Board_Event,
  BoardViewInit: ActionType.Board_Event,
  BoardViewInitFinish: ActionType.Board_Event,
  invite_board: ActionType.Board_Event,
  BoardViewDidFinishLoad: ActionType.Board_Event,
  remove_board: ActionType.Board_Event,
  'tcic@tmain@board-init': ActionType.Board_Event,
  'tcic@tmain@board-ready': ActionType.Board_Event,
  'tcic@tmain@board-permission': ActionType.Board_Event,
  'tcic@tmain@board-create': ActionType.Board_Event,

  tim_sendMsg: ActionType.TIM_Event,
  tim_sendGroupCustomMessage: ActionType.TIM_Event,
  tim_net_state_change: ActionType.TIM_Event,
  tim_init: ActionType.TIM_Event,
  tim_joinGroup: ActionType.TIM_Event,
  tim_updateProfile: ActionType.TIM_Event,
  tim_login: ActionType.TIM_Event,
  tim_error: ActionType.TIM_Event,
  tim_trySendMsg: ActionType.TIM_Event,
  tim_sdk_ready: ActionType.TIM_Event,
  tim_sdk_not_ready: ActionType.TIM_Event,
  tim_logout: ActionType.TIM_Event,
  tim_sendGroupTextMessage: ActionType.TIM_Event,
  tim_sendGroupImgMessage: ActionType.TIM_Event,
  tim_destroy: ActionType.TIM_Event,
  'tcic@tim@set-quick-im-words': ActionType.TIM_Event,
  'tcic@tim@recv-custom-message': ActionType.TIM_Event,
  'tcic@tim@recv-message': ActionType.TIM_Event,
  'tcic@tim@revoked-message': ActionType.TIM_Event,
  'tcic@tim@send-success': ActionType.TIM_Event,
  'tcic@tim@revoked-custom-message': ActionType.TIM_Event,
  'tcic@tim@send-custom-success': ActionType.TIM_Event,

  TEventUpdateLayout: ActionType.UI_Event,
  TEventUpdateLayoutAfterJoinClass: ActionType.UI_Event,
  updateComponentLayout: ActionType.UI_Event,
  TEventAddVideoComponent: ActionType.UI_Event,
  TEventRemoveVideoComponent: ActionType.UI_Event,
  'tcicui@addUserRenderItem': ActionType.UI_Event,
  'tcicui@removeUserRenderItem': ActionType.UI_Event,
  'tcicui@registerRender': ActionType.UI_Event,
};

// 用户状态
const stateKeys = Object.keys(TMainState);
stateKeys.forEach((item) => {
  actionTypeMap[item] = ActionType.User_State;
});

// 课堂event
const eventKeys = Object.keys(TMainEvent);
eventKeys.forEach((item) => {
  actionTypeMap[item] = ActionType.Class_Main_Event;
});

const isTRTCEvent = function (eventName: string) {
  return eventName && (eventName.indexOf('trtc.') > -1 || eventName.indexOf('trtc@') > -1);
};

// 获取actionType
const getActionTypeFromActionName = (actionName: string) => {
  const type = actionTypeMap[actionName];
  if (!type) {
    if (isTRTCEvent(actionName)) {
      return ActionType.TRTC_Event;
    }
    return ActionType.Other;
  }
  return type;
};

/**
 * 日志上报模块
 */
export class TLogger {
  private static _instances: Map<string, TLogger> = new Map<string, TLogger>();
  private static _isRunning = false;
  private static _looper: any = null;
  private static _offlineLogLooper: any = null;
  private static _reportCache: string[] = [];
  // #!if MP_ENV === 'false'
  private static tdb: TIndexDb = new TIndexDb();
  // #!else
  // @ts-ignore
  private static tdb = null;
  // #!endif

  public static getInstance(key: string): TLogger {
    if (!TLogger._isRunning) {
      // 判断是否支持indexed
      if (TLogger.tdb?.isSupport()) {
        TLogger.tdb.openDB(); // 打开db
      }
      TLogger._isRunning = true;
      TLogger._startLooper();
      TLogger._startOfflineLogLooper();
      TLogger._deleteExpiredOfflineLog();
    }
    let instance = TLogger._instances.get(key);
    if (!instance) {
      instance = new TLogger(key);
      TLogger._instances.set(key, instance);
    }
    return instance;
  }
  public static destroy() {
    TLogger._stopOfflineLogLooper();
    TLogger._stopLooper();
    TLogger._isRunning = false;
  }

  public static _deleteExpiredOfflineLog() {
    const serverConfig = TServerConfig.instance;
    const defaultConfig = serverConfig.defaultConfig;
    const expiredDate = TUtil.dateToDateStr(Date.now() - defaultConfig.logExpiredTime * 24 * 60 * 60 * 1000);
    // 删除过期日志
    setTimeout(() => {
      console.log('删除过期日志');
      TLogger.tdb && TLogger.tdb.removeLog('created_at', expiredDate);
    }, 5000);
  }

  public static destroyInstance(key: string) {
    if (TLogger._instances.has(key)) {
      TLogger._instances.delete(key);
    }
  }

  /**
   * 立即上报所有日志
   */
  public static async flushReport() {
    try {
      const url = TServerConfig.instance.defaultConfig.reportUrl;
      const contents = TLogger._reportCache;
      TLogger._reportCache = [];
      if (contents.length > 1000) {
        return;
      }
      const content = TLogger._serializeReportContent(contents);
      const headers = {};
      if (content.length !== 0) {
        await TRequest.instance.httpRequest('POST', url, headers, content)
          .then((resp) => {
            const result = JSON.parse(resp.data);
            if (result.error_code !== 0) {
              TLogger._reportCache = TLogger._reportCache.concat(contents);
            }
          })
          .catch(() => {
            TLogger._reportCache = TLogger._reportCache.concat(contents);
          });
      }
    } catch (error) {
      console.error('%c [ error ]-587', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }

  private static _getStackTrace(): string {
    const err = new Error();
    return err.stack.replace('Error\n', '\n');
  }

  private static _serializeReportContent(contents: string[]): string {
    let content = contents.join(',');
    if (content.length !== 0) {
      content.substr(0, content.length - 1);
      content = `{"items":[${content}]}`;
    }
    return content;
  }

  private static _startLooper(): void {
    TLogger._looper = setInterval(() => {
      TLogger.flushReport();
    }, TServerConfig.instance.defaultConfig.reportInterval * 1000);
  }

  private static _stopLooper(): void {
    clearInterval(TLogger._looper);
  }

  /**
   * 开启离线日志检测任务
   */
  private static _startOfflineLogLooper() {
    TLogger._offlineLogLooper = setInterval(() => {
      try {
        TLogger._reportOfflineLog();
      } catch (error) {
      }
    }, TServerConfig.instance.defaultConfig.configCheckInterval * 1000);

    setTimeout(() => {
      try {
        TLogger._reportOfflineLog();
      } catch (error) {
      }
    }, 5 * 1000);
  }

  private static _stopOfflineLogLooper() {
    clearInterval(TLogger._offlineLogLooper);
  }

  /**
   * 上报离线日志
   */
  private static _reportOfflineLog() {
    const serverConfig = TServerConfig.instance;
    if (TLogger._instances.size) {
      const config = TLogger._instances.get(TLogger._instances.entries().next().value[0])._config;
      const sdkAppIdConfig = serverConfig._appConfigs.get(config.sdkAppId);
      if (!sdkAppIdConfig) {
        return;
      }
      const userConfig: any = sdkAppIdConfig.userConfigs.get(config.userId);
      if (!userConfig) {
        return;
      }

      // 如果日志不开启，则不需要上报
      if (userConfig.uploadLog === 0) {
        return;
      }
      // 小程序不执行
      if (!this.tdb) {
        return;
      }
      // 小程序无法使用
      // #!if MP_ENV === 'false'
      const logFiles = userConfig.logFiles;
      logFiles && logFiles.forEach(async (item: any) => {
        const arr = item.split('-');
        // const filePrefix = `${arr[0]}-${arr[1]}`;
        const sdkAppId = arr[2];
        const classId = arr[3];
        const userId = arr[4];
        const date = arr[5];
        const uin = `${sdkAppId}-${classId}-${userId}-${date}`;
        try {
          const result = await TLogger.tdb?.getLog('uin', uin);
          if (result && result.length) {
            const cos = new TCosFile(sdkAppId, userId);
            const fileName = `${item}-${Date.now()}.log`;
            const file = new File([JSON.stringify(result)], fileName, {
              type: 'text/plain',
            });
            const uploadRes = await cos.upload(file, result);
            if (uploadRes) { // 如果上传成功, 则删掉旧日志
              console.log('上报离线成功');
              TLogger.tdb.removeLog('uin', uin);
            } else { // 如果上传失败，则先忽略，等待下一次
              console.log('上报离线失败');
            }
          }
        } catch (error) {

        }
      });
      // #!endif
    }
  }

  readonly _loggerName: string;
  private _config: TLoggerConfig = new TLoggerConfig();
  private _labelMap: Map<string, number> = new Map<string, number>();
  // 需要被合并上报的info日志, 20条 : 合并上报日志，方便排查问题
  private _mergeInfoLogs: Map<string, { mergeSize: number, mergeLogs: string[] }> = new Map();

  constructor(name: string) {
    this._loggerName = name;
  }

  public setConfig(config: Partial<TLoggerConfig>) {
    Object.assign(this._config, config);
    if (!this._config.sessionId) {
      this._config.sessionId = TUtil.getUUID();
    }
    if (!this._config.globalRandom) {
      this._config.globalRandom = TUtil.getGlobalRandom();
    }
    // console.log(`[TLogger-${this._loggerName}] setConfig`, config, this._config);
  }

  // 返回当前配置用于更新
  public getConfig() {
    return this._config;
  }

  public debug(param: TLoggerParam) {
    this.log(TLevel.DEBUG, param);
  }

  public info(param: TLoggerParam) {
    // 添加合并日志上报处理逻辑
    const logModule = param.module;
    const logAction = param.action;
    const logMergeKey = `${logModule}_${logAction}`;
    const logs = this._mergeInfoLogs.get(logMergeKey);
    if (logs === undefined) {
      // 原逻辑直接上报
      this.log(TLevel.INFO, param);
    } else {
      const timestamp = TUtil.dateToTimeStr(Date.now());
      const msg = `${timestamp} : ${param.param}`;
      logs.mergeLogs.push(msg);
      if (logs.mergeLogs.length >= logs.mergeSize) {
        // 原逻辑直接上报
        this.log(TLevel.INFO, {
          module: logModule,
          action: logAction,
          param: `${JSON.stringify(logs.mergeLogs)}`,
        });
        logs.mergeLogs = [];
      }
    }
  }

  public warn(param: TLoggerParam) {
    this.log(TLevel.WARN, param);
  }

  public error(param: TLoggerParam) {
    this.log(TLevel.ERROR, param);
  }

  public logStart(label: string, timestamp = TUtil.getTimestamp()) {
    this._labelMap.delete(label);
    this._labelMap.set(label, timestamp);
  }

  public logEnded(label: string, level: TLevel, param: TLoggerParam) {
    try {
      param = Object.assign(new TLoggerParam(), param);
      if (!TServerConfig.instance.needLog(this._config.sdkAppId, this._config.userId, level)) {
        // return;
      }
      const date = TUtil.dateToTimeStr();
      let cost = 0;
      const startTime = this._labelMap.get(label);
      if (startTime) {
        cost = TUtil.getTimestamp() - startTime;
        this._labelMap.delete(label);
      }
      let logContent = `${param.module}::${param.action}->${param.param}`;
      if (param.ext && param.ext.length !== 0) {
        logContent += `|${param.ext}|${cost}`;
      }

      switch (level) {
        case TLevel.DEBUG:
          // 过滤一些不需要了解又一直频繁打印的控制台log
          if (!excludeLogMap[param.action]) {
            console.debug(logContent);
          }
          logContent = `[${date}][D]${logContent}`;
          break;
        case TLevel.INFO:
          // 过滤一些不需要了解又一直频繁打印的控制台log
          if (!excludeLogMap[param.action]) {
            console.info(logContent);
          }
          logContent = `[${date}][I]${logContent}`;
          break;
        case TLevel.WARN:
          logContent = `${logContent}=>(${param.code}, ${param.desc ? param.desc : ''})`;
          if (param.stack && param.stack.length !== 0) {
            logContent += `\n${param.stack}`;
          }
          if (!param.stack) {
            param.stack = new Error().stack;
          }
          console.warn(logContent);
          logContent = `[${date}][W]${logContent}`;
          break;
        case TLevel.ERROR:
          logContent = `${logContent}=>(${param.code}, ${param.desc ? param.desc : ''})`;
          if (param.stack && param.stack.length !== 0) {
            logContent += `\n${param.stack}`;
          }
          if (!param.stack) {
            param.stack = new Error().stack;
          }
          console.error(logContent);
          logContent = `[${date}][E]${logContent}`;
          break;
        default:
          break;
      }
      if (TSession.instance.isIOS() && TSession.instance.isDebug()) {    // 拦截IOS端调试日志输出
        if ((window as any).webkit && (window as any).webkit.messageHandlers) {
          (window as any).webkit.messageHandlers.logger.postMessage(logContent);
        }
      }

      const paramExt = new TReportParamExt();
      // 设备信息
      let platform;
      if (TSession.instance.isMiniProgram()) {
        platform = 'minprogram';
      } else if (TSession.instance.isMiniProgramWebview()) {
        platform = 'miniprogram_webview';
      } else if (TSession.instance.isElectron()) {
        platform = 'electron';
        if (TSession.instance.isWindows()) {
          platform = 'electron_windows';
        } else if (TSession.instance.isMac()) {
          platform = 'electron_mac';
        }
      } else if (TSession.instance.isWeb()) {
        platform = 'pcweb';
        if (TSession.instance.isAndroidWeb()) {
          platform = 'android_web';
        } else if (TSession.instance.isIOSWeb()) {
          platform = 'ios_web';
        } else if (TSession.instance.isPadWeb()) {
          platform = 'pad_web';
        }
      } else if (TSession.instance.isIOS()) {
        platform = 'ios';
        if (TSession.instance.isPad()) {
          platform = 'ipad';
        }
      } else if (TSession.instance.isAndroid()) {
        platform = 'android';
        if (TSession.instance.isPad()) {
          platform = 'pad';
        }
      } else if (TSession.instance.isWindows()) {
        platform = 'pc';
      } else if (TSession.instance.isMac()) {
        platform = 'mac';
      }
      paramExt.devPlatform = platform;
      paramExt.devType = '';
      paramExt.devSysVersion = '';
      paramExt.devAndroidSdkVersion = '';
      if (!TSession.instance.isMiniProgram()) {
        paramExt.devUserAgent = navigator.userAgent;
      }
      // 应用信息
      paramExt.appModule = param.module;
      paramExt.appNativeSdkVersion = this._config.nativeSdkVersion;
      paramExt.appWebSdkVersion = this._config.webSdkVersion;
      paramExt.appWebUIVersion = this._config.webUIVersion;
      paramExt.appBusiness = this._config.business;
      paramExt.appSdkAppId = this._config.sdkAppId;
      paramExt.appNewEnterId = this._config.schoolId;
      paramExt.schoolId = this._config.realSchoolId;
      paramExt.appRoomId = this._config.roomId;
      paramExt.appUserId = this._config.userId;
      paramExt.appUserRole = TSession.instance.getUserRole();
      paramExt.appLanguage = this._config.language;
      // 状态信息
      paramExt.statsuAppCpu = 0;
      paramExt.statusSysCpu = 0;
      paramExt.statusAppMem = 0;
      paramExt.statusAvailableMem = 0;
      paramExt.statusCamera = TState?.instance?.getState(TMainState.Video_Device_Status, TDeviceStatus.Open);
      paramExt.statusMic = TState?.instance?.getState(TMainState.Audio_Device_Status, TDeviceStatus.Open);

      // 网络信息
      paramExt.netType = TNetType.WIFI;
      paramExt.netBandwidth = 0;
      paramExt.netRtt = 0;
      paramExt.networkQuality = TState?.instance?.getState(TMainState.Network_Quality_Status, 1);
      // 行为信息
      paramExt.actionName = param.action;
      paramExt.actionType = getActionTypeFromActionName(param.action);
      paramExt.actionParam = param.param;
      paramExt.actionExt = param.ext;
      paramExt.actionCost = cost;
      // 错误信息
      paramExt.errorCode = param.code;
      paramExt.errorDesc = param.desc;
      paramExt.errorStack = param.stack;
      // 其他信息
      paramExt.reportLevel = level;
      paramExt.reportTime = TUtil.getTimestamp();
      paramExt.reportSessionId = this._config.sessionId;
      paramExt.reportGlobalRandom = this._config.globalRandom;
      // 上报日志
      if (TLogger.tdb?.isSupport()) {
        const date = TUtil.dateToDateStr();
        TLogger.tdb.addLog(Object.assign({}, {
          uin: `${paramExt.appSdkAppId}-${paramExt.appRoomId}-${paramExt.appUserId}-${date}`,
          created_at: date,
        }, paramExt));
      }

      if (!param.report || !TServerConfig.instance
        .needReport(this._config.sdkAppId, this._config.userId, level, param.action)) {
        return;
      }

      const content = paramExt.Serialize();
      if (!this.checkIsPrivacyLog(param.action)) {
        TEvent.instance.notify(TMainEvent.Log_Output, paramExt, false);
      }
      TLogger._reportCache.push(content);
    } catch (error) {
      console.error('%c [ error ]-911', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }

  public checkIsPrivacyLog(actionName: string): boolean {
    const privacyActionNameList = [
      'trtc.setVideoEncoderParam',
      'trtc.setLocalVideoEncodeParams',
      'setVideoEncoderParam',
      'setVideoResolutionMode',
    ];
    return privacyActionNameList.includes(actionName);
  }

  public log(level: TLevel, param: TLoggerParam) {
    this.logEnded('', level, param);
  }
  /**
   * 合并上报日志白名单
   * @param module 日志模块
   * @param action 日志方法名
   * @param mergeSize 合并的条数
   * @param isAdd 添加（true）/取消(false)合并日志上报逻辑
   */
  public mergeInfoLog(module: string, action: string, mergeSize = 10, isAdd = true) {
    const logMergeKey = `${module}_${action}`;
    const logs = this._mergeInfoLogs.get(logMergeKey);
    if (isAdd) {
      if (logs === undefined) {
        // 没添加过，直接添加
        this._mergeInfoLogs.set(logMergeKey, {
          mergeSize,
          mergeLogs: [],
        });
      }
    } else {
      if (logs !== undefined) {
        // 已有日志先上传
        // 原逻辑直接上报
        this.log(TLevel.INFO, {
          module,
          action,
          param: `${JSON.stringify(logs.mergeLogs)}`,
        });
        this._mergeInfoLogs.delete(logMergeKey);
      }
    }
  }
}
