import i18next from 'i18next';
import HttpApi from 'i18next-http-backend';
import { SDK_MAIN_VERSION } from './tversion';
import { TSession } from './tsession';
import { TBusiness } from './business/tbusiness';

// 初始化i18next
const currentLocation = location.href.substring(0, location.href.indexOf('.html'));
const basePath = currentLocation.substring(0, currentLocation.lastIndexOf('/'));
const staticTcicVerionPath = `${basePath}/static/tcic/${SDK_MAIN_VERSION}`;

export const currentLng = TSession.instance.getLanguage();
console.log('初始化sdk时语言', currentLng);

const i18nLoadPath = `${staticTcicVerionPath}/locales/sdk/{{lng}}.json?t=202405131101`;
// TODO: 修改 i18next.t 方法，从而允许用户替换文案
export const i18nextPromise = i18next.use(HttpApi).init({
  lng: currentLng,
  fallbackLng: /zh(-\w+)?/g.test(currentLng) ? 'zh' : 'en',
  load: /zh(-\w+)?/g.test(currentLng) ? 'currentOnly' : 'languageOnly',
  keySeparator: false,
  nsSeparator: false,
  backend: {
    loadPath: i18nLoadPath,
  },
});

// TODO 多语言 先放版本目录，稳定后移到外层，更新不用发版本，直接在控制台上传
export const errorCodePromise = TBusiness.instance.loadErrorCodeConfig(currentLng, `${staticTcicVerionPath}/locales/errorcode/${currentLng}.json`);
