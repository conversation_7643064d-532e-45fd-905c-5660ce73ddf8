import {
  TLanguage,
  TLanguageOptions,
  parseLanguage,
  NameConfig,
  initNameConfig as initNameConfigInner,
  getNameConfig as getNameConfigInner,
} from '../constants/nameConfig';
// import { rum } from '../utils/aegis';
import { TClassInfo, TRoomType } from './business/tbusiness_class';
import { TSchoolInfo } from './business/tbusiness_school';
import { TUtil } from './tutil_inner';

declare let MP_ENV: boolean;
export class TWaterMark {
  public board: string;
  public video: string;
  public text: string;
  public textColor: string;
}

/**
 * 全局属性模块
 */
export class TSession {
  private static _instance: any = null;

  // sessionId是传进来的，electron版reload之后sessionId没变，另外加个字段区分
  private _lifeId = '';
  private _params = new Map();
  private _schoolInfo: TSchoolInfo = null;
  private _classInfo: TClassInfo = null;
  private _customData: any;
  private _supportTouch = false;
  private _isElectron = false;
  private _isWeb = false;
  private _isWin32 = false;
  private _isAndroidNative = false;
  private _isX5Webview = false;
  private _isAndroidWeb = false;
  private _isIosNative = false;
  private _isIosWeb = false;
  private _isHarmony = false;
  private _isHarmonyWeb = false;
  private _isHarmonyPC = false;
  private _isPadNative = false;
  private _isPadWeb = false;
  private _isMac = false;
  private _isWindows = false;
  private _isMiniProgram = false;
  private _isMiniProgramWebview = false;
  private _platformDetected = false;
  private _env = 'prod';
  private _lng: TLanguage = parseLanguage(navigator?.language); // 不能直接用 navigator.language，可能是 en-US en-IN 之类的
  private _isSupervisor = false;
  private _userRole = '';

  // 创建和切换语言时自动更新
  private _nameConfig: NameConfig;

  // 调试时记录log用
  private _logs: string[] = [];
  private _logFlushed = false;
  private _reportTRTCLog = false;
  private _TRTCLogs: string[] = [];
  private _currentRotate = 0; // 0 1 2 3
  constructor() {
    let randomStr = Math.floor(Math.random() * 10000).toString();
    if (randomStr.padStart) {
      randomStr = randomStr.padStart(4, '0');
    }
    this._lifeId = `${Date.now()}-${randomStr}`;
    this.setParams('webview', 'main');  // 默认为主窗口

    this.addLog(`lifeId: ${this._lifeId}`);
    this.addLog(`lng: ${this._lng}`);

    // 创建时就设置一次nameConfig
    this._updateNameConfig('constructor');
  }


  /**
   * 获取当前旋转的角度
   */
  getCurrentRotate(): number {
    return this._currentRotate;
  }
  /**
   * @param rotate 设置当前旋转角度
   * @returns 当前设置后的旋转角度
   */
  setCurrentRotate(rotate: number): number {
    this._currentRotate = rotate;
    return this._currentRotate;
  }
  /**
   * 获取实例
   * */
  public static get instance(): TSession {
    if (TSession._instance === null) {
      TSession._instance = new TSession();
      // 把初始参数设好
      if (MP_ENV) {
        TSession._instance.initParamsMp();
      } else {
        TSession._instance.initParams();
      }
    }
    return TSession._instance;
  }

  public isLogFlushed() {
    return this._logFlushed;
  }

  public setLogFlushed(flag: boolean) {
    this._logFlushed = !!flag;
  }

  public addLog(log: string) {
    if (this._logFlushed) {
      return;
    }
    this._logs.push(`[${Date.now()}] - ${log}`);
  }

  public addTRTCLog(log: string) {
    this._TRTCLogs.push(`[${Date.now()}] - ${log}`);
  }

  public injectConsoleInfoForTRTC() {
    const originConsoleInfo = console.info;
    // const originConsoleError = console.error;
    const t = this;
    if (originConsoleInfo.toString() === 'function info() { [native code] }') {
      console.info = function () {
        originConsoleInfo.apply(console, arguments);
        const len = arguments.length;
        const firstArg = arguments[0];
        // const lastArg = arguments[len - 1];
        if (firstArg && firstArg.indexOf && firstArg.indexOf('%cTRTC%c%s') > -1) {
          let logBegin = false;
          let res = '';
          for (let i = 0; i < len ; i++) {
            const tmp = arguments[i];
            if (tmp && typeof tmp === 'string' && (tmp.indexOf('<INFO>') > -1 || tmp.indexOf('<ERROR>') > -1)) {
              logBegin = true;
            }
            if (logBegin && typeof tmp === 'string') {
              res = res + tmp;
            }
          }
          // console.error('add trtc log', res);
          t.addTRTCLog(res);
        }
      };
    }
  }

  public setReportTRTCLog(flag: boolean) {
    this._reportTRTCLog = !!flag;
  }

  public flushLog() {
    if (this._logFlushed) {
      return;
    }
    this._logFlushed = true;
    let res = this._logs.join('\n');
    this._logs = [];
    if (this._reportTRTCLog) {
      const tlogs = this._TRTCLogs.join('\n');
      res = res + tlogs;
      this._TRTCLogs = [];
    }
    return res;
  }

  /**
   * 初始化缓存参数
   * @param {string} query &符号分割的参数列表（URL内的query串格式），若未传该参数，取当前页面URL的query串
   */
  public initParams(query = '') {
    // 优先读取localstorage参数
    [
      'schoolid',
      'classid',
      'userid',
      'usersig',
      'token',
      // 'sessionid',
      'platform',
      'webloadstamp',
      'originurl',
      'camera',
      'mic',
      'speaker',
      'orientation',
      'exp',
    ].forEach((param) => {
      this._params.set(param, localStorage.getItem(param));
    });

    const userId = this._params.get('userid');
    const tokenForUserId = userId ? localStorage.getItem(userId) : '';

    if (tokenForUserId) {
      this._params.set('token', tokenForUserId);
    }

    // 通过url解析参数
    const searchParams = TUtil.parseQuery(window.location.search.substring(1));
    this.addLog(`searchParams:${TUtil.getErrorDetailMessage(searchParams)}`);
    const queryParams = TUtil.parseQuery(query);
    this.addLog(`queryParams:${TUtil.getErrorDetailMessage(queryParams)}`);
    const totalParams = parseInt(searchParams.isreload, 10) === 1
      ? { ...queryParams, ...searchParams } // reload时，用search覆盖初始query
      : { ...searchParams, ...queryParams };
    Object.entries(totalParams).forEach(([key, value]: [string, string]) => {
      // 替换颜色的#符
      const val = value?.replace('%23', '#').replace('%20', '');
      this._params.set(key, val);
      // this.addLog(`param:${key}=>${val}`);
    });

    // 根据参数更新语言包
    const lng = this.getParams('lng');
    this.addLog(`initParams, search: ${window.location.search.substring(1)}, otherquery: ${query}, lngInParams ${lng}`);
    if (lng) {
      this.setLng(lng);
    }
  }

  public initParamsMp(query?: string) {
    [
      'schoolid',
      'classid',
      'userid',
      'usersig',
      'token',
      'sessionid',
      'platform',
      'webloadstamp',
      'originurl',
    ].forEach((param) => {
      this._params.set(param, wx.getStorageSync(param));
    });
    // 通过url解析参数
    if (!query) {
      // TODO: 确认此方法在何处
      // @ts-ignore
      const pages = getCurrentPages();
      const currentPage = pages[pages.length - 1];
      const options = currentPage.options;
      let urlWithArgs = '';
      Object.keys(options).forEach((key) => {
        const value = options[key];
        urlWithArgs += `&${key}=${value}`;
      });
      query = urlWithArgs.substring(1); // window.location.search.substring(1);
    }
    const vars = query.split('&');
    for (const item of vars) {
      const pair = item.split('=');
      this._params.set(pair[0], pair[1]);
    }

    // 根据参数更新语言包
    const lng = this.getParams('lng');
    if (lng) {
      this.setLng(lng);
    }
  }

  /** 设置当前环境 */
  public setEnv(env: string) {
    this._env = env;
  }

  /** 当前语言环境被切换 */
  public setLng(lng: string) {
    if (!lng) {
      return;
    }
    const newLng = parseLanguage(lng);
    if (newLng === this._lng) {
      return;
    }
    this._lng = newLng;
    this.addLog(`setLng ${lng}, now ${this._lng}`);

    this._updateNameConfig('setLng');
  }

  // 清除时间戳
  public clearTimeStamp() {
    if (this.isMiniProgram()) {
      wx.setStorageSync('webloadstamp', '0');
    } else {
      localStorage.setItem('webloadstamp', '0');
    }
  }

  /**
   * 检测指定URL是否本地URL
   * @param url 要检测的URL地址
   */
  public isLocalUrl(url: string): boolean {
    const parsedUrl = new URL(decodeURIComponent(url));
    if (parsedUrl.hostname === 'localhost') {
      return true;
    }
    // 局域网IP
    // 10.0.0.0 - ************** Addresses: 16,777,216
    // ********** - ************** Addresses: 1,048,576
    // *********** - *************** Addresses: 65,536
    const parts = parsedUrl.hostname.split('.');
    if (parts.length !== 4) {
      return false;
    }
    return parts[0] === '10'
      || (parts[0] === '172' && (parseInt(parts[1], 10) >= 16 && parseInt(parts[1], 10) <= 31))
      || (parts[0] === '192' && parts[1] === '168');
  }

  /**
   * 是否支持触摸事件
   */
  public supportTouch(): boolean {
    this._detectPlatform();
    return this._supportTouch;
  }

  /**
   * 是否Electron端
   */
  public isElectron(): boolean {
    this._detectPlatform();
    return this._isElectron;
  }

  /**
   * 判断是否32位Electron
   */
  public isWin32Electron(): boolean {
    this._detectPlatform();
    return this._isElectron && this._isWin32;
  }

  /**
   * 获取平台，该方法通过Native侧传入参数区分Native平台，如果没有传入参数，统一视为Web
   */
  public getPlatform(): string {
    if (this.isElectron()) {
      // 由于electron平台没有传入platform参数，这里做下特殊判断
      return 'electron';
    }
    // iOS和Android下，Native端会传入platform参数，非native统一返回web
    return this.getParams('platform', 'web').toLowerCase();
  }

  /**
   * 是否Web端，含所有设备上使用浏览器载入的场景
   */
  public isWeb(): boolean {
    this._detectPlatform();
    return this._isWeb;
  }

  public isX5Webview(): boolean {
    this._detectPlatform();
    return this._isX5Webview;
  }

  public isAndroidNative(): boolean {
    this._detectPlatform();
    return this._isAndroidNative;
  }

  public isAndroidWeb(): boolean {
    this._detectPlatform();
    return this._isAndroidWeb;
  }

  /**
   * 是否Android端，含Native和Web加载两种场景
   */
  public isAndroid(): boolean {
    this._detectPlatform();
    return this._isAndroidNative || this._isAndroidWeb;
  }

  public isIOSNative(): boolean {
    this._detectPlatform();
    return this._isIosNative;
  }

  public isIOSWeb(): boolean {
    this._detectPlatform();
    return this._isIosWeb;
  }

  /**
   * 是否iOS端，含Native和Web加载两种场景
   */
  public isIOS(): boolean {
    this._detectPlatform();
    return this._isIosNative || this._isIosWeb;
  }

  /**
   * 是否iOS8910系统
   */
  public isIOS8910(): boolean {
    if (this.isIOS()) {
      const userAgent = navigator.userAgent.toLowerCase();
      const isMobileIos8910 = userAgent.match(/OS (8|9|10)[_\d]* like Mac OS X/i);
      return !!isMobileIos8910;
    }
    return false;
  }

  /**
   * 是否是iOS11.2以下系统
   */
  public getIOSVersion(): string[] {
    if (this.isIOS()) {
      const userAgent = navigator.userAgent.toLowerCase();
      const versions = userAgent.match(/OS (\d+)_(\d+).* like Mac OS X/i);
      if (versions && versions.length === 3) {
        return [versions[1], versions[2]];
      }
    }
    return [];
  }

  public isPadNative(): boolean {
    this._detectPlatform();
    return this._isPadNative;
  }

  public isPadWeb(): boolean {
    this._detectPlatform();
    return this._isPadWeb;
  }

  /**
   * 是否大屏(平板或电视)，含Native和Web加载两种场景
   */
  public isPad(): boolean {
    this._detectPlatform();
    return this._isPadNative || this._isPadWeb;
  }

  /**
   * 是否Mac端，含Native和Web加载两种场景
   */
  public isMac(): boolean {
    this._detectPlatform();
    return this._isMac;
  }

  /**
   * 是否Windows端，含Native和Web加载两种场景
   */
  public isWindows(): boolean {
    this._detectPlatform();
    return this._isWindows;
  }

  public isMobileNative(): boolean {
    this._detectPlatform();
    // 目前还没有纯血鸿蒙 native
    return this._isAndroidNative || this._isIosNative;
  }

  public isMobileWeb(): boolean {
    this._detectPlatform();
    return (this._isAndroidWeb
        || this._isIosWeb
        || (this._isHarmonyWeb && !this._isHarmonyPC)
    ) && this._checkIsMobileWebWithScreenWidthAndRatio();
  }

  /**
   * 是否移动端，含Native和Web加载两种场景
   */
  public isMobile(): boolean {
    return this.isMobileNative() || this.isMobileWeb();
  }

  /**
   * 是否小程序
   */
  public isMiniProgram(): boolean {
    this._detectPlatform();
    return this._isMiniProgram;
  }

  public isMiniProgramWebview(): boolean {
    this._detectPlatform();
    return this._isMiniProgramWebview;
  }

  /**
   * 是否调试模块
   */
  public isDebug(): boolean {
    return this.getBooleanParams('debug');
  }

  /**
   * 获取参数
   * @param key 参数名称
   * @param defValue 默认值
   */
  public getParams(key: string, defValue?: any) {
    if (this._params.has(key)) {
      return this._params.get(key) || defValue;
    }
    return defValue;
  }

  /**
   * 获取整型参数
   * @param key 参数名称
   * @param defValue 默认值
   */
  public getIntParams(key: string, defValue = 0) {
    return parseInt(this.getParams(key, defValue.toString()), 10);
  }

  /**
   * 获取布尔型参数
   * @param key 参数名称
   * @param defValue 默认参数值
   */
  public getBooleanParams(key: string, defValue = false) {
    return this.getParams(key, defValue ? 'true' : 'false') === 'true';
  }

  /**
   * 设置参数
   * @param key 参数名称
   * @param value 参数值
   */
  public setParams(key: string, value: any) {
    this._params.set(key, value);
  }

  public isSubWindow() {
    return this.getParams('webview') !== 'main';
  }

  /**
   * 获取严重错误发生次数
   */
  public getSeriousErrorTimes(): number {
    return this.getIntParams('seriousError', 0);
  }

  /**
   * 设置学校信息
   * @param schoolInfo 学校信息
   */
  public setSchoolInfo(schoolInfo: TSchoolInfo) {
    this._schoolInfo = schoolInfo;
  }
  /**
   * isInFaith 原本是只把这个能力开放给infaith，现在准备全量开放
   */
  public isInFaithOrDemo() {
    // const schoolId = this.getSchoolId().toString();
    // return schoolId === '3552817' || schoolId === '3555081' || schoolId === '3923193';
    return true;
  }
  /**
   * 获取学校信息
   */
  public getSchoolInfo() {
    return this._schoolInfo;
  }

  /**
   * 初始化课堂与角色名称信息，需要在课堂信息ready之前
   */
  public initNameConfig(customJson: { [lng: string]: any }) {
    initNameConfigInner(customJson);
    this._updateNameConfig('initNameConfig');
    return this._nameConfig;
  }

  /**
   * 获取课堂与角色名称信息
   */
  public getNameConfig() {
    return this._nameConfig;
  }
  /**
   * 获取课堂与角色名称信息，命名容易误解，请使用 getNameConfig
   */
  public getRoleInfo() {
    return this._nameConfig;
  }

  public setClassInfo(classInfo: TClassInfo) {
    this._classInfo = classInfo;
  }

  /**
   * 假1v1需求：
   * 1、老师端看到所有的学生
   * 2、学生之间互相看不见，只能看到和听到老师，学生间互相流不订阅看不见听不见
   */
  public isFake1v1() {
    return this._classInfo.interactionMode === 1;
  }

  /**
   * 获取课堂信息
   */
  public getClassInfo() {
    return this._classInfo;
  }

  /**
   * 设置学校定制信息
   * @param customData 定制信息
   */
  public setCustomData(customData: any) {
    this._customData = customData;
  }

  /**
   * 获取学校定制信息
   */
  public getCustomData() {
    return this._customData;
  }

  /**
   * 获取当前使用的UI语言类型
   */
  public getLanguage() {
    return this._lng;
  }

  /**
   * 获取支持的UI语言类型
   */
  public getLanguageOptions() {
    return TLanguageOptions;
  }

  /**
   * 获取水印信息
   */
  public getWatermark() {
    const jsonString = this.getParams('watermark', '');
    return JSON.parse(jsonString);
  }

  /**
   * 获取当前学校 ID
   */
  public getSchoolId() {
    const schoolId = this.getIntParams('schoolId');
    const schoolid = this.getIntParams('schoolid');
    let sid = 0;
    if (schoolid > 0) {
      sid = schoolid;
    } else if (schoolId > 0) {
      sid = schoolId;
    }
    return this._schoolInfo ? this._schoolInfo.schoolId : sid;
  }

  /**
   * 获取当前课堂 ID
   */
  public getClassId() {
    return this.getIntParams('classid');
  }

  /**
   * 获取当前课堂 ID (客户定义）
   */
  public getCid() {
    return this.getParams('cid', '');
  }

  /**
   * 获取当前用户 ID
   */
  public getUserId() {
    return this.getParams('userid');
  }

  /**
   * 获取当前用户 ID (客户定义）
   */
  public getUid() {
    return this.getParams('uid', '');
  }

  /**
   * 获取当前用户签名信息
   */
  public getUserSig() {
    return this.getParams('usersig');
  }

  /**
   * 获取当前子用户签名信息
   */
  public getSubUserSig() {
    return this.getParams('subusersig');
  }

  /**
   * 设置当前用户签名信息
   * @param userSig 签名信息
   */
  public setUserSig(userSig: string) {
    this.setParams('usersig', userSig);
  }

  /**
   * 设置当前子用户签名信息
   * @param subUserSig 签名信息
   */
  public setSubUserSig(subUserSig: string) {
    this.setParams('subusersig', subUserSig);
  }

  /**
   * 获取当前用户业务鉴权信息
   */
  public getToken() {
    return this.getParams('token', '');
  }

  /**
   * 获取当前环境信息
   */
  public getEnv() {
    const param = this.getParams('env', this._env);
    if (param) {
      return param;
    }
    if (location.host.startsWith('test-')) {
      return 'test';
    }
    if (location.host.startsWith('dev-')) {
      return 'dev';
    }
    return 'prod';
  }

  /**
   * 获取传入的场景参数
   */
  public getScene() {
    const scene =  this.getParams('scene', 'default');
    this.addLog(`scene:${scene}`);
    return scene;
  }

  /**
   * 获取当前设备类型（由native侧传入）
   */
  public getDevice() {
    return this.getParams('device');
  }

  /**
   * 获取调试用的CSS链接
   */
  public getDebugCSS() {
    let url = this.getParams('debugcss');
    if (url) {
      // 方便调试
      if (url === '1') {
        url = 'http://localhost:8080/static/custom/custom.css';
        return url;
      }
      if (this.isLocalUrl(url)) {
        return url;
      }
      console.error('Debug CSS only allow from localhost', url);
    }
    return '';
  }

  /**
   * 获取调试用的JS链接
   */
  public getDebugJS() {
    let url = this.getParams('debugjs');
    if (url) {
      // 方便调试
      if (url === '1') {
        url = 'http://localhost:8080/static/custom/custom.js';
        return url;
      }
      if (this.isLocalUrl(url)) {
        return url;
      }
      console.error('Debug JS only allow from localhost', url);
    }
    return '';
  }

  /**
   * 判断是否是巡课老师
   */
  public isSupervisor() {
    return this._isSupervisor;
  }

  public setIsSuperVisor(value: boolean) {
    this._isSupervisor = value;
  }

  /**
    * 判断是否是访客
    */
  public isVisitor() {
    return this.getParams('role') === 'visitor';
  }

  /**
   * 判断是否是老师(获取课堂信息后调用)
   */
  public isTeacher(_userId?: string) {
    const selfUserId = this.getUserId();
    const userId = _userId || selfUserId;
    if (userId === selfUserId && this.isSupervisor()) {
      return false;
    }
    const classInfo = this.getClassInfo();
    return classInfo ? classInfo.teacherId === userId : false;
  }

  /**
   * 判断是否是助教(获取课堂信息后调用)
   */
  public isAssistant(_userId?: string) {
    const selfUserId = this.getUserId();
    const userId = _userId || selfUserId;
    if (userId === selfUserId && this.isSupervisor()) {
      return false;
    }
    const classInfo = this.getClassInfo();
    return classInfo && classInfo.assistants && classInfo.assistants.includes(userId);
  }

  /**
   * 判断是否是圆桌嘉宾(获取课堂信息后调用)
   */
  public isRoundTableGuest(_userId?: string) {
    const selfUserId = this.getUserId();
    const userId = _userId || selfUserId;
    if (userId === selfUserId && this.isSupervisor()) {
      return false;
    }
    const classInfo = this.getClassInfo();
    return classInfo && classInfo.guests && classInfo.guests.includes(userId);
  }

  /**
   * 判断是否是学生(获取课堂信息后调用)
   */
  public isStudent(_userId?: string) {
    const selfUserId = this.getUserId();
    const userId = _userId || selfUserId;
    if (userId === selfUserId && this.isSupervisor()) {
      return false;
    }
    // if (this.isRoundTableGuest(userId)) {
    //   return false;
    // }
    // 查询其他人属性，不是老师不是助教就认为是学生，巡课前面已经判断过这里不考虑
    return !this.isTeacher(userId) && !this.isAssistant(userId);
  }

  /**
   * 更新自己的角色(获取课堂信息后调用)
   */
  public initUserRole() {
    let role = '';
    if (this.isSupervisor()) {
      role = 'supervisor';
    } else if (this.isTeacher()) {
      role = 'teacher';
    } else if (this.isAssistant()) {
      role = 'assistant';
    } else {
      role = 'student';
    }
    this._userRole = role;

    // rum.aegis.setConfig({
    //   ext2: JSON.stringify({
    //     ...(JSON.parse(rum.aegis.config.ext2 || '{}')),
    //     role,
    //   }),
    // });
  }

  /**
   * 获取自己角色
   */
  public getUserRole() {
    return this._userRole;
  }

  /**
    * 判断是否录制模式，url中带有 role=supervisor&mode=record 为录制模式
    */
  public isRecordMode() {
    return this.getParams('role') === 'supervisor' && this.getParams('mode') === 'record';
  }

  /**
   * 判断是否是 RTMP 推流模式
   */
  public isRtmpMode() {
    return this.getParams('rtmp') === '1';
  }

  // 获取Globalrandom
  public getGlobalRandom() {
    return this.getParams('globalrandom', TUtil.getGlobalRandom());
  }

  // 获取SessionId
  public getSessionID() {
    let sessionId = this.getParams('sessionid', TUtil.getUUID());
    if (!sessionId) {
      sessionId = TUtil.getUUID();
    }
    return sessionId;
  }

  // 获取LifeId
  public getLifeID() {
    return this._lifeId;
  }

  public getFromMpAppId() {
    return this.getParams('fromMpAppId', '');
  }


  public getLockedOrientation(): ''|'landscape'|'portrait'|'locked' {
    return this.getParams('orientation', '');
  }

  protected _checkIsMobileWebWithScreenWidthAndRatio() {
    // 获取屏幕宽度和高度
    const screenWidth = window.innerWidth > 0 ? window.innerWidth : screen.width;
    const screenHeight = window.innerHeight > 0 ? window.innerHeight : screen.height;

    // 获取屏幕方向
    const isPortrait = window.matchMedia('(orientation: portrait)').matches;

    // 根据屏幕方向确定使用的宽高比
    const aspectRatio = isPortrait
      ? screenWidth / screenHeight : screenHeight / screenWidth;

    // 判断屏幕宽高比来确定设备类型
    return aspectRatio < 0.75 && screenWidth < (isPortrait ? 768 : 950);
  }

  protected _detectPlatform(): boolean {
    // 检测是否小程序
    if (MP_ENV) {
      this._isMiniProgram = true;
      return true;
    }
    if (this._platformDetected) {
      return true;
    }
    if (typeof navigator !== 'object' || typeof navigator.userAgent !== 'string') {
      console.error('_detectPlatform failed, navigator.userAgent invalid', navigator.userAgent);
      return false;
    }
    const userAgent = navigator.userAgent;
    if (userAgent.match(/MicroMessenger/i)) {
      const wxMiniprogram: any = wx;
      wxMiniprogram?.miniProgram.getEnv((res: any) => {
        console.log(res, 'wx');
        if (res.miniprogram) {
          this._isMiniProgramWebview = true;
          return true;
        }
      });
    }
    if (/X5|TBS/i.test(userAgent)) {
      this._isX5Webview = true;
    }
    // 检测是否electron平台
    // Renderer process
    // @ts-ignore
    if (typeof window !== 'undefined' && typeof window.process === 'object' && window.process.type === 'renderer') {
      this._isElectron = true;
      this._isWin32 = window.process.arch === 'ia32';
    }

    // Main process
    // @ts-ignore
    if (typeof process !== 'undefined' && typeof process.versions === 'object' && !!process.versions.electron) {
      this._isElectron = true;
    }

    // Detect the user agent when the `nodeIntegration` option is set to false
    if (userAgent.indexOf('Electron') >= 0) {
      this._isElectron = true;
    }
    this._supportTouch = 'ontouchstart' in window
      || ((window as any).DocumentTouch && document instanceof (window as any).DocumentTouch)
      || navigator.maxTouchPoints > 0
      // @ts-ignore
      || navigator.msMaxTouchPoints > 0;
    if (!this._supportTouch) {
      try {
        document.createEvent('TouchEvent');
        this._supportTouch = true;
      } catch (error) {
      }
    }

    // 检测是否iPad/Mac，结果供后续其它平台判断使用
    let isMac = /Macintosh|Mac OS X/i.test(userAgent);
    let isIpad = isMac || /iPad/i.test(userAgent);
    if (this._supportTouch) {
      // 从iOS13开始，iPad设备获取的userAgent值中不再包含“iPad”字段了，而变成了“Macintosh”字段
      // 由于Mac当前还没有支持触摸屏的设备，这里通过是否支持触摸事件来区分iPad和Mac
      isMac = false;
    } else {
      isIpad = false;
    }

    // https://developer.huawei.com/consumer/cn/doc/harmonyos-guides/web-useragent-V13
    this._isHarmony = /HarmonyOS|OpenHarmony/i.test(userAgent);

    // 检测是否Web平台
    const platform = this.getParams('platform', 'web').toLowerCase();
    this._isWeb = !this._isElectron && (platform === 'web');
    if (this._isWeb) {
      // 检测是否Android Web平台
      const isAndroid = /Android/.test(userAgent);
      this._isAndroidWeb = isAndroid;
      this._isHarmonyWeb = this._isHarmony;
      // 检测是否iOS Web平台
      const isIphone = /iPhone/.test(userAgent);
      this._isIosWeb = isIpad || isIphone;
      // 检测是否Pad Web平台
      const isFireFox = /Firefox/.test(userAgent);
      this._isPadWeb = /PlayBook/.test(userAgent)
        || (isAndroid && !/Mobile/.test(userAgent))
        || ((isFireFox || this._isHarmony) && /Tablet/.test(userAgent))
        || (isIpad && !isIphone);
    } else {
      // 检测是否Android Native平台
      this._isAndroidNative = platform === 'android';
      // 检测是否iOS Native平台
      this._isIosNative = platform === 'ios';
      // 检测是否Pad Native平台
      const padTypes = ['pad', 'tv', 'tablet'];
      const device = this.getDevice();
      this._isPadNative = device && padTypes.includes(device);
    }
    // 检测是否Mac平台
    this._isMac = isMac;
    // 检测是否Windows平台
    this._isWindows = /win32|wow32|win64|wow64|windows/i.test(userAgent);
    this._isHarmonyPC = this._isHarmony && /PC/i.test(userAgent);
    this._platformDetected = true;
    return true;
  }

  protected _updateNameConfig(reason: string) {
    const lng = this.getLanguage();
    // console.log(`==== _updateNameConfig, reason ${reason}, lng ${lng}`);
    // 很多组件都保存了 roleInfo/roomInfo 的引用，直接换对象无法更新到组件里。。。修改对象的值
    const newCfg = getNameConfigInner(lng);
    if (!this._nameConfig) {
      this._nameConfig = newCfg;
    } else {
      Object.assign(this._nameConfig.roleInfo, newCfg.roleInfo);
      Object.assign(this._nameConfig.roomInfo, newCfg.roomInfo);
    }
  }
}
