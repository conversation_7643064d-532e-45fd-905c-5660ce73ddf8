/* eslint-disable no-plusplus */
import i18next from 'i18next';
import type TRTCCloud from 'trtc-electron-sdk';
import { TEvent } from '../../base/tevent';
import { TMain } from '../tmain';
import { TMainState, TDeviceStatus } from '../../constants';
import { TSession } from '../tsession';
import { TState } from '../tstate';
import {
  TScreenCaptureSourceInfo,
  TScreenCaptureSourceType,
  TTrtcAction,
  TTrtcBase,
  TTrtcDeviceInfo,
  TTrtcDeviceState,
  TTrtcDeviceType,
  TTrtcEvent,
  TTrtcLocalVideoParams,
  TTrtcMode,
  TTrtcRemoteStatistics,
  TTrtcRtcInfo,
  TTrtcSnapshot,
  TTrtcStatistics,
  TTrtcVideoResolution,
  TTrtcVideoStreamType,
  TTrtcVideoFillMode,
  TTrtcVideoRotation,
  TTrtcVideoMirrorType,
  getVideoSizeFromResolution,
  getNativeErrorInfo,
  isDeviceAbnormal,
  TRTCVideoQosPreference,
} from './ttrtc_base';
import { TRTCElectronError as TCICError } from '../../base/tmodule';
import { TRTCVideoResolution, TRTCVideoStreamType } from 'trtc-electron-sdk';

export class TTrtcElectron extends TTrtcBase {
  /**
   * 获取 SDK JS，客户端不需要
   */
  public static getSDKJS(): string {
    return '';
  }
  /**
   * 获取插件列表
   */
  public static getPluginList(): { name: string, url: string }[] {
    return [];
  }

  private _trtc: TRTCCloud;
  private _electron: any = window.Electron;
  private _sdkAppId: number;
  private _userId: string;
  private _userSig: string;
  private _mode: TTrtcMode;
  private _roomId: number;
  private _isAnchor: boolean; // 是否为主播
  private _selectedSource: TScreenCaptureSourceInfo;
  private _rtcInfos: TTrtcRtcInfo[] = [];
  private _volumeInfo: Map<string, number[]> = new Map();
  private _lastMusicId = Math.floor(Math.random() * 1e9);
  // TODO 对齐参数
  private _rtcLocalBeautyPlugin: any = null; // 本流美颜插件
  private _beautyCfg: any = null; // 美颜配置(默认关闭)
  private _virtualUrl: string = undefined; //  虚拟背景
  private _enableVirtualBackground = false; // 是否开启虚拟背景(默认关闭)
  private _loadVirtualResource = false; // 是否加载虚拟背景资源

  private _localVideoDom: HTMLElement = null;

  // _devices 移到 TTrtcBase
  // private _devices: TTrtcDeviceInfo[] = [];
  private _currentCameraId = '';
  private _currentMicId = '';
  private _enableMixSystemAudio = false;
  private _localCameraParam: TTrtcLocalVideoParams = {
    // 本地摄像头配置
    mode: TTrtcVideoFillMode.Fill,
    rotation: TTrtcVideoRotation.Rotation_0,
    mirror: TTrtcVideoMirrorType.Auto,
  };
  private _currentMusicParams: any = {
    id: 0,
    path: '',
  };
  private _screenStatus:
  | ''
  | 'starting'
  | 'started'
  | 'startError'
  | 'stopping'
  | 'stopped'
  | 'stopError' = '';

  private updateClickableRectsTimer: any = null;

  // *******************************************主流程控制*******************************************
  public constructor() {
    super();
    this._trtc = new (window as any).TRTCCloud();
    this._trtc.setLogLevel(2);
    try {
      this._trtc.setLogCallback((log, level, module) => {
        this._info('TRTCLog', log + module);
      });
    } catch (error) {
      console.log(error);
    }
    (window as any).MTTrtc = this._trtc;
    this._getDevices().then((devices: any) => {
      this._devices = devices;
      this._info('initDevices', JSON.stringify(this._devices));
      // 更新是否notfound
      this._updateDeviceStatusByDeviceList('initDevices');
    });
  }

  public updateClickableRects() {
    // 非屏幕共享状态下，不需要更新
    // if (this._screenStatus !== 'started') {
    //   return;
    // }

    // // 用 typeof 来做判断，避免在旧版本多余的计算
    // if (typeof window?.Electron?.updateClickableRects !== 'function') {
    //   return;
    // }

    // const rects: { x: number; y: number; width: number; height: number }[] = [];

    // 对象值是为了应对 hover 场景添加一些上下左右的 padding
    const queryComponentsMap: {
      [key: string]: {
        left?: number,
        right?: number,
        top?: number,
        bottom?: number,
      }
    } = {
      // 顶部工具栏
      '.shortcut-toolbar-component': {},
      '.shortcut-panel__more-list': {
        top: 10, // DOM元素之间存在间隙，鼠标移动过程中容易触发离开事件
      },
      // 教师学生摄像头栏目
      '.screen-videowrap-component.share-screen__mode': {},
      'video-ctrl-component': {},
      // 白板工具栏
      'board-tool-component': {},
      '.back-popper': {
        right: 10,
      },
      // 花名册
      'member-list-component': {},
      // 聊天框
      'float-im-component': {
        left: 110, // 表情会左边突出
      },
      // 弹窗类
      'co-teaching-msg-box-component': {},
      'college-msg-box-component': {},
      '.msg-box-component': {},
      '.teacher-component-default': {},
      '.class-operation': {},
      // 开始上课 tips
      // 'header__class-tips': {},
    };

    setTimeout(() => {
      for (const queryComponent of Object.keys(queryComponentsMap)) {
        const doms = document.querySelectorAll(queryComponent);
        doms.forEach((dom) => {
          TMain.instance.addScreenShareClickArea(dom);
        });
        // if (doms?.length) {
        //   for (const dom of Array.from(doms)) {
        //     const rect = dom.getBoundingClientRect();
        //     let { x, y, width, height } = rect;

        //     if (queryComponentsMap[queryComponent]) {
        //       const { left, right, top, bottom } = queryComponentsMap[queryComponent];
        //       if (left) {
        //         x = x - left;
        //         width = width + left;
        //       }
        //       if (right) {
        //         width = width + right;
        //       }
        //       if (top) {
        //         y = y - top;
        //         height = height + top;
        //       }
        //       if (bottom) {
        //         height = height + bottom;
        //       }
        //     }

        //     rects.push({ x, y, width, height });
        //   }
        // }
      }
    }, 2000);


    // const queryAllComponents = [
    //   '.el-tooltip__popper',
    // ];
    // for (const queryAllComponent of queryAllComponents) {
    //   const doms = document.querySelectorAll(queryAllComponent);
    //   if (doms) {
    //     doms.forEach((dom) => {
    //       const rect = dom.getBoundingClientRect();
    //       const { x, y, width, height } = rect;
    //       rects.push({ x, y, width, height });
    //     });
    //   }
    // }

    // console.timeEnd('updateClickableRects');

    // console.log('updateClickableRects', rects);

    // window?.Electron?.updateClickableRects?.(rects);
  }

  public removeLogCallback(): void {
    // try catch 主要为了兼容老的rtc 版本没有setLogCallback 方法
    try {
      this._trtc.setLogCallback(null);
    } catch (error) {
      console.log('remove log callback error');
    }
  }

  /**
   * 检测当前环境是否支持
   */
  public checkSystemRequirements(): Promise<any> {
    return Promise.resolve({ result: true });
  }

  public isWebRTCSupport(): boolean {
    return true;
  }

  /**
   * 模块初始化
   * @param sdkAppId  sdkAppId
   * @param userId    用户帐号
   * @param userSig   用户签名
   * @param mode      TRTC模式
   */
  public init(
    sdkAppId: number,
    userId: string,
    userSig: string,
    mode = TTrtcMode.RTC,
  ): Promise<void> {
    const executor = this.newExecutor(TTrtcAction.Init);

    setTimeout(() => {
      this._info(
        'init',
        JSON.stringify({
          sdkAppId,
          userId,
          mode,
          sdk: this._trtc.getSDKVersion(),
        }),
      );
      this._sdkAppId = sdkAppId;
      this._userId = userId;
      this._userSig = userSig;
      this._mode = mode;
      this._setTrtcCallback();
      executor.resolve();
    }, 0);
    return executor.getPromise();
  }

  /**
   * 模块反初始化
   */
  public unInit(): Promise<void> {
    this._info('unInit');
    if (this._trtc) {
      this._trtc.destroy();
      this._trtc = undefined;
    }
    const executor = this.newExecutor(TTrtcAction.UnInit);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 加入音视频房间
   * @param roomId    音视频房间 ID
   * @param isAnchor  是否主播角色
   */
  public join(roomId: number, isAnchor: boolean): Promise<void> {
    this._info(
      'join',
      JSON.stringify({
        roomId,
        isAnchor,
      }),
    );
    this._roomId = roomId;
    this._isAnchor = isAnchor;
    const param = new (window as any).TRTCDefine.TRTCParams();
    param.sdkAppId = this._sdkAppId;
    param.roomId = this._roomId;
    param.userId = this._userId;
    param.userSig = this._userSig;
    param.role = this._isAnchor ? 20 : 21;
    this._trtc.enterRoom(param, this._mode === TTrtcMode.RTC ? 0 : 1);
    return this.newPromise(TTrtcAction.Join);
  }

  /**
   * 退出音视频房间
   */
  public quit(): Promise<void> {
    this._info('quit');
    this._trtc.exitRoom();
    return this.newPromise(TTrtcAction.Quit, '', true, 1000);
  }

  /**
   * 切换用户角色
   * @param isAnchor    是否主播角色
   */
  public switchRole(isAnchor: boolean): Promise<void> {
    this._info(
      'switchRole',
      JSON.stringify({
        isAnchor,
      }),
    );
    if (isAnchor) {
      this._trtc.switchRole(20);
    } else {
      this._trtc.switchRole(21);
    }
    return this.newPromise(TTrtcAction.SwitchRole, '', false);
  }

  public setNetworkQosParam(preference: TRTCVideoQosPreference): Promise<void> {
    this._trtc.setNetworkQosParam({
      preference,
    });
    return Promise.resolve();
  }

  /**
   * 获取上报用的是RTCInfo
   */
  public getRtcInfo() {
    return {
      video: this._rtcInfos,
      audio: new Map(),
    };
  }

  // *******************************************视频控制*******************************************
  /**
   * 设置本地视频参数
   * @param option      本地视频参数
   */
  public setLocalVideoParams(option: TTrtcLocalVideoParams): Promise<void> {
    this._info('setLocalVideoParams', JSON.stringify(option));
    // 缓存摄像头的配置
    this._localCameraParam = {
      ...this._localCameraParam,
      ...(option || {}),
    };
    this._trtc.setLocalRenderParams({
      rotation: this._localCameraParam.rotation as any,
      fillMode: this._localCameraParam.mode as any,
      mirrorType: this._localCameraParam.mirror as any,
    });
    const mirror = this._localCameraParam.mirror === 1;
    // 同步镜像到远端
    this._trtc.setVideoEncoderMirror(mirror);
    const executor = this.newExecutor(TTrtcAction.SetLocalVideoParams);
    executor.resolve();
    return executor.getPromise();
  }

  public setVideoResolutionMode(resMode: 0 | 1) {
    // TODO: 实现electron设置video横竖屏的方法
    return Promise.resolve();
  }

  /**
   * 设置视频编码参数
   * @param resolution  编码分辨率
   * @param fps         编码FPS
   * @param bitrate     编码码率
   */
  public setVideoEncoderParam(
    resolution: TTrtcVideoResolution,
    fps: number,
    bitrate: number,
  ): Promise<void> {
    this._info(
      'setVideoEncoderParam',
      JSON.stringify({
        resolution,
        fps,
        bitrate,
      }),
    );
    // mac下走硬编码可能存在单帧多slice的情况，快直播SDK目前无法兼容，因此强制走软编码
    if (TSession.instance.isMac() && TMain.instance.isLiveClass()) {
      const size = getVideoSizeFromResolution(resolution);
      const params = {
        api: 'setVideoEncodeParamEx',
        params: {
          streamType: 1,
          videoWidth: size.width,
          videoHeight: size.height,
          videoFps: fps,
          videoBitrate: bitrate,
          // rcMethod: 1,    // 1:CBR, 2:VBR
          codecType: 0, // 0:软编, 1:硬编
          // minQP: 26,
          // maxQP: 36,
          // gop: 5,
        },
      };
      this._trtc.callExperimentalAPI(JSON.stringify(params));
    } else {
      const trtcDefine = (window as any).TRTCDefine;
      const param = new trtcDefine.TRTCVideoEncParam();
      param.videoResolution = resolution;
      param.videoFps = fps;
      param.videoBitrate = bitrate;
      this._trtc.setVideoEncoderParam(param);
    }
    const executor = this.newExecutor(TTrtcAction.SetVideoEncoderParam);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 开启本地视频采集及渲染
   * @param dom         用于渲染视频画面的DOM节点
   */
  public async startLocalVideo(dom: HTMLElement): Promise<void> {
    this._info('startLocalVideo', 'start');
    const list = this.getExecutorList(TTrtcAction.StartLocalVideo);
    const oldExecutor = list?.length > 0 ? list[0] : null;
    const executor = this.newExecutor(TTrtcAction.StartLocalVideo, false, 15000);
    if (this._cameraTestStarted) {
      this._info('startLocalVideo', 'isTestingCamera, reject');
      executor.reject(new TCICError(-1, '', i18next.t('正在设备检测，打开摄像头失败')));
      return executor.getPromise();
    }
    if (this._trtc.getCameraDevicesList().length === 0) {
      this._info('startLocalVideo', 'device not found, reject');
      this._processCameraResult(false, { errCode: 1111, errMsg: '' }, 'startLocalVideo but no camera device');
      return executor.getPromise();
    }
    if (!this._cameraStarted) {
      // 如果已经开始采集则没有ready回调，强制调用一次stop
      this._info('startLocalVideo', 'stopLocalVideo first');
      await this.stopLocalVideo();

      // const version = this._trtc.getSDKVersion();
      // TODO 该接口在8.x 之后废弃
      // if (version && parseInt(version, 10) < 8 && this._trtc.setLocalViewFillMode) {
      //   this._trtc.setLocalViewFillMode((window as any).TRTCDefine.TRTCVideoFillMode.TRTCVideoFillMode_Fill);
      // }

      this._info('startLocalVideo', 'set cameraStarted');
      this._cameraStarted = true; // 只是开始打开，并不表示成功

      this._info('startLocalVideo', 'trtc.startLocalPreview');
      this._trtc.startLocalPreview(dom || this._localVideoDom);
      this._localVideoDom = dom || this._localVideoDom;

      this._trtc.setBeautyStyle(1, 5, 5, 5);
    } else {
      if (!oldExecutor) {
        // 已经打开
        this._info('startLocalVideo', 'alreay started, resolve');
        executor.resolve();
      } else {
        // 正在打开中，继续等待
        this._info('startLocalVideo', `alreay starting, oldExecutor ${oldExecutor.getIdentifier()}`);
      }
    }
    // 该Promise的状态在外部用 this.resolveExecutor/this.rejectExecutor控制..
    return executor.getPromise().catch((err) => {
      this._cameraStarted = false;
      return Promise.reject(err);
    });
  }

  /**
   * 关闭本地视频采集及渲染
   */
  public stopLocalVideo(): Promise<void> {
    const deviceStatus = TState.instance.getState(TMainState.Video_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._info(
      'stopLocalVideo',
      `_cameraStarted ${this._cameraStarted}, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}`,
    );
    this._cameraStarted = false;
    this._currentCameraId = '';
    this._trtc.stopLocalPreview();
    this._setState(TMainState.Video_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this._setState(TMainState.Video_Capture, false);
    this._updatePublishState(TMainState.Video_Publish);
    const executor = this.newExecutor(TTrtcAction.StopLocalVideo);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 控制是否屏蔽自己的视频画面，屏蔽后不推流
   * @param mute        是否屏蔽
   */
  public muteLocalVideo(mute: boolean): Promise<void> {
    this._info(
      'muteLocalVideo',
      JSON.stringify({
        mute,
      }),
    );
    this._publishStatus.video = !mute;
    this._trtc.muteLocalVideo(mute);
    this._updatePublishState(TMainState.Video_Publish);
    const executor = this.newExecutor(TTrtcAction.MuteLocalVideo);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 开始接收并渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param dom         用于渲染视频画面的DOM节点
   * @param isFitMode   是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  public startRemoteVideo(
    userId: string,
    type: TTrtcVideoStreamType,
    dom: HTMLElement,
    isFitMode?: boolean,
  ): Promise<void> {
    this._info(
      'startRemoteVideo',
      JSON.stringify({
        userId,
        type,
      }),
    );
    this._trtc.startRemoteView(userId, dom, type as any);
    const trtcDefine = (window as any).TRTCDefine;
    const fitMode = isFitMode
      ? trtcDefine.TRTCVideoFillMode.TRTCVideoFillMode_Fit
      : trtcDefine.TRTCVideoFillMode.TRTCVideoFillMode_Fill;
    const rotate = trtcDefine.TRTCVideoRotation.TRTCVideoRotation0;
    const mirror = trtcDefine.TRTCVideoMirrorType.TRTCVideoMirrorType_Disable;
    const params = new trtcDefine.TRTCRenderParams(rotate, fitMode, mirror);
    this._trtc.setRemoteRenderParams(userId, type as any, params);
    // this._trtc.setRemoteViewFillMode(userId, fitMode);
    const executor = this.newExecutor(TTrtcAction.StartRemoteVideo);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 更新远端视频画面渲染模式
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   * @param isFitMode    是否使用自适应模式渲染(默认关闭，即使用放大裁剪的方式渲染)
   */
  public updateRemoteVideoFitMode(
    userId: string,
    type: TTrtcVideoStreamType,
    isFitMode: boolean,
  ): Promise<void> {
    const trtcDefine = (window as any).TRTCDefine;
    const fitMode = isFitMode
      ? trtcDefine.TRTCVideoFillMode.TRTCVideoFillMode_Fit
      : trtcDefine.TRTCVideoFillMode.TRTCVideoFillMode_Fill;
    const rotate = trtcDefine.TRTCVideoRotation.TRTCVideoRotation0;
    const mirror = trtcDefine.TRTCVideoMirrorType.TRTCVideoMirrorType_Disable;
    const params = new trtcDefine.TRTCRenderParams(rotate, fitMode, mirror);
    this._trtc.setRemoteRenderParams(userId, type as any, params);
    // TODO 先用旧接口(旧接口无法区分主路/辅路)
    // this._trtc.setRemoteViewFillMode(userId, fitMode);
    return Promise.resolve();
  }

  /**
   * 停止接收和渲染远端视频画面
   * @param userId      要处理的用户ID
   * @param type        要处理的视频流类型
   */
  public stopRemoteVideo(
    userId: string,
    type: TTrtcVideoStreamType,
  ): Promise<void> {
    this._info(
      'stopRemoteVideo',
      JSON.stringify({
        userId,
        type,
      }),
    );
    this._trtc.stopRemoteView(userId, type as any);
    const executor = this.newExecutor(TTrtcAction.StopRemoteVideo);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 视频截图
   * @param userId      要处理的用户ID
   * @param streamType  要处理的视频流类型
   */
  public snapshotVideo(
    userId: string,
    streamType: TTrtcVideoStreamType,
  ): Promise<TTrtcSnapshot> {
    this._info(
      'snapshotVideo',
      JSON.stringify({
        streamType,
      }),
    );
    this._trtc.snapshotVideo(userId, streamType as any);
    return this.newPromise(TTrtcAction.SnapshotVideo);
  }

  /**
   * 暂停渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  public pauseVideoRender(
    userId: string,
    streamType: TTrtcVideoStreamType,
  ): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 恢复渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  public resumeVideoRender(
    userId: string,
    streamType: TTrtcVideoStreamType,
  ): Promise<void> {
    return Promise.resolve();
  }

  /**
   * 重置视频渲染
   * @param userId   要处理的用户id
   * @param streamType  要处理的视频流类型
   */
  public resetVideoRender(userId: string, type: TTrtcVideoStreamType) {}

  // *******************************************音频控制*******************************************
  /**
   * 开始本地音频采集
   * @param highAudioQuality  高清音质
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  public async startLocalAudio(
    highAudioQuality: boolean,
    dom?: HTMLElement,
  ): Promise<void> {
    this._info('startLocalAudio', `start, highAudioQuality: ${highAudioQuality}`);
    const executor = this.newExecutor(TTrtcAction.StartLocalAudio, false);
    // TODO 和 startLocalVideo 不一致，this._micTestStarted 时没有直接 reject，为什么?
    if (this._trtc.getMicDevicesList().length === 0) {
      this._info('startLocalAudio', 'device not found, reject');
      this._processMicResult(false, { errCode: 1201, errMsg: '' }, 'startLocalAudio but no mic device');
      return executor.getPromise();
    }
    // TODO 和 startLocalVideo 不一致，this._micStarted 时没有直接 resolve，为什么?
    // 如果已经开始采集则没有ready回调，强制调用一次stop
    this._info('startLocalAudio', 'stopLocalAudio first');
    await this.stopLocalAudio();

    this._info('startLocalAudio', 'set micStarted');
    this._micStarted = true; // 只是开始打开，并不表示成功

    // TRTCAudioQuality 文档 https://cloud.tencent.com/document/product/647/79641#f8aeb89d8ef78db15d893e55f68cdb42
    // 产品要求低音质用 TRTCAudioQualityDefault，因为 TRTCAudioQualitySpeech 采样率太低 - 2023.05.19
    this._info('startLocalAudio', 'trtc.startLocalAudio');
    this._trtc.startLocalAudio(highAudioQuality
      ? (window as any).TRTCDefine.TRTCAudioQuality.TRTCAudioQualityMusic
      : (window as any).TRTCDefine.TRTCAudioQuality.TRTCAudioQualityDefault);

    // 当前 Appid 已经配置了云控的 AI 降噪，通过 TRTCAudioQualitySpeech 来开启
    // this._trtc.startLocalAudio((window as any).TRTCDefine.TRTCAudioQuality.TRTCAudioQualitySpeech);

    // 该Promise的状态在trtc的回调中用 this.resolveExecutor/this.rejectExecutor控制..
    return executor.getPromise().catch((err) => {
      this._micStarted = false;
      return Promise.reject(err);
    });
  }

  /**
   * 停止本地音频采集
   */
  public stopLocalAudio(): Promise<void> {
    const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._info(
      'stopLocalAudio',
      `_micStarted ${this._micStarted}, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}`,
    );
    this._micStarted = false;
    this._currentMicId = '';
    this._trtc.stopLocalAudio();
    this._setState(TMainState.Audio_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this._setState(TMainState.Audio_Capture, false);
    this._setState(TMainState.Audio_Publish, false);
    const executor = this.newExecutor(TTrtcAction.StopLocalAudio);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 控制是否屏蔽自己的声音
   * @param mute        是否屏蔽
   */
  public muteLocalAudio(mute: boolean): Promise<void> {
    this._info(
      'muteLocalAudio',
      JSON.stringify({
        mute,
      }),
    );
    if (!mute) {
      this.setMicVolume(this._micVolumeWhenOn);
    } else {
      this.setMicVolume(0);
    }
    this._publishStatus.audio = !mute;
    this._trtc.muteLocalAudio(false);
    console.log('===>>tTRTC::muteLocalAudio=====', mute);
    this._updatePublishState(TMainState.Audio_Publish);
    const executor = this.newExecutor(TTrtcAction.MuteLocalAudio);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 控制是否屏蔽远端的声音
   * @param userId      要处理的用户ID
   * @param mute        是否屏蔽
   */
  public muteRemoteAudio(userId: string, mute: boolean): Promise<void> {
    this._info(
      'muteRemoteAudio',
      JSON.stringify({
        userId,
        mute,
      }),
    );
    this._trtc.muteRemoteAudio(userId, mute);
    const executor = this.newExecutor(TTrtcAction.MuteRemoteAudio);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 开启音量大小回调，回调直接通过事件抛出
   * @param interval    回调间隔(最小100ms，0为关闭)
   */
  public enableVolumeEvaluation(interval: number): Promise<void> {
    this._info(
      'enableVolumeEvaluation',
      JSON.stringify({
        interval,
      }),
    );
    this._trtc.enableAudioVolumeEvaluation(interval);
    const executor = this.newExecutor(TTrtcAction.EnableVolumeEvaluation);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 开始自定义音视频采集
   */
  public startCaptureStream(elementId: string): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('系统不支持')));
  }

  /**
   * 停止自定义音视频采集
   */
  public stopCaptureStream(): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', i18next.t('系统不支持')));
  }

  // *******************************************设备检测*******************************************
  /**
   * 开始摄像头设备测试
   * @param dom         用于渲染摄像头画面的DOM节点，不传入表示只是打开摄像头，但是不渲染
   */
  public startCameraTest(dom: HTMLElement): Promise<void> {
    this._info('startCameraTest', `_cameraTestStarted ${this._cameraTestStarted}`);
    // 如果已经开始采集则没有ready回调，强制调用一次stop
    if (this._cameraTestStarted) {
      this._stopCameraTestInner();
    }
    return this._startCameraTest(dom);
  }

  /**
   * 停止摄像头设备测试
   */
  public stopCameraTest(): Promise<void> {
    const list = this.getExecutorList(TTrtcAction.StartCameraTest);
    this._info('stopCameraTest', `has testCameraExecutor ${list?.length > 0}, _cameraTestStarted ${this._cameraTestStarted}`);
    this._stopCameraTestInner();
    if (list?.length > 0) {
      this.rejectExecutor(TTrtcAction.StartCameraTest, new TCICError(-1, 'user stop'), true);
    }
    const executor = this.newExecutor(TTrtcAction.StopCameraTest);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 开始麦克风设备测试
   * @param dom         用于插入音频元素的DOM节点，只有Web端需要
   */
  public startMicTest(dom?: HTMLElement): Promise<void> {
    this._info('startMicTest', `_micTestStarted ${this._micTestStarted}`);
    // 如果已经开始采集则没有ready回调，强制调用一次stop
    if (this._micTestStarted) {
      this._stopMicTestInner();
    }
    return this._startMicTest(dom);
  }

  /**
   * 停止麦克风设备测试
   */
  public stopMicTest(): Promise<void> {
    const list = this.getExecutorList(TTrtcAction.StartMicTest);
    this._info('stopMicTest', `has testMicExecutor ${list?.length > 0}, _micTestStarted ${this._micTestStarted}`);
    this._stopMicTestInner();
    if (list?.length > 0) {
      this.rejectExecutor(TTrtcAction.StartMicTest, new TCICError(-1, 'user stop'), true);
    }
    const executor = this.newExecutor(TTrtcAction.StopMicTest);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 开启扬声器设备测试
   * @param path        要播放的声音文件路径
   */
  public startSpeakerTest(path: string): Promise<void> {
    this._info(
      'startSpeakerTest',
      `path=${path}`,
    );
    this._trtc.startSpeakerDeviceTest(path);
    const executor = this.newExecutor(TTrtcAction.StartSpeakerTest, false);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 停止扬声器设备测试
   */
  public stopSpeakerTest(): Promise<void> {
    this._info('stopSpeakerTest');
    this._trtc.stopSpeakerDeviceTest();
    const executor = this.newExecutor(TTrtcAction.StopSpeakerTest);
    executor.resolve();
    return executor.getPromise();
  }

  // *******************************************设备管理*******************************************
  /**
   * 获取摄像头设备列表
   */
  public getCameras(): Promise<TTrtcDeviceInfo[]> {
    const executor = this.newExecutor(TTrtcAction.GetCameras, false);
    if (this.getExecutorList(TTrtcAction.GetCameras).length === 1) {
      setTimeout(() => {
        const deviceInfoList = this._trtc.getCameraDevicesList();
        for (const item of deviceInfoList) {
          (item as any).type = TTrtcDeviceType.Camera;
        }
        this.resolveExecutor(TTrtcAction.GetCameras, deviceInfoList, true);
      }, 0);
    }
    return executor.getPromise();
  }

  /**
   * 切换使用的摄像头设备
   * @param deviceId      要切换的设备ID
   */
  public switchCamera(deviceId: string): Promise<void> {
    this._info(
      'switchCamera',
      JSON.stringify({
        deviceId,
      }),
    );
    this._trtc.setCurrentCameraDevice(deviceId);
    const executor = this.newExecutor(TTrtcAction.SwitchCamera, false);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 获取正在使用的摄像头设备ID
   */
  public getCameraDeviceId(): Promise<string> {
    const deviceInfo = this._trtc.getCurrentCameraDevice();
    const executor = this.newExecutor(TTrtcAction.GetCameraDeviceId);
    executor.resolve(deviceInfo.deviceId);
    return executor.getPromise();
  }

  /**
   * 获取麦克风设备列表
   */
  public getMics(): Promise<TTrtcDeviceInfo[]> {
    const executor = this.newExecutor(TTrtcAction.GetMics, false);
    if (this.getExecutorList(TTrtcAction.GetMics).length === 1) {
      setTimeout(() => {
        const deviceInfoList = this._trtc.getMicDevicesList();
        const result = deviceInfoList.map(i => ({
          ...i,
          type: TTrtcDeviceType.Mic,
        }));
        this.resolveExecutor(TTrtcAction.GetMics, result, true);
      }, 0);
    }
    return executor.getPromise();
  }

  /**
   * 切换使用的麦克风设备
   * @param deviceId      要切换的设备ID
   */
  public switchMic(deviceId: string): Promise<void> {
    this._info(
      'switchMic',
      JSON.stringify({
        deviceId,
      }),
    );
    this._trtc.setCurrentMicDevice(deviceId);
    const executor = this.newExecutor(TTrtcAction.SwitchMic, false);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 获取正在使用的麦克风设备ID
   */
  public getMicDeviceId(): Promise<string> {
    const deviceInfo = this._trtc.getCurrentMicDevice();
    const executor = this.newExecutor(TTrtcAction.GetMicDeviceId);
    executor.resolve(deviceInfo.deviceId);
    return executor.getPromise();
  }

  /**
   * 设置正在使用的麦克风设备音量
   * @param volume        要设置的音量大小
   */
  public setMicVolume(volume: number): Promise<void> {
    this._info(
      'setMicVolume',
      JSON.stringify({
        volume,
      }),
    );
    const executor = this.newExecutor(TTrtcAction.SetMicVolume);
    const vol = Math.floor(volume);
    if (!isNaN(vol)) {
      this._micVolume = vol;
      this._trtc.setAudioCaptureVolume(this._micVolume);
      if (vol > 0) {
        this._micVolumeWhenOn = vol;
      }
      console.log('===>>tTRTC::setMicVolume=====', vol);
      this._updatePublishState(TMainState.Audio_Publish);
      executor.resolve();
    } else {
      executor.reject(new TCICError(-1, i18next.t('传入非法参数')));
    }
    return executor.getPromise();
  }

  /**
   * 获取正在使用的系统设备音量
   */
  public getMicVolume(): Promise<number> {
    return Promise.resolve(parseInt(this._trtc.getAudioCaptureVolume(), 10));
  }

  /**
   * 获取正在使用的麦克风音量
   */
  public getRealMicVolume(): Promise<number> {
    // 做一下和老版本的兼容处理
    return Promise.resolve(parseInt(`${this._trtc.getCurrentMicDeviceVolume()}`, 10));
  }

  /**
   * 获取扬声器设备列表
   */
  public getSpeakers(): Promise<TTrtcDeviceInfo[]> {
    const executor = this.newExecutor(TTrtcAction.GetSpeakers, false);
    if (this.getExecutorList(TTrtcAction.GetSpeakers).length === 1) {
      setTimeout(() => {
        const deviceInfoList = this._trtc.getSpeakerDevicesList();
        const result = deviceInfoList.map(i => ({
          ...i,
          type: TTrtcDeviceType.Speaker,
        }));
        this.resolveExecutor(TTrtcAction.GetSpeakers, result, true);
      }, 0);
    }
    return executor.getPromise();
  }

  /**
   * 切换使用的扬声器设备
   * @param deviceId      要切换的设备ID
   */
  public switchSpeaker(deviceId: string): Promise<void> {
    this._info(
      'switchSpeaker',
      JSON.stringify({
        deviceId,
      }),
    );
    this._trtc.setCurrentSpeakerDevice(deviceId);
    const executor = this.newExecutor(TTrtcAction.SwitchSpeaker);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 获取正在使用的扬声器设备ID
   */
  public getSpeakerDeviceId(): Promise<string> {
    const deviceInfo = this._trtc.getCurrentSpeakerDevice();
    const executor = this.newExecutor(TTrtcAction.GetSpeakerDeviceId);
    executor.resolve(deviceInfo.deviceId);
    return executor.getPromise();
  }

  /**
   * 设置正在使用的扬声器设备音量
   * @param volume        要设置的音量大小
   */
  public setSpeakerVolume(volume: number): Promise<void> {
    this._info(
      'setSpeakerVolume',
      JSON.stringify({
        volume,
      }),
    );
    const executor = this.newExecutor(TTrtcAction.SetSpeakerVolume);
    this._trtc.setAudioPlayoutVolume(Math.floor(volume));
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 获取正在使用的扬声器设备音量
   */
  public getSpeakerVolume(): Promise<number> {
    return Promise.resolve(this._trtc.getAudioPlayoutVolume());
  }

  // *******************************************屏幕分享*******************************************

  /**
   * 检查当前平台是否支持屏幕分享
   */
  public isScreenShareSupported(): Promise<boolean> {
    const executor = this.newExecutor(TTrtcAction.IsScreenShareSupported);
    executor.resolve(true);
    return executor.getPromise();
  }

  /**
   * 检查屏幕分享权限
   */
  public hasScreenCapturePermission(): Promise<boolean> {
    return this._electron.hasScreenCapturePermission();
  }

  /**
   * 检查设备权限状态
   */
  public getMacAuthStatus(type: 'camera'|'microphone'|'screen'): Promise<'authorized'|'denied'> {
    return this._electron.getMacAuthStatus(type);
  }

  /**
   * 请求麦克风权限
   */
  public askForMicrophoneAccess(): Promise<'authorized'|'denied'> {
    return this._electron.askForMicrophoneAccess();
  }

  /**
   * 请求摄像头权限
   */
  public askForCameraAccess(): Promise<'authorized'|'denied'> {
    return this._electron.askForCameraAccess();
  }

  /**
   * 请求屏幕分享权限
   */
  public askForScreenCaptureAccess(): Promise<'authorized'|'denied'> {
    return this._electron.askForScreenCaptureAccess();
  }

  /**
   * 获取屏幕分享屏幕采集源列表
   */
  public getScreenCaptureSources(): Promise<TScreenCaptureSourceInfo[]> {
    return new Promise((resolve) => {
      this._electron
        .getExcludeSelectWindows()
        .then((excludeWindows: string[]) => {
          const windows = [];
          const srcInfos = this._trtc.getScreenCaptureSources(320, 240, 16, 16);
          for (const item of srcInfos) {
            if (excludeWindows.includes(item.sourceId)) {
              continue;
            }
            windows.push(item);
          }
          resolve(windows);
        });
    });
  }

  /**
   * 选择要进行屏幕分享的目标采集源
   * @param source        要分享的采集源
   * @param captureMouse        是否捕获鼠标
   * @param highlightWindow        是否高亮选择区域
   */
  public selectScreenCaptureTarget(
    source: TScreenCaptureSourceInfo,
    captureMouse = true,
    highlightWindow = true,
  ): Promise<void> {
    this._info(
      'selectScreenCaptureTarget',
      JSON.stringify({
        type: source.type,
        sourceId: source.sourceId,
        sourceName: source.sourceName,
      }),
    );
    this._selectedSource = source;
    this._innerSelectScreenCaptureTarget(
      source.type,
      source.sourceId,
      source.sourceName,
      {
        left: 0,
        top: 0,
        right: 0,
        bottom: 0,
      },
      captureMouse,
      highlightWindow,
    );
    const executor = this.newExecutor(TTrtcAction.SelectScreenCaptureTarget);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 进行屏幕分享的目标采集源
   * @param source        要分享的采集源
   */
  public getScreenCaptureTarget(): TScreenCaptureSourceInfo {
    return this._selectedSource;
  }

  /**
   * 将指定窗口加入屏幕分享的排除列表中
   */
  public addExcludedShareWindows(sourceIds: string[]): Promise<void> {
    this._info('addExcludedShareWindows', JSON.stringify(sourceIds));
    for (const item of sourceIds) {
      this._trtc.addExcludedShareWindow(item);
    }
    const executor = this.newExecutor(TTrtcAction.AddExcludedShareWindows);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 将指定窗口加入屏幕分享的列表中
   */
  public addIncludedShareWindows(sourceIds: string[]): Promise<void> {
    this._info('addIncludedShareWindows', JSON.stringify(sourceIds));
    for (const item of sourceIds) {
      // TODO 在新版本中没有这个接口
      this._trtc.addIncludedShareWindow(item);
    }
    const executor = this.newExecutor(TTrtcAction.AddIncludedShareWindows);
    executor.resolve();
    return executor.getPromise();
  }
  /**
   *
   * 获取屏幕共享流
   */
  public getScreenShareStream() {}

  public setSubStreamEncoderParam(params: Partial<{
    videoResolution: TTrtcVideoResolution,
    resMode: 0 | 1,
    videoFps: number,
    videoBitrate: number,
    enableAdjustRes: boolean,
    screenCaptureMode: number,
  }>): Promise<void> {
    return Promise.reject(new TCICError(-1, 'not support', 'electron not support'));
  }

  /**
   * 开始屏幕分享
   * @param dom           用于渲染分享画面的DOM节点，不需要渲染可以忽略
   */
  public startScreenShare(dom?: HTMLElement): Promise<void> {
    this._info('startScreenShare');
    return new Promise<void>((resolve, reject) => {
      this._innerStartScreenShare(0 as any, dom)
        .then(() => {
          // this._setState(TMainState.Screen_Share, 0);
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  // 【屏幕暂停恢复说明注释】
  // 是因为 trtc 的 onScreenCapturePaused 和 onScreenCaptureResumed 事件触发靠不住
  // mac 下最小化窗口和还原窗口不触发，在 class.ts 中双端统一使用系统接口来判断共享应用是否最小化
  // 此时如果多次触发 pause、resume trtc 就不会多次通知，这里统一认为成功

  /**
   * 暂停屏幕分享
   */
  public pauseScreenShare(): Promise<void> {
    this._info('pauseScreenShare');
    return new Promise<void>((resolve, reject) => {
      this._innerPauseScreenShare()
        .then(() => {
          this._setState(TMainState.Screen_Share, 1);
          resolve();
        })
        .catch((error) => {
          // reject(error);

          // 这里之所以不reject，见上方 【屏幕暂停恢复说明注释】
          this._setState(TMainState.Screen_Share, 1);
          resolve();
        });
    });
  }

  /**
   * 恢复屏幕分享
   */
  public resumeScreenShare(): Promise<void> {
    this._info('resumeScreenShare');
    return new Promise<void>((resolve, reject) => {
      this._innerResumeScreenShare()
        .then(() => {
          this._setState(TMainState.Screen_Share, 0);
          resolve();
        })
        .catch((error) => {
          // reject(error);

          // 这里之所以不reject，见上方 【屏幕暂停恢复说明注释】

          this._setState(TMainState.Screen_Share, 0);
          resolve();
        });
    });
  }

  /**
   * 停止屏幕分享
   */
  public stopScreenShare(): Promise<void> {
    this._info('stopScreenShare');
    return new Promise<void>((resolve, reject) => {
      this._innerStopScreenShare()
        .then(() => {
          // this._setState(TMainState.Screen_Share, 2);
          // TEvent.instance.notify(TTrtcEvent.Screen_Share_Stopped, {}, false);
          resolve();
        })
        .catch((error) => {
          reject(error);
        });
    });
  }

  /**
   * 控制是否分享系统声音
   * @param enable        是否分享
   */
  public enableSystemAudioLoopback(enable: boolean): Promise<void> {
    this._info(
      'enableSystemAudioLoopback',
      JSON.stringify({
        enable,
      }),
    );
    this._enableMixSystemAudio = enable;
    if (enable) {
      this._trtc.startSystemAudioLoopback();
    } else {
      this._trtc.stopSystemAudioLoopback();
    }
    const executor = this.newExecutor(TTrtcAction.EnableSystemAudioLoopback);
    executor.resolve();
    return executor.getPromise();
  }

  /**
   * 播放背景音乐
   */
  public startMusic(documentId: string, url: string): Promise<boolean> {
    if (this.getExecutorList(TTrtcAction.StartMusic).length > 0) {
      this._info('startMusic_conflict');
      return Promise.reject(new TCICError(-1, i18next.t('正在加载音频资源，请勿频繁操作')));
    }

    this._lastMusicId += 1;
    const playId = this._lastMusicId;

    const executor = this.newExecutor(TTrtcAction.StartMusic, true, 60000);
    if (!executor.isPending()) {
      this._info('startMusic_executorConflict');
      return executor.getPromise();
    }

    window.Electron.downloadMusic({
      documentId,
      url,
    })
      .then((path: string) => {
        this._info(
          'downloadMusic',
          JSON.stringify({
            path,
            id: playId,
          }),
        );
        if (!executor.isPending()) {
          // 下载过程中，停止播放
          this._info(
            'downloadMusic_cancelled',
            JSON.stringify({
              path,
              id: playId,
            }),
          );

          return;
        }
        const trtcDefine = (window as any).TRTCDefine;
        const param = new trtcDefine.AudioMusicParam();
        param.id = playId;
        param.path = path;
        param.publish = true;
        this._info('startMusic', JSON.stringify(param));
        this._trtc.startPlayMusic(param, {
          onStart: (id: number, errCode: number) => {
            this._info(
              'startMusic_onStart',
              JSON.stringify({
                id,
                errCode,
              }),
            );
            if (!executor.isPending()) {
              this._info('startMusic_cancelled', JSON.stringify({ id: playId }));
              this._trtc.stopPlayMusic(playId);
              return;
            }
            this._currentMusicParams.id = id;
            this._currentMusicParams.path = path;
            this._setState(TMainState.Music_Play, 3);
            executor.resolve(errCode === 0 && id);
          },
          onPlayProgress: (id: number, current: number, duration: number) => {
            TEvent.instance.notify(TTrtcEvent.Music_Progress, {
              id,
              current,
              duration,
            });
          },
          onComplete: (id: number, errCode: number) => {
            this._info(
              'startMusic_onComplete',
              JSON.stringify({
                id,
                errorCode: errCode,
              }),
            );
            TEvent.instance.notify(TTrtcEvent.Music_Complete, {
              id,
              errorCode: errCode,
            });
            this._setState(TMainState.Music_Play, 2);
          },
        });
      })
      .catch((error: any) => {
        executor.reject(error);
        this._reportEvent('start_music', error);
      });
    return executor.getPromise();
  }

  /**
   * 停止背景音乐
   */
  public stopMusic(): void {
    if (this.getExecutorList(TTrtcAction.StartMusic).length > 0) {
      this.rejectExecutor(TTrtcAction.StartMusic, new TCICError(-10, i18next.t('播放音频被取消')), true);
    }
    this._info('stopMusic', JSON.stringify(this._currentMusicParams));
    this._trtc.stopPlayMusic(this._currentMusicParams.id);
    this._setState(TMainState.Music_Play, 2);
  }

  /**
   * 暂停背景音乐
   */
  public pauseMusic(): void {
    this._info(
      'pauseMusic',
      JSON.stringify(JSON.stringify(this._currentMusicParams)),
    );
    this._trtc.pausePlayMusic(this._currentMusicParams.id);
    this._setState(TMainState.Music_Play, 2);
  }

  /**
   * 恢复背景音乐
   */
  public resumeMusic(): void {
    this._info(
      'resumeMusic',
      JSON.stringify(JSON.stringify(this._currentMusicParams)),
    );
    this._trtc.resumePlayMusic(this._currentMusicParams.id);
    if (this._currentMusicParams.id) {
      this._setState(TMainState.Music_Play, 3);
    }
  }

  /**
   * 获取音乐时长，单位毫秒
   */
  public getMusicDuration(): number {
    const duration = this._trtc.getMusicDurationInMS(this._currentMusicParams.path);
    this._info(
      'getMusicDuration',
      JSON.stringify({
        id: this._currentMusicParams.id,
        path: this._currentMusicParams.path,
        duration,
      }),
    );
    return duration;
  }

  /**
   * 设置背景音乐进度
   */
  public seekMusic(pts: number): void {
    this._trtc.seekMusicToPosInTime(this._currentMusicParams.id, pts);
  }

  /**
   * 设置背景音乐的音量大小
   */
  public setMusicVolume(volume: number): void {
    this._trtc.setAllMusicVolume(volume);
  }

  /**
   * 加载视频
   */
  public loadVideo(dom: HTMLElement, url: string): Promise<void> {
    this._info(
      'loadVideo',
      JSON.stringify({
        url,
      }),
    );
    return new Promise<void>((resolve, reject) => {
      window.Electron.loadVideo(dom, url)
        .then((ret: any) => {
          this._innerSelectScreenCaptureTarget(
            TScreenCaptureSourceType.Window,
            ret.sourceId,
            '',
            {
              left: 0,
              top: 0,
              right: 0,
              bottom: 0,
            },
            false,
            false,
          );
          this._innerEnableSystemAudioLoopback(true).then();
          this._innerStartScreenShare(dom)
            .then(() => {
              this._setState(TMainState.Vod_Play, 0);
              resolve();
            })
            .catch((error) => {
              this._reportEvent('load_video', error);
              reject(error);
            });
        })
        .catch((error: any) => {
          this._reportEvent('load_video', error);
          reject(new TCICError(-1, ''));
        });
    });
  }
  /**
   * 开始播放视频
   */
  public playVideo(): Promise<void> {
    this._info('playVideo');
    return new Promise<void>((resolve, reject) => {
      window.Electron.playVideo()
        .then(() => {
          resolve();
        })
        .catch((error: any) => {
          this._reportEvent('play_video', error);
          reject(error);
        });
    });
  }

  /**
   * 暂停播放视频
   */
  public pauseVideo(): Promise<void> {
    this._info('pauseVideo');
    return window.Electron.pauseVideo();
  }

  /**
   * 视频进度跳转
   */
  public seekVideo(time: number): Promise<void> {
    this._info(
      'seekVideo',
      JSON.stringify({
        time,
      }),
    );
    window.Electron.seekVideo(time);
    return Promise.resolve();
  }

  /**
   * 结束播放视频
   */
  public stopVideo(): Promise<void> {
    this._info('stopVideo');
    return new Promise<void>((resolve, reject) => {
      window.Electron.stopVideo()
        .then(() => {
          this._innerStopScreenShare()
            .then(() => {
              this._setState(TMainState.Vod_Play, 2);
              resolve();
            })
            .catch((error) => {
              this._setState(TMainState.Vod_Play, 2);
              resolve();
            });
        })
        .catch(() => {
          reject(new TCICError(-1, ''));
        });
    });
  }

  /**
   * 设置音量大小
   */
  public setVideoVolume(volume: number): Promise<void> {
    return window.Electron.setVideoVolume(volume);
  }

  /**
   * 打开辅助摄像头
   */
  public startSubCamera(
    dom: HTMLElement,
    deviceIndex: number,
    resolution: TTrtcVideoResolution,
  ): Promise<void> {
    const videoSize = getVideoSizeFromResolution(resolution);
    this._info(
      'startSubCamera',
      JSON.stringify({
        deviceIndex,
        resolution: videoSize,
      }),
    );
    return new Promise<void>((resolve, reject) => {
      window.Electron.startSubCamera({
        deviceIndex,
        width: videoSize.width,
        height: videoSize.height,
      })
        .then((ret: any) => {
          this._innerSelectScreenCaptureTarget(
            TScreenCaptureSourceType.Window,
            ret.sourceId,
            '',
            {
              left: 10,
              top: 0,
              right: 10,
              bottom: 0,
            },
            false,
            false,
          );
          this._innerEnableSystemAudioLoopback(false).then();
          this._innerStartScreenShare(dom)
            .then(() => {
              this._setState(TMainState.Sub_Camera, 0);
              resolve();
            })
            .catch((error) => {
              reject(error);
            });
        })
        .catch((error: any) => {
          reject(error);
        });
    });
  }

  /**
   * 关闭辅助摄像头
   */
  public stopSubCamera(): Promise<void> {
    this._info('stopSubCamera');
    return new Promise<void>((resolve, reject) => {
      window.Electron.stopSubCamera()
        .then(() => {
          this._innerStopScreenShare()
            .then(() => {
              this._setState(TMainState.Sub_Camera, 2);
              resolve();
            })
            .catch((error) => {
              reject(error);
            });
        })
        .catch((error: any) => {
          reject(error);
        });
    });
  }
  public setRemoteVideoParams(option: TTrtcLocalVideoParams): Promise<void> {
    throw new Error('Method not implemented.');
  }
  // *******************************************美颜相关*******************************************
  /**
   * 设置虚拟背景
   */
  public async setVirtualImg(enable: boolean, url: string, sceneKey: string): Promise<void> {
    // return Promise.reject(new TCICError(-1, 'not support', i18next.t('系统不支持')));
    try {
      const sdk = this._electron.beautySdk;
      sdk.setTrtc(this._trtc);
      await sdk.setVirtualImg(enable, url, sceneKey);
    } catch (error) {
      console.log('%c [ error ]-1642', 'font-size:13px; background:pink; color:#bf2c9f;', error);
    }
  }
  /**
   * 设置美颜
   * @param beauty  美颜度( 0 - 10，推荐为 5 )
   * @param brightness 	明亮度( 0 - 10，推荐为 5 )
   * @param ruddy 红润度( 0 - 10，推荐为 5 )
   */
  public setBeautyParam(
    beauty: number,
    brightness: number,
    ruddy: number,
  ): Promise<void> {
    return this._trtc.setBeautyStyle(
      (window as any).TRTCDefine.TRTCBeautyStyle.TRTCBeautyStyleNature,
      beauty > 9 ? 9 : beauty,
      brightness > 9 ? 9 : brightness,
      ruddy > 9 ? 9 : ruddy,
    ) as any;
  }
  // todo electron 暂不支持
  public async setAvatar(effectId: string, url: string): Promise<void> {
    return Promise.reject();
  }

  // *******************************************降噪相关*******************************************
  /**
   * 检查当前平台是否支持AI降噪
   */
  public isAIDenoiseSupported(): boolean {
    return true;
  }

  /**
   * 开启AI降噪
   * @param enable    是否开启
   */
  public enableAIDenoise(enable: boolean): Promise<void> {
    this._info('enableAIDenoise', `enable ${enable}`);
    this._trtc.callExperimentalAPI(JSON.stringify({
      api: 'enableAIDenoise',
      params: {
        enable,
      },
    }));
    this._trtc.callExperimentalAPI(JSON.stringify({
      api: 'setAudioAINSStyle',
      params: {
        style: 2,
      },
    }));
    return Promise.resolve(undefined);
  }

  /**
   * 获取类型
   * @protected
   */
  protected _getClassName() {
    return 'TTrtcElectron';
  }

  /**
   * 设置 trtc 回调
   * @private
   */
  private _setTrtcCallback() {
    // 进房回调
    this._trtc.on('onEnterRoom', (result: number) => {
      if (result < 0) {
        const error = new TCICError(result, '');
        this.rejectPromise(TTrtcAction.Join, error);
      } else {
        this._publishStatus.join = true;
        this._updatePublishState(TMainState.Video_Publish);
        console.log('===>>tTRTC::onEnterRoom=====');
        this._updatePublishState(TMainState.Audio_Publish);
        this.resolvePromise(TTrtcAction.Join);
      }
    });
    // 退房回调
    this._trtc.on('onExitRoom', (reason: number) => {
      this._publishStatus.join = false;
      this._updatePublishState(TMainState.Video_Publish);
      console.log('===>>tTRTC::onExitRoom=====');
      this._updatePublishState(TMainState.Audio_Publish);
      this.resolvePromise(TTrtcAction.Quit, reason);
    });
    // 屏幕分享窗口被遮挡无法正常捕获时, 可能是共享的某个应用的小窗口
    this._trtc.on('onScreenCaptureCovered', () => {
      this._info('onScreenCaptureCovered');
      // this._setState(TMainState.Screen_Share, 0);
      // this.resolvePromise(TTrtcAction.StartScreenShare, undefined, '', true);
    });

    // 屏幕分享开始回调
    this._trtc.on('onScreenCaptureStarted', () => {
      this._info(
        'onScreenCaptureStarted',
        `_screenStatus ${this._screenStatus}`,
      );
      this.bindStartScreenEvents();

      // 用于通过 web 来切换可点击区域的模式
      this.setClickableAreaDetectMode('screenCapture');
      // TODO: mac 第二屏无效
      // this.setClickableAreaDetectMode('domRect');

      this._screenStatus = 'started';
      this._setState(TMainState.Screen_Share, 0);
      this.resolvePromise(TTrtcAction.StartScreenShare, undefined, '', true);
    });

    // 屏幕分享暂停与恢复回调，文档不准，参考下面具体说明
    // https://web.sdk.qcloud.com/trtc/electron/doc/zh-cn/trtc_electron_sdk/TRTCCallback.html#event:onScreenCapturePaused

    // reason 说明
    // 0：用户主动暂停。
    // 1：注意此字段的含义在 MAC 和 Windows 平台有稍微差异。屏幕窗口不可见暂停（Mac）。表示设置屏幕分享参数导致的暂停（Windows）。
    // 2：表示屏幕分享窗口被最小化导致的暂停（仅 Windows）。
    // 3：表示屏幕分享窗口被隐藏导致的暂停（仅 Windows）。

    // 下面两个trtc监听目前不能稳定生效，通过mac和win系统原生接口来实现

    // 屏幕分享暂停回调
    this._trtc.on('onScreenCapturePaused', (reason: number) => {
      this._info('trtc onScreenCapturePaused', `reason: ${reason}`);

      if (this._enableMixSystemAudio) {
        this._trtc.stopSystemAudioLoopback();
      }
      this.resolvePromise(TTrtcAction.PauseScreenShare, undefined, '', true);
    });

    // 屏幕分享恢复回调
    this._trtc.on('onScreenCaptureResumed', (reason: number) => {
      this._info('trtc onScreenCaptureResumed', `reason: ${reason}`);

      if (this._enableMixSystemAudio) {
        this._trtc.startSystemAudioLoopback();
      }
      this.resolvePromise(TTrtcAction.ResumeScreenShare, undefined, '', true);
    });

    // 屏幕分享停止回调
    this._trtc.on('onScreenCaptureStopped', () => {
      this._info(
        'onScreenCaptureStopped',
        `_screenStatus ${this._screenStatus}`,
      );
      this.unbindStartScreenEvents();
      this._screenStatus = 'stopped';
      // TMain.instance.isWin32Electron() &&
      const shouldDelayLayout = TMain.instance.isFeatureAvailable('ScreenShareAdvanceMode');
      setTimeout(() => {
        this._warn(
          'onScreenCaptureStopped-setState',
          `status:${this._screenStatus}`,
        );
        // 这里受Browserview的bug影响可能是渲染的竞态冲突造成全局共享屏幕时白屏现象
        this._setState(TMainState.Screen_Share, 2);
      },  shouldDelayLayout ? 1 : 0);
      TEvent.instance.notify(TTrtcEvent.Screen_Share_Stopped, {}, false);
      this.resolvePromise(TTrtcAction.StopScreenShare, undefined, '', true);
    });
    // 截图完成回调
    this._trtc.on(
      'onSnapshotComplete',
      (
        userId: string,
        type: any,
        buffer: any,
        width: number,
        height: number,
      ) => {
        const snapshot = new TTrtcSnapshot();
        snapshot.data = buffer;
        snapshot.width = width;
        snapshot.height = height;
        this.resolvePromise(TTrtcAction.SnapshotVideo, snapshot);
      },
    );
    // 切换角色回调
    this._trtc.on('onSwitchRole', (errCode: number, errMsg: string) => {
      if (errCode === 0) {
        this.resolvePromise(TTrtcAction.SwitchRole);
      } else {
        const error = new TCICError(
          errCode,
          i18next.t('切换用户角色遇到一些问题'),
        );
        this.rejectPromise(TTrtcAction.SwitchRole, error);
      }
    });
    // 摄像头就绪回调
    this._trtc.on('onCameraDidReady', () => {
      this._currentCameraId = this._trtc.getCurrentCameraDevice().deviceId;
      this._info(
        'onCameraDidReady',
        `current: ${this._currentCameraId}, test: ${this._cameraTestStarted}, local: ${this._cameraStarted}, timestamp: ${Date.now()}`,
      );
      // 没有权限时也会走到这里，然后收到 onError，具体原因待确认
      // 先兼容处理，加个延迟，延迟期间没收到onError就认为成功，用 1000 是因为自测时 onCameraDidReady 和 onError 相差最多有 700+ ms
      setTimeout(() => {
        // processCameraResult 里会检查 _cameraTestStarted 和 _cameraStarted
        this._processCameraResult(true, null, 'onCameraDidReady');
      }, 1000);
    });
    // 麦克风就绪回调
    this._trtc.on('onMicDidReady', () => {
      this._currentMicId = this._trtc.getCurrentMicDevice().deviceId;
      this._info(
        'onMicDidReady',
        `current: ${this._currentMicId}, test: ${this._micTestStarted}, local: ${this._micStarted}, timestamp: ${Date.now()}`,
      );
      this._processMicResult(true, null, 'onMicDidReady');
    });
    // 用户音频上行是否开启回调
    this._trtc.on(
      'onUserAudioAvailable',
      (userId: string, available: number) => {
        TEvent.instance.notify(TTrtcEvent.Audio_Changed, {
          userId,
          available,
        });
      },
    );
    // 用户视频上行是否开启回调
    this._trtc.on(
      'onUserVideoAvailable',
      (userId: string, available: number) => {
        TEvent.instance.notify(TTrtcEvent.Video_Changed, {
          userId,
          available,
        });
      },
    );
    // 用户辅路上行是否开启回调
    this._trtc.on(
      'onUserSubStreamAvailable',
      (userId: string, available: number) => {
        TEvent.instance.notify(TTrtcEvent.SubStream_Changed, {
          userId,
          available,
        });
        // TODO 监测用户辅路流用于渲染
      },
    );
    // 用户辅路流首帧加载成功
    this._trtc.on('onFirstVideoFrame', (userId: string) => {
      TEvent.instance.notify(TTrtcEvent.SubStream_Loaded, {
        userId,
        available: true,
      });
    });
    // 网络质量回调
    this._trtc.on(
      'onNetworkQuality',
      (localQuality: any, remoteQuality: any) => {
        let average = 1;
        if (remoteQuality.length > 0) {
          // 下行网络质量由所有人的下行状态平均值决定
          average = remoteQuality.reduce(
            (acc: number, value: any) => acc + value.quality,
            0,
          ) / remoteQuality.length;
        }
        TEvent.instance.notify(
          TTrtcEvent.Network_Quality,
          {
            uplinkNetworkQuality: localQuality.quality,
            downlinkNetworkQuality: average,
          },
          false,
        );
      },
    );
    // 技术指标统计回调
    this._trtc.on('onStatistics', (param: any) => {
      const infos: any[] = [];
      const handleRtcInfo = (userId: string, item: any) => {
        if (item.audioBitrate !== 0 || item.videoBitrate !== 0) {
          let streamType = '';
          let resolution = '0x0';
          if (item.audioBitrate !== 0) {
            streamType = 'audio';
            resolution = '0x0';
          }
          if (item.videoBitrate !== 0) {
            streamType = item.streamType === 2 ? 'sub' : 'main';
            resolution = `${item.width}x${item.height}`;
          }
          infos.push({
            UserId: userId,
            StreamType: streamType,
            Resolution: resolution,
          });
        }
      };
      const trtcStatistics = new TTrtcStatistics();
      trtcStatistics.upLoss = param.upLoss;
      trtcStatistics.downLoss = param.downLoss;
      trtcStatistics.upVideoPixels = 0;
      trtcStatistics.downVideoPixels = 0;
      trtcStatistics.appCpu = param.appCpu;
      trtcStatistics.systemCpu = param.systemCpu;
      trtcStatistics.rtt = param.rtt;
      trtcStatistics.remoteStatisticsArray = [];
      param.localStatisticsArray.forEach((item: any) => {
        handleRtcInfo(TSession.instance.getUserId(), item);
        trtcStatistics.upVideoPixels += item.width * item.height;
      });
      param.remoteStatisticsArray.forEach((item: any) => {
        handleRtcInfo(item.userId, item);
        if (
          item.width
          && item.height
          && item.frameRate
          && !item.userId.startsWith('tic_push_user')
        ) {
          trtcStatistics.downVideoPixels += item.width * item.height;
        }
        // 远端网络统计数据
        const remoteStatistics = new TTrtcRemoteStatistics();
        remoteStatistics.userId = item.userId;
        remoteStatistics.upLoss = Math.max(item.finalLoss - param.downLoss, 0);
        trtcStatistics.remoteStatisticsArray.push(remoteStatistics);
      });
      this._rtcInfos = infos;
      TEvent.instance.notify(TTrtcEvent.Network_Statistis, trtcStatistics);
    });
    this._trtc.on(
      'onUserVoiceVolume',
      (infos: any, count: number, total: number) => {
        for (const item of infos) {
          let userId = item.userId;
          const volume = item.volume;
          if (userId === '') {
            // 空字符串为用户自己
            userId = this._userId;
          }
          TEvent.instance.notify(
            TTrtcEvent.Volume_Update,
            {
              userId,
              volume,
            },
            false,
          );
        }
      },
    );
    this._trtc.on('onTestMicVolume', (volume: number) => {
      TEvent.instance.notify(
        TTrtcEvent.Volume_Update,
        {
          userId: this._userId,
          volume,
        },
        false,
      );
    });
    // 错误回调
    this._trtc.on('onError', (errCode: number, errMsg: string) => {
      this._error('onError', `errCode: ${errCode}, errMsg: ${errMsg}, timestamp: ${Date.now()}`);
      // 摄像头错误
      if (errCode === -1301 || errCode === -1314 || errCode === -1316) {
        this._processCameraResult(false, { errCode, errMsg }, 'onError');
        return;
      }
      // 麦克风错误
      if (errCode === -1302 || errCode === -1317 || errCode === -1319) {
        this._processMicResult(false, { errCode, errMsg }, 'onError');
        return;
      }
      if (errCode === -1320) {
        const showMsg = getNativeErrorInfo(errCode)?.errMsg || i18next.t('关闭麦克风失败');
        this.rejectExecutor(
          TTrtcAction.StopLocalAudio,
          new TCICError(errCode, showMsg, errMsg),
          true,
        );
        return;
      }
      // 扬声器错误
      if (errCode === -1321) {
        const showMsg = getNativeErrorInfo(errCode)?.errMsg || i18next.t('打开扬声器失败');
        // TODO 扬声器没有 TMainState.xxx ？
        this.rejectExecutor(
          TTrtcAction.StartSpeakerTest,
          new TCICError(errCode, showMsg, errMsg),
          true,
        );
        return;
      }
      if (errCode === -1323) {
        const showMsg = getNativeErrorInfo(errCode)?.errMsg || i18next.t('关闭扬声器失败');
        this.rejectExecutor(
          TTrtcAction.StopSpeakerTest,
          new TCICError(errCode, showMsg, errMsg),
          true,
        );
        return;
      }
      // 屏幕分享错误
      if (
        errCode === -1308
        || errCode === -1309
        || errCode === -102015
        || errCode === -102016
        || errCode === -7001
      ) {
        const showMsg = getNativeErrorInfo(errCode)?.errMsg || i18next.t('屏幕共享失败');
        this._error('onScreenShareError', `errCode: ${errCode}, errMsg: ${errMsg}, _screenStatus ${this._screenStatus}`);
        this._screenStatus = 'startError';
        this._setState(TMainState.Screen_Share, 2);
        TEvent.instance.notify(TTrtcEvent.Screen_Share_Stopped, {}, false);
        this.rejectExecutor(
          TTrtcAction.StartScreenShare,
          new TCICError(errCode, showMsg, errMsg),
          true,
        );
        return;
      }
      // 进房失败
      if (
        errCode === -3301
        || errCode === -3316
        || errCode === -3317
        || errCode === -3318
        || errCode === -3319
        || errCode === -3320
        || errCode === -3308
        || errCode === -100013
      ) {
        const error = new TCICError(errCode, i18next.t('加入音视频房间失败'), errMsg);
        this.rejectPromise(TTrtcAction.Join, error);
        return;
      }
      this._error('onUnknownError', `errCode: ${errCode}, errMsg: ${errMsg}`);
    });
    // 警告回调，例如出现了卡顿或可恢复的解码失败
    // https://cloud.tencent.com/document/product/647/38308#6424a85bafc29e2318bda3836abfc531
    // https://cloud.tencent.com/document/product/647/38552
    this._trtc.on('onWarning', (warningCode: number, warningMsg: string) => {
      // 确认不用处理的，太多了不上报
      if (warningCode === 1104 || warningCode === 2101) {
        return;
      }
      this._warn(
        'onWarning',
        `warningCode: ${warningCode}, warningMsg: ${warningMsg}, timestamp: ${Date.now()}`,
      );
      // 摄像头警告
      if (warningCode === 1111 || warningCode === 1112) {
        this._processCameraResult(false, { errCode: warningCode, errMsg: warningMsg }, 'onWarning');
        return;
      }
      // 麦克风警告
      if (
        warningCode === 1201
        || warningCode === 1203
        || warningCode === 1204
      ) {
        this._processMicResult(false, { errCode: warningCode, errMsg: warningMsg }, 'onWarning');
        return;
      }
      // 扬声器警告
      if (warningCode === 1202 || warningCode === 1205) {
        const showMsg = getNativeErrorInfo(warningCode)?.errMsg
          || i18next.t('打开扬声器失败');
        // TODO 扬声器没有 TMainState.xxx ？
        this.rejectExecutor(
          TTrtcAction.StartSpeakerTest,
          new TCICError(warningCode, showMsg, warningMsg),
          true,
        );
        return;
      }
    });
    // 网络断开回调
    this._trtc.on('onConnectionLost', () => {
      this._setState(TMainState.Network_Broken, true);
    });
    // 网络恢复回调
    this._trtc.on('onConnectionRecovery', () => {
      this._setState(TMainState.Network_Broken, false);
    });
    // 设备检测
    navigator.mediaDevices.addEventListener('devicechange', (event) => {
      let tryTimes = 0;
      const handleChange = (lastTry = false) => {
        this._getDevices().then((devices: any) => {
          if (JSON.stringify(this._devices) === JSON.stringify(devices)) {
            // 设备列表没有变化
            console.log('deviceChange', 'no change');
            // navigator.mediaDevices获取的设备ID和trtc获取设备列表的设备ID不一致，所以需要通过trtc接口获取设备列表进行比对。
            // 由于trtc更新设备列表可能会有延迟，如果收到设备变化通知，但是获取的设备列表没有变更，会尝试多次获取。
            if (!lastTry && tryTimes < 2) {
              setTimeout(() => {
                handleChange();
              }, 1000);
              tryTimes = tryTimes + 1;
            }
            return;
          }
          this._info(
            'deviceChange',
            `old: ${JSON.stringify(this._devices)}\nnew: ${JSON.stringify(devices)}`,
          );
          const oldDevices = this._devices;
          this._devices = devices;

          const notifyDevices: any[] = [];
          // 设备移除
          oldDevices.forEach((oldDevice: any) => {
            // 原有设备在新的列表中找不到，就是删除的设备
            if (
              !devices.find((device: any) => device.deviceId === oldDevice.deviceId)
            ) {
              // 如果拔掉当前使用的设备，则停止采集
              if (oldDevice.type === TTrtcDeviceType.Mic) {
                if (oldDevice.deviceId === this._currentMicId) {
                  const currentDevice = this._trtc.getCurrentMicDevice();
                  if (
                    currentDevice.deviceId !== ''
                    && currentDevice.deviceId !== this._currentMicId
                  ) {
                    // 如果已经自动切换，则不停止采集
                    this._info('deviceChange', `remove current mic ${JSON.stringify(oldDevice)}, already changed to ${JSON.stringify(currentDevice)}`);
                    this._currentMicId = currentDevice.deviceId;
                  } else {
                    this._info('deviceChange', `remove current mic ${JSON.stringify(oldDevice)}`);
                    this.stopMicTest().then();
                    this.stopLocalAudio().then();
                  }
                } else {
                  this._info('deviceChange', `remove mic ${JSON.stringify(oldDevice)}, not current mic ${this._currentMicId}`);
                }
              } else if (oldDevice.type === TTrtcDeviceType.Camera) {
                if (oldDevice.deviceId === this._currentCameraId) {
                  const currentDevice = this._trtc.getCurrentCameraDevice();
                  if (
                    currentDevice.deviceId !== ''
                    && currentDevice.deviceId !== this._currentCameraId
                  ) {
                    // 如果已经自动切换，则不停止采集
                    this._info('deviceChange', `remove current camera ${JSON.stringify(oldDevice)}, already changed to ${JSON.stringify(currentDevice)}`);
                    this._currentCameraId = currentDevice.deviceId;
                  } else {
                    this._info('deviceChange', `remove current camera ${JSON.stringify(oldDevice)}`);
                    this.stopCameraTest().then();
                    this.stopLocalVideo().then();
                  }
                } else {
                  this._info('deviceChange', `remove camera ${JSON.stringify(oldDevice)}, not current camera ${this._currentCameraId}`);
                }
              } else {
                console.log('deviceChange', 'remove other device', oldDevice);
              }
              notifyDevices.push({
                deviceId: oldDevice.deviceId,
                type: oldDevice.type,
                state: TTrtcDeviceState.Remove,
              });
            }
          });
          // 设备增加
          devices.forEach((newDevice: any) => {
            // 新的设备在原有列表中找不到，就是新增的设备
            if (
              !oldDevices.find((device: any) => device.deviceId === newDevice.deviceId)
            ) {
              if (newDevice.type === TTrtcDeviceType.Camera) {
                const currentDevice = this._trtc.getCurrentCameraDevice();
                if (
                  currentDevice.deviceId !== ''
                  && currentDevice.deviceId !== this._currentCameraId
                ) {
                  // 自动切换了设备
                  this._info('deviceChange', `add camera ${JSON.stringify(newDevice)}, already changed to ${JSON.stringify(currentDevice)}`);
                  this._currentCameraId = currentDevice.deviceId;
                } else {
                  this._info('deviceChange', `add camera ${JSON.stringify(newDevice)}, current camera ${this._currentCameraId}`);
                }
              } else if (newDevice.type === TTrtcDeviceType.Mic) {
                const currentDevice = this._trtc.getCurrentMicDevice();
                if (
                  currentDevice.deviceId !== ''
                  && currentDevice.deviceId !== this._currentMicId
                ) {
                  // 自动切换了设备
                  this._info('deviceChange', `add mic ${JSON.stringify(newDevice)}, already changed to ${JSON.stringify(currentDevice)}`);
                  this._currentMicId = currentDevice.deviceId;
                } else {
                  this._info('deviceChange', `add mic ${JSON.stringify(newDevice)}, current mic ${this._currentMicId}`);
                }
              } else {
                console.log('deviceChange', 'add other device', newDevice);
              }
              notifyDevices.push({
                deviceId: newDevice.deviceId,
                type: newDevice.type,
                state: TTrtcDeviceState.Add,
              });
            }
          });

          // 更新是否notfound
          this._updateDeviceStatusByDeviceList('deviceChange');

          // 设置默认设备
          if (this._trtc.getCurrentCameraDevice().deviceId === '') {
            const defualtCamera = devices.find((device: any) => device.type === TTrtcDeviceType.Camera);
            if (defualtCamera) {
              this._info(
                'deviceChange',
                `set default camera start: ${JSON.stringify(defualtCamera)}`,
              );
              this.switchCamera(defualtCamera.deviceId).then(() => {
                this._info('deviceChange', `set default camera end, now _currentCameraId: ${this._currentCameraId}`);
              });
            } else {
              const deviceStatus = TState.instance.getState(TMainState.Video_Device_Status);
              const isAbnormal = isDeviceAbnormal(deviceStatus);
              const newDeviceStatus = isAbnormal ? deviceStatus : TDeviceStatus.Not_Found;
              this._info('deviceChange', `no camera device, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}, newDeviceStatus ${newDeviceStatus}`);
              this._setState(TMainState.Video_Device_Status, newDeviceStatus);
            }
          }
          if (this._trtc.getCurrentMicDevice().deviceId === '') {
            const defaultMic = devices.find((device: any) => device.type === TTrtcDeviceType.Mic);
            if (defaultMic) {
              this._info(
                'deviceChange',
                `set default mic start: ${JSON.stringify(defaultMic)}`,
              );
              this.switchMic(defaultMic.deviceId).then(() => {
                this._info('deviceChange', `set default mic end, now _currentMicId: ${this._currentMicId}`);
              });
            } else {
              const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
              const isAbnormal = isDeviceAbnormal(deviceStatus);
              const newDeviceStatus = isAbnormal ? deviceStatus : TDeviceStatus.Not_Found;
              this._info('deviceChange', `no mic device, deviceStatus ${deviceStatus}, isAbnormal ${isAbnormal}, newDeviceStatus ${newDeviceStatus}`);
              this._setState(TMainState.Audio_Device_Status, newDeviceStatus);
            }
          }
          // 通知设备变化
          notifyDevices.forEach((device: any) => {
            TEvent.instance.notify(TTrtcEvent.Device_Changed, device);
          });
        });
      };
      setTimeout(() => {
        handleChange();
      }, 1000);
      // navigator.mediaDevices.addEventListener('devicechange') 存在不触发的情况
      // 1. 单摄像头设备(没有麦克风） 2. 多次快速插拔，最后拔出，过一段时间后再插入仍不触发
      // 此时主动枚举设备可以正常获取，延时多做一次设备检测
      setTimeout(() => {
        handleChange(true);
      }, 5000);
    });
    // 设备热插拔
    // this._trtc.on('onDeviceChange', (deviceId: string, type: any, state: any) => {
    //   TEvent.instance.notify(TTrtcEvent.Device_Changed, {
    //     deviceId,
    //     type,
    //     state,
    //   }, false);
    // export declare enum TRTCDeviceState {
    //   TRTCDeviceStateAdd = 0,
    //   TRTCDeviceStateRemove = 1,
    //   TRTCDeviceStateActive = 2
    // }
    // export declare enum TRTCDeviceType {
    //   TRTCDeviceTypeUnknow = -1,
    //   TRTCDeviceTypeMic = 0,
    //   TRTCDeviceTypeSpeaker = 1,
    //   TRTCDeviceTypeCamera = 2
    // }
    // });
  }

  private _getDevices(): Promise<TTrtcDeviceInfo[]> {
    return new Promise<TTrtcDeviceInfo[]>((resolve) => {
      const micPromise = this.getMics();
      const cameraPromise = this.getCameras();
      const speakerPromise = this.getSpeakers();
      Promise.all([micPromise, cameraPromise, speakerPromise]).then((values) => {
        const devices = [].concat(...values);
        resolve(devices);
      });
    });
  }
  // setMicTestAction
  private _startMicTest(dom?: HTMLElement): Promise<void> {
    this._info('startMicTest', 'set micTestStarted');
    this._micTestStarted = true;
    const executor = this.newExecutor(TTrtcAction.StartMicTest, true, 5000);
    if (this._trtc.getMicDevicesList().length === 0) {
      this._info('startMicTest', 'no mic device');
      this._processMicResult(false, { errCode: 1201, errMsg: '' }, 'startMicTest but no mic device');
    } else {
      this._info('startMicTest', 'trtc.startMicDeviceTest');
      this._trtc.startMicDeviceTest(200);
    }
    return executor.getPromise();
  }
  private async _startCameraTest(dom?: HTMLElement): Promise<void> {
    this._info('startCameraTest', 'set cameraTestStarted');
    this._cameraTestStarted = true;
    const executor = this.newExecutor(TTrtcAction.StartCameraTest, false, 5000);
    if (this._trtc.getCameraDevicesList().length === 0) {
      this._info('startCameraTest', 'no camera device');
      this._processCameraResult(false, { errCode: 1111, errMsg: '' }, 'startCameraTest but no camera device');
    } else {
      this._info('startCameraTest', 'setVideoEncoderParam');
      await this.setVideoEncoderParam(
        TTrtcVideoResolution.Resolution_1280_720,
        30,
        1200,
      );
      this._info('startCameraTest', 'trtc.startCameraDeviceTest');
      this._trtc.startCameraDeviceTest(dom);
    }
    return executor.getPromise();
  }

  // private _getNetworkLevel(loss: number) {
  //   if (loss === 0) {
  //     return 1;
  //   } else if (loss < 10) {
  //     return 2;
  //   } else if (loss < 30) {
  //     return 3;
  //   } else if (loss < 50) {
  //     return 4;
  //   } else {
  //     return 5;
  //   }
  // }

  /* ===屏幕分享trtc方法封装===*/
  /**
   * 默认是传入dom,如果指定采集方案，就使用采集方案,dom参数从第二个开始
   * @param dom domHTML
   * @param other 其它参数
   * @returns void
   */
  private _innerStartScreenShare(
    dom?: HTMLElement,
    other?: any,
  ): Promise<void> {
    this._info('innerStartScreenShare');
    /**
     * window下GDI采集方式有系统兼容问题，无法采集涂鸦窗口画面，
     * 但是放大镜采集方式采集透明窗口数据偶尔有问题，辅助摄像头/视频课件会出现灰屏。
     * strategy
     * 1 GDI，辅助摄像头/视频课件采集方案
     * 0 放大镜，屏幕分享时采集方案
     */
    let strategy: any = 1;
    if (typeof dom === 'number') {
      strategy = dom;
      dom = other;
    }

    const trtcDefine = (window as any).TRTCDefine;
    const param = new trtcDefine.TRTCVideoEncParam();
    param.videoResolution = 114;
    param.videoFps = 15;
    param.videoBitrate = 4000;
    this._trtc.callExperimentalAPI(`{"api":"setWindowCaptureStrategy", "params":{"strategy":${strategy}}}`);
    this._trtc.startScreenCapture(dom, 2, param);
    return this.newPromise(TTrtcAction.StartScreenShare, '', true, 2000);
  }
  private _innerPauseScreenShare(): Promise<void> {
    this._info('innerPauseScreenShare');
    this._trtc.pauseScreenCapture();
    return this.newPromise(TTrtcAction.PauseScreenShare, '', true, 300);
  }
  private _innerResumeScreenShare(): Promise<void> {
    this._info('innerResumeScreenShare');
    this._trtc.resumeScreenCapture();
    return this.newPromise(TTrtcAction.ResumeScreenShare, '', true, 300);
  }
  private _innerStopScreenShare(): Promise<void> {
    this._info('innerStopScreenShare', `_screenStatus ${this._screenStatus}`);
    if (!this._screenStatus || this._screenStatus === 'stopped') {
      // 已停止的不会有 onScreenCaptureStopped 回调，会导致报 StopScreenShare 超时，直接返回
      return Promise.resolve();
    }
    this._trtc.removeAllExcludedShareWindow();
    // TODO 这个在最新的版本中已经移除
    if ((this._trtc as any).removeAllIncludedShareWindow) {
      (this._trtc as any).removeAllIncludedShareWindow();
    } else {
      console.error('%c [ removeAllIncludedShareWindow missing! ]-2407', 'font-size:13px; background:pink; color:#bf2c9f;');
    }
    this.enableSystemAudioLoopback(false).then();
    this._trtc.stopScreenCapture();
    this._screenStatus = 'stopping';
    return this.newPromise(TTrtcAction.StopScreenShare, '', true, 2000).catch((err) => {
      this._error(
        'innerStopScreenShare',
        `_screenStatus ${this._screenStatus}, errCode ${
          err?.errCode
        }, errMsg ${err?.errMsg || err?.message}`,
      );
      if (this._screenStatus === 'stopping') {
        this._screenStatus = 'stopError';
      }
      throw err;
    });
  }
  private _innerEnableSystemAudioLoopback(enable: boolean): Promise<void> {
    this._info(
      'innerEnableSystemAudioLoopback',
      JSON.stringify({
        enable,
      }),
    );
    this._enableMixSystemAudio = enable;
    if (enable) {
      this._trtc.startSystemAudioLoopback();
    } else {
      this._trtc.stopSystemAudioLoopback();
    }
    const executor = this.newExecutor(TTrtcAction.EnableSystemAudioLoopback);
    executor.resolve();
    return executor.getPromise();
  }
  private _innerSelectScreenCaptureTarget(
    type: TScreenCaptureSourceType,
    sourceId: string,
    sourceName: string,
    bounds: any,
    captureMouse: boolean,
    highlightWindow: boolean,
  ): void {
    this._info(
      'innerSelectScreenCaptureTarget',
      JSON.stringify({
        type,
        sourceId,
        sourceName,
        bounds: JSON.stringify(bounds),
        captureMouse,
        highlightWindow,
      }),
    );
    this._trtc.selectScreenCaptureTarget(
      type,
      sourceId,
      sourceName,
      bounds,
      captureMouse,
      highlightWindow,
    );
  }

  // 允许通过web来控制屏幕共享时候可点击区域的检测模式，避免大规模上线后 domRect 模式有可能产生的边界情况，随时可以切回 screenCapture 模式
  private setClickableAreaDetectMode(mode: 'screenCapture' | 'domRect') {
    // 1.7.3 430 及之前的版本都没有这个方法
    if (typeof window?.Electron?.setClickableAreaDetectMode !== 'function') {
      return;
    }

    window.Electron.setClickableAreaDetectMode(mode);
  }

  private bindStartScreenEvents() {
    this?.updateClickableRects();
    // this.updateClickableRectsTimer = setInterval(() => {
    // }, 500);
  }

  private unbindStartScreenEvents() {
    clearInterval(this.updateClickableRectsTimer);
  }

  // 只清理内部数据，不处理 executor
  private _stopCameraTestInner() {
    const deviceStatus = TState.instance.getState(TMainState.Video_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._cameraTestStarted = false;
    this._currentCameraId = '';
    this._trtc.stopCameraDeviceTest();
    this._setState(TMainState.Video_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this._setState(TMainState.Video_Capture, false);
    this._updatePublishState(TMainState.Video_Publish);
  }

  // 只清理内部数据，不处理 executor
  private _stopMicTestInner() {
    const deviceStatus = TState.instance.getState(TMainState.Audio_Device_Status);
    const isAbnormal = isDeviceAbnormal(deviceStatus);
    this._micTestStarted = false;
    this._currentMicId = '';
    this._trtc.stopMicDeviceTest();
    this._setState(TMainState.Audio_Device_Status, isAbnormal ? deviceStatus : TDeviceStatus.Closed);
    this._setState(TMainState.Audio_Capture, false);
    this._updatePublishState(TMainState.Audio_Publish);
  }

  private _processCameraResult(open: boolean, err?: { errCode: number; errMsg: string; }, reason?: string) {
    this._info('processCameraResult', `${open}, _cameraStarted ${this._cameraStarted}, _cameraTestStarted ${this._cameraTestStarted}`);

    if (open) {
      // 成功
      if (this._cameraStarted || this._cameraTestStarted) {
        this._setState(TMainState.Video_Device_Status, TDeviceStatus.Open);
        this._setState(TMainState.Video_Capture, true);
        this._updatePublishState(TMainState.Video_Publish);

        this._cameraStarted && this._info('startLocalVideo', `success, reason ${reason}`);
        this.resolveExecutor(TTrtcAction.StartLocalVideo, undefined, true);
        this._cameraTestStarted && this._info('startCameraTest', `success, reason ${reason}`);
        this.resolveExecutor(TTrtcAction.StartCameraTest, undefined, true);
      }
      return;
    }

    // 失败
    const { errCode, errMsg } = err;
    const { deviceStatus, errMsg: showMsg } = getNativeErrorInfo(errCode) || { errMsg: i18next.t('打开摄像头失败') };
    this._setState(TMainState.Video_Device_Status, deviceStatus || TDeviceStatus.Fail);
    this._setState(TMainState.Video_Capture, false);
    this._currentCameraId = '';

    this._cameraStarted && this._error('startLocalVideo', `fail, errCode ${errCode}, errMsg ${errMsg}, reason ${reason}`);
    this._cameraStarted = false;
    // 不用先判断 _xxxStarted，可能还没设置就直接出错返回
    this.rejectExecutor(
      TTrtcAction.StartLocalVideo,
      new TCICError(errCode, showMsg, errMsg),
      true,
    );
    this._cameraTestStarted && this._error('startCameraTest', `fail, errCode ${errCode}, errMsg ${errMsg}, reason ${reason}`);
    this._cameraTestStarted = false;
    // 不用先判断 _xxxStarted，可能还没设置就直接出错返回
    this.rejectExecutor(
      TTrtcAction.StartCameraTest,
      new TCICError(errCode, showMsg, errMsg),
      true,
    );
  }

  private _processMicResult(open: boolean, err?: { errCode: number; errMsg: string; }, reason?: string) {
    this._info('processMicResult', `${open}, _micStarted ${this._micStarted}, _micTestStarted ${this._micTestStarted}`);

    if (open) {
      // 成功
      if (this._micStarted || this._micTestStarted) {
        this._setState(TMainState.Audio_Device_Status, TDeviceStatus.Open);
        this._setState(TMainState.Audio_Capture, true);
        this._updatePublishState(TMainState.Audio_Publish);

        this._micStarted && this._info('startLocalAudio', `success, reason ${reason}`);
        this.resolveExecutor(TTrtcAction.StartLocalAudio, undefined, true);
        this._micTestStarted && this._info('startMicTest', `success, reason ${reason}`);
        this.resolveExecutor(TTrtcAction.StartMicTest, undefined, true);
      }
      return;
    }

    // 失败
    const { errCode, errMsg } = err;
    const { deviceStatus, errMsg: showMsg } = getNativeErrorInfo(errCode) || { errMsg: i18next.t('打开麦克风失败') };
    this._setState(TMainState.Audio_Device_Status, deviceStatus || TDeviceStatus.Fail);
    this._setState(TMainState.Audio_Capture, false);
    this._currentMicId = '';

    this._micStarted && this._error('startLocalAudio', `fail, errCode ${errCode}, errMsg ${errMsg}, reason ${reason}`);
    this._micStarted = false;
    // 不用先判断 _xxxStarted，可能还没设置就直接出错返回
    this.rejectExecutor(
      TTrtcAction.StartLocalAudio,
      new TCICError(errCode, showMsg, errMsg),
      true,
    );
    this._micTestStarted && this._error('startMicTest', `fail, errCode ${errCode}, errMsg ${errMsg}, reason ${reason}`);
    this._micTestStarted = false;
    // 不用先判断 _xxxStarted，可能还没设置就直接出错返回
    this.rejectExecutor(
      TTrtcAction.StartMicTest,
      new TCICError(errCode, showMsg, errMsg),
      true,
    );
  }
}
