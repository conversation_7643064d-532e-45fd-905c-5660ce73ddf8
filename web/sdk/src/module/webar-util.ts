/* eslint-disable */
// @ts-nocheck
import sha256 from 'sha256';
import {TRequest} from "./trequest";

export class WebarUtil {
    public static getSignatureTest1(opt: any) {
        opt = opt || {
            token: '',
            APPID: '1312440575'
        };
        const {token, APPID} = opt;
        const timestamp = Math.round(new Date().getTime() / 1000);
        const signature = sha256(timestamp + token + APPID + timestamp).toUpperCase();
        return { signature, timestamp };
    }

    public static async getSignature(opt: any) {
        opt = opt || {}
        let testEnv = 'https://service-00mawu02-**********.gz.apigw.tencentcs.com/release/ar-tcic-sign';
        let env = 'https://service-fe2fu2yw-**********.gz.apigw.tencentcs.com/release/ar-tcic-sign';
        // 兼容测试环境
        let host = window.location.host
        if (host.indexOf('test-') > -1) {
            env = testEnv;
        }
        const d = await TRequest.instance.httpRequest('GET', env);
        if(d && d.data){
            const json = JSON.parse(d.data);
            return json
        }

    }

    public static getLicenseKey() {
        let testKey = 'a70c30e8be33e864c8415aab18ed7087'
        let prodKey = 'e9908a4bf4794fb184930479f3e6425e'
        let devKey =  '8d4e1b178e06f9d0782bac3619014c5a'
        try {
            let host = window.location.host
            if (host.indexOf('test-') > -1) {
                return testKey
            }
            if (host.indexOf('dev-') > -1) {
                return testKey
            }
        } catch(e) {

            console.error('getLicenseKey error', e);

        }
        return prodKey
    }

    public static async checkAndInitStream(trtcLocalStream: any) {
        if(trtcLocalStream){
            if (trtcLocalStream.__isInited__) {
                return trtcLocalStream
            } else {
                await trtcLocalStream.initialize();
                trtcLocalStream.__isInited__ = true;
                return trtcLocalStream
            }
        }
    }

    public static async timeoutPromise(promiseFn: any, defaultRtnValue: any, timeout?: number) {
        timeout = timeout || 8000; // 默认8秒超时
        const sleep = (timeout) => {
            return new Promise(((resolve, reject) => {
                setTimeout(() => {
                    console.warn('timeoutPromise done, return default value', defaultRtnValue);
                    resolve(defaultRtnValue);
                }, timeout);
            }))
        }
        const promises = [promiseFn, sleep(timeout)];
        return new Promise((resolve, reject) => {
            for (const p of promises) {
                p.then((res) => resolve(res)).catch((error) => {
                    console.error('timeoutPromise', error);
                    resolve(defaultRtnValue);
                });
            }
        });
    }
}
