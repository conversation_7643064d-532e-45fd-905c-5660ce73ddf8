import { TEvent } from '../base/tevent';
import { TModule } from '../base/tmodule';
import {
  TGetMemberListResult,
  TBusinessMember,
  TMemberInfo,
  TMemberListFilter,
  TMemberStatus,
  TMemberType, THandUpMemberInfo, THandUpListFilter,
} from './business/tbusiness_member';
import { TPermissionInfo } from './business/tbusiness_user';
import {
  TMain,
} from './tmain';
import {
  TMainEvent,
  TMainState,
  TPermissionUpdateReason,
} from '../constants/main';
import { TSession } from './tsession';
import { TState } from './tstate';

interface TMemberHandUpStatus {
  handUp: boolean;
  handUpActive?: boolean;
  handUpTimes: number;
  handUpTimestamp?: number;
  handDownTimestamp?: number;
  timer?: number | NodeJS.Timeout | any;
}

interface TMemberHandUpStatusUpdateParams extends TMemberHandUpStatus{
  userId: string;
  userName: string;
}

export class TStore extends TModule {
  /**
   * 返回该类实例
   */
  public static get instance(): TStore {
    return this.getInstance();
  }

  private _handUpActiveTimeout = 3000;
  private _handUpStatus = new Map<string, TMemberHandUpStatus>();
  private _handUpMemberList: TMemberInfo[] = [];
  private _memberListMergeTimer: any = 0; // 聚合调用定时器
  private _memberListRetryCount = 0; // 请求次数
  private _memberListFilter: TMemberListFilter = {
    page: 1,
    limit: 0,
    type: TMemberType.All,
    keyword: '',
  };
  private _memberList: TMemberInfo[] = [];

  public init() {
    TState.instance.registerState(TMainState.Hand_Up_Member_List, '举手成员列表', this._handUpMemberList, [this.constructor.name]);
    TState.instance.registerState(TMainState.Member_List, '成员列表', this._memberList, [this.constructor.name]);
    TState.instance.registerState(TMainState.Member_List_Page_Count, '成员列表页数', 1, [this.constructor.name]);
    TState.instance.registerState(TMainState.Member_List_Total_Member_Count, '成员列表成员总数', 0, [this.constructor.name]);
    TState.instance.registerState(TMainState.Member_List_Offline_Member_Count, '成员列表离线成员总数', 0, [this.constructor.name]);
    TState.instance.registerState(TMainState.HandUp_Count, '举手人数', 0, [this.constructor.name]);
    TState.instance.registerState(TMainState.Chat_Permission, '文字聊天权限', true, [this.constructor.name]);
    TState.instance.registerState(TMainState.Hand_Up, '是否举手', false, [this.constructor.name]);

    // 确保成员加入时，_updateMemberList可以执行下去，花名册总数可以正常更新
    if (TMain.instance.isTeacher() || TMain.instance.isAssistant() || TMain.instance.isSupervisor()) {
      this.updateMemberListFilter({
        page: 1,
        limit: 1,
        type: TMemberType.All,
        keyword: '',
      });
    }
    TEvent.instance.on(TMainEvent.Member_Join, (userId: string) => {
      this._updateMemberList();
    });
    const removeHandUpCallback = async ({ userId }: { userId: string }) => {
      if (!this._handUpMemberList.find(member => member.userId === userId)) {
        return;
      }
      await this.removeHandUpMember([userId]);
      await this.getHandUpMemberList({
        page: 1,
        limit: 50,
      });
    };
    TEvent.instance.on(TMainEvent.Member_Stage_Up, removeHandUpCallback);
    TEvent.instance.on(TMainEvent.Member_Stage_Down, removeHandUpCallback);
    TEvent.instance.on(TMainEvent.Member_Exit, (userId: string) => {
      this._updateMemberList();
      // 退房后从举手列表删除
      const index = this._handUpMemberList.findIndex(member => member.userId === userId);
      if (index !== -1) {
        this._handUpMemberList.splice(index, 1);
        this._notifyHandUpMemberListUpdated();
      }
    });
    TEvent.instance.on(TMainEvent.Member_Info_Update, (memberInfo: TMemberInfo) => {
      this.notifyChatPermission([memberInfo]);
      if (this._tryUpdateListItemByUserId(this._handUpMemberList, memberInfo.userId, memberInfo)) {
        this._notifyHandUpMemberListUpdated();
      }
      if (this._tryUpdateListItemByUserId(this._memberList, memberInfo.userId, memberInfo)) {
        this._notifyMemberListUpdated();
      }
    });
    TEvent.instance.on(TMainEvent.Member_Hand_Up_Update, (data) => {
      this._updateMemberHandUpList(data);
    });
    TEvent.instance.on(
      TMainEvent.Permission_Update,
      (permissionList: TPermissionInfo[], reason: TPermissionUpdateReason) => {
        // 处理举手列表
        let handUpListChanged = false;
        for (let index = 0; index < this._handUpMemberList.length; ++index) {
          const permission = TMain.instance.getPermission(this._handUpMemberList[index].userId).serialize();
          if (this._tryUpdateListItemByIndex(this._handUpMemberList, index, permission)) {
            handUpListChanged = true;
          }
        }
        if (handUpListChanged) {
          this._notifyHandUpMemberListUpdated();
        }
        // 处理成员列表
        let memberListChanged = false;
        for (let index = 0; index < this._memberList.length; ++index) {
          const permission = TMain.instance.getPermission(this._memberList[index].userId).serialize();
          if (this._tryUpdateListItemByIndex(this._memberList, index, permission)) {
            memberListChanged = true;
          }
        }
        if (memberListChanged) {
          this._notifyMemberListUpdated();
        }
      },
    );
    if (!TMain.instance.isStudent()) {
      this.getHandUpMemberList({
        page: 1,
        limit: 50,
      }).then((res) => {
        this.updateHandUpMemberList(res.members as unknown as TMemberInfo[]);
        return res;
      });
    }
  }

  public updateHandUpMemberList(memberList: TMemberInfo[]) {
    this._handUpMemberList = memberList;
    this._notifyHandUpMemberListUpdated();
  }
  public getHandUpMemberList(filter: THandUpListFilter) {
    const token = TSession.instance.getToken();
    const classId = TSession.instance.getClassId();
    return TBusinessMember.instance.getHandUpList(classId, filter, token)
      .then((res) => {
        this.updateHandUpMemberList(res.members as unknown as TMemberInfo[]);
        return res;
      });
  }

  public clearHandUpMemberList() {
	  const token = TSession.instance.getToken();
	  const classId = TSession.instance.getClassId();
	  return TBusinessMember.instance.clearHandUpList(classId, token).then(() => {
		  this.updateHandUpMemberList([]);
	  });
  }


  public removeHandUpMember(userIdList: string[]) {
    const token = TSession.instance.getToken();
    const classId = TSession.instance.getClassId();
    return TBusinessMember.instance.removeHandUpMember(classId, userIdList, token);
  }

  public updateMemberListFilter(filter: TMemberListFilter) {
    this._memberListFilter = Object.assign(this._memberListFilter, filter);
    this._memberList = [];
    this._memberListRetryCount = 0; // 主动请求，重置重试次数
    this._updateMemberList();
  }

  public notifyChatPermission(memberlist: TMemberInfo[]) {
    const userId = TSession.instance.getUserId();
    const index = memberlist.findIndex(member => member.userId === userId);
    if (index >= 0 && index < memberlist.length) {
      const member = memberlist[index];
      const chatFlag = !member.silence;
      if (TState.instance.getState(TMainState.Chat_Permission) !== chatFlag) {
        TMain.instance.reportEvent(chatFlag ? 'un_muted_msg' : 'muted_msg', {});
      }
      TState.instance.setState(TMainState.Chat_Permission, chatFlag, this.constructor.name, true);
    }
  }

  /**
   * 获取类名
   * @private
   */
  protected _getClassName() {
    return 'TStore';
  }

  private _getMemberInfo(userId: string): TMemberInfo {
    return this._memberList.find(item => item.userId === userId);
  }

  private _getMemberHandUpStatus(userId: string, defaultValue?: TMemberHandUpStatus) {
    if (!this._handUpStatus.has(userId)) {
      const value = {
        handUp: defaultValue && defaultValue.handUp !== undefined ? defaultValue.handUp : false,
        handUpActive: defaultValue && defaultValue.handUpActive !== undefined ? defaultValue.handUpActive : false,
        handUpTimes: defaultValue && defaultValue.handUpTimes !== undefined ? defaultValue.handUpTimes : 0,
        handUpTimestamp: defaultValue && defaultValue.handUpTimestamp ? defaultValue.handUpTimestamp : 0,
        handDownTimestamp: defaultValue && defaultValue.handDownTimestamp ? defaultValue.handDownTimestamp : 0,
        timer: defaultValue && defaultValue.timer !== undefined ? defaultValue.timer : 0,
      };
      this._handUpStatus.set(userId, value);
    }
    return this._handUpStatus.get(userId);
  }

  private _tryUpdateListItemByIndex(list: TMemberInfo[], index: number, newState: any) {
    const newItem = Object.assign({}, list[index], newState);
    if (JSON.stringify(list[index]) !== JSON.stringify(newItem)) {
      list.splice(index, 1, newItem);
      this._updateMemberHandUpStatus(newItem);
      return true;
    }
    return false;
  }

  private _tryUpdateListItemByUserId(list: TMemberInfo[], userId: string, newState: any) {
    const index = list.findIndex(member => member.userId === userId);
    return index !== -1 && this._tryUpdateListItemByIndex(list, index, newState);
  }

  private _updateHandUpActiveTimer(userId: string) {
    if (this._getMemberHandUpStatus(userId).timer) {
      clearTimeout(this._getMemberHandUpStatus(userId).timer);
    }
    this._getMemberHandUpStatus(userId).timer = setTimeout(() => {  // 超过活跃时间后取消活跃状态
      const status = this._getMemberHandUpStatus(userId);
      status.handUpActive = false;
      status.timer = 0;
      if (this._tryUpdateListItemByUserId(this._handUpMemberList, userId, { handUpActive: false })) {
        this._notifyHandUpMemberListUpdated();
      }
      if (this._tryUpdateListItemByUserId(this._memberList, userId, { handUpActive: false })) {
        this._notifyMemberListUpdated();
      }
    }, this._handUpActiveTimeout);
  }

  private _updateMemberHandUpList(data: any) {
    console.warn('updateMemberHandUpList:xxxxx', data);
    if (!data) return;
    if (data.hand_ups && data.hand_ups instanceof Array) {
      data.hand_ups.forEach((item: { user_id: any, hand_up_times: any, hand_up_timestamp: any, user_name: any}) => {
        const handupInfo = { userId: item.user_id || '', handUpTimes: item.hand_up_times || 0, handUpTimestamp: item.hand_up_timestamp || 0, userName: item.user_name || '' };
        if (handupInfo.userId !== TSession.instance.getUserId()) {
          // 自己的举手在memberAction已发送，此处忽略
          const status = this._getMemberHandUpStatus(handupInfo.userId);
          // 根据时间戳得到举手状态
          if (status.handUpTimes < handupInfo.handUpTimes && status.handUpTimestamp < handupInfo.handUpTimestamp) {
            const result = Object.assign({}, status, { handUp: true }, handupInfo);
            this._updateMemberHandUpStatus(result);
            // 发送举手通知事件
            const permission = TMain.instance.getPermission(handupInfo.userId);
            TEvent.instance.notify(TMainEvent.Member_Hand_Up, [{
              userId: handupInfo.userId,
              stage: permission.stage,
            }, {}], true, true);
          }
        }
      });
    }
    if (data.hand_downs && data.hand_downs instanceof Array) {
      data.hand_downs.forEach((item: { user_id: any, hand_down_timestamp: any, user_name: any}) => {
        const handDownInfo = { userId: item.user_id || '', handDownTimestamp: item.hand_down_timestamp || 0, userName: item.user_name || '' };
        if (handDownInfo.userId !== TSession.instance.getUserId()) {
          const status = this._getMemberHandUpStatus(handDownInfo.userId);
          if (status.handDownTimestamp < handDownInfo.handDownTimestamp) {
            const result = Object.assign({}, status, { handUp: false, handUpTimes: status.handUpTimes }, handDownInfo);
            this._updateMemberHandUpStatus(result);
            // 发送取消举手通知事件
            const permission = TMain.instance.getPermission(handDownInfo.userId);
            TEvent.instance.notify(TMainEvent.Member_Hand_Up_Cancel, [{
              userId: handDownInfo.userId,
              stage: permission.stage,
            }, {}], true, true);
          }
        }
      });
    }
  }
  private async _updateMemberHandUpStatus(params: TMemberHandUpStatusUpdateParams) {
    const status = this._getMemberHandUpStatus(params.userId);
    const isHandUpTimesUpdated = status.handUpTimes < params.handUpTimes; // 举手次数增加
    // 举手状态放下
    const isHandDown = status.handUpTimes === params.handUpTimes && status.handUp && !params.handUp;
    if (!isHandUpTimesUpdated && !isHandDown) return;

    status.handUp = params.handUp;
    status.handUpTimes = params.handUpTimes;
    status.handUpActive = params.handUp;
    if (params.handUpTimestamp > 0) {
      status.handUpTimestamp = params.handUpTimestamp;
    }
    if (params.handDownTimestamp > 0) {
      status.handDownTimestamp = params.handDownTimestamp;
    }

    if (params.handUp) {
      // 处理举手列表
      let memberInfo: TMemberInfo;
      const index = this._handUpMemberList.findIndex(member => member.userId === params.userId);
      if (index !== -1) {  // 之前已在举手列表，挪到最前面
        memberInfo = this._handUpMemberList.splice(index, 1)[0];
      } else {
        // 没在举手列表，新增，尝试从_memberList拿信息
        memberInfo = this._getMemberInfo(params.userId);
        if (!memberInfo) {
          const permission = TMain.instance.getPermission(params.userId);
          memberInfo = Object.assign(
            new TMemberInfo(),
            permission.serialize(),
            { currentStatus: TMemberStatus.Online },
          );
          if (!memberInfo.userName || memberInfo.userName.length === 0) {
            memberInfo.userName = params.userName.length > 0
              ? params.userName
              : (await TMain.instance.getUserInfo(params.userId)).nickname;
          }
        }
      }
      memberInfo.handUp = true;
      memberInfo.handUpActive = true;
      memberInfo.handUpTimes = params.handUpTimes;
      memberInfo.handUpTimeStamp = params.handUpTimestamp;
      this._handUpMemberList.push(memberInfo);
      this._notifyHandUpMemberListUpdated();
      // 处理成员列表
      if (this._tryUpdateListItemByUserId(this._memberList, params.userId, {
        handUp: true,
        handUpActive: true,
        handUpTimes: params.handUpTimes,
      })) {
        this._notifyMemberListUpdated();
      }
      // 管理举手活跃状态定时器
      this._updateHandUpActiveTimer(params.userId);
    } else {
      // 处理举手列表
      const handUpIndex = this._handUpMemberList.findIndex(member => member.userId === params.userId);
      if (handUpIndex !== -1) {  // 之前已在举手列表，挪到最后面
        const memberInfo = this._handUpMemberList.splice(handUpIndex, 1)[0];
        memberInfo.handUp = false;
        memberInfo.handUpActive = false;
        // this._handUpMemberList.push(memberInfo);
      }
      this._notifyHandUpMemberListUpdated();
      // 处理成员列表
      if (this._tryUpdateListItemByUserId(this._memberList, params.userId, {
        handUp: false,
        handUpActive: false,
      })) {
        this._notifyMemberListUpdated();
      }
    }
  }

  private _notifyHandUpMemberListUpdated() {
    TState.instance.setState(TMainState.Hand_Up_Member_List, this._handUpMemberList, this.constructor.name, false);
  }

  private _notifyMemberListUpdated() {
    TState.instance.setState(TMainState.Member_List, this._memberList, this.constructor.name, false);
    this._notifyHandUp(this._memberList);
  }

  private _notifyHandUp(memberlist: TMemberInfo[]) {
    // 更新举手人数
    let handUpCount = 0; // 举手人数
    memberlist.forEach((member) => {
      if (member.handUp) {
        handUpCount += 1;
      }
      if (member.userId === TSession.instance.getUserId()) {
        const handUp = member.handUp;
        if (TState.instance.getState(TMainState.Hand_Up) !== handUp) {
          TMain.instance.reportEvent(handUp ? 'hand_up' : 'put_down', {});
        }
        TState.instance.setState(TMainState.Hand_Up, handUp, this.constructor.name, true);
      }
    });
    TState.getInstance().setState(TMainState.HandUp_Count, handUpCount, this.constructor.name);
  }

  private _updateMemberList() {
    if (this._memberListFilter.limit <= 0) {
      return;
    }
    if (this._memberListMergeTimer !== 0) {
      return;
    }
    // 列表为空，则马上加载
    const mergeInterval = this._memberList.length === 0 ? 0 : 1000;
    // 聚合500ms内的请求
    this._memberListMergeTimer = setTimeout(() => {
      this._memberListMergeTimer = 0;
      const filter = this._memberListFilter;
      const token = TSession.instance.getToken();
      TBusinessMember.instance.getClassMemberList(
        TSession.instance.getClassId(),
        filter, token,
      )
        .then((result: TGetMemberListResult) => {
          this._memberListRetryCount = 0;
          this._memberList = result.members;
          this._memberList.forEach((member) => {  // 初始化本地存储的举手状态
            const status = this._getMemberHandUpStatus(member.userId, member);
            Object.assign(member, status);  // 把本地存储的举手状态附到成员列表
            if (member.userId === TMain.instance.getClassInfo().teacherId && member.screen === 0) {
              member.screen = 1; // 校正老师的屏幕共享权限
            }
          });
          const memberCount = filter.type === TMemberType.All ? result.total : result.offlineTotal;
          const pageCount = Math.max(1, Math.ceil(memberCount / filter.limit));  // 至少一页
          this._notifyMemberListUpdated();
          TState.instance.setState(TMainState.Member_List_Page_Count, pageCount, this.constructor.name, false);
          TState.instance.setState(
            TMainState.Member_List_Total_Member_Count,
            result.memberNumber,
            this.constructor.name,
            false,
          );
          TState.instance.setState(
            TMainState.Member_List_Offline_Member_Count,
            result.offlineNumber,
            this.constructor.name,
            false,
          );
        })
        .catch((error) => {
          // 1秒后重试
          if (this._memberListRetryCount < 3) {
            this._memberListRetryCount = this._memberListRetryCount + 1;
            setTimeout(this._updateMemberList.bind(this), 1000);
          }
        });
    }, mergeInterval);
  }
}
