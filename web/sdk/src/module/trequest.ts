import i18next from 'i18next';
import axios, {
  AxiosError,
  Method,
} from 'axios';
// #!if MP_ENV === 'true'
import Aegis from 'aegis-mp-sdk';
// #!endif
import { TSession } from './tsession';
import { TEvent } from '../base/tevent';
import { TMainEvent } from '../constants';
import { TUtil } from './tutil_inner';
export enum THttpAction {
  Request,
  Response
}

export class THttpResponse {
  public code = 0;
  public headers: { [key: string]: string } = {};
  public data = '';

  constructor(code: number, headers: { [key: string]: string }, data: string) {
    this.code = code;
    this.headers = headers;
    this.data = data;
  }
}

export class THttpStatistics {
  public requestTimes = 0;  // 请求次数（不含重试）
  public requestTimesSucceed = 0;  // 请求成功次数
  public requestTimesFailed = 0;  // 请求失败次数（不含重试）
  public requestTimesTotal = 0;  // 请求总次数（含重试）
  public requestTimesTotalFailed = 0;  // 请求失败总次数（含重试）
  public responseTimes: {[key: number]: number} = {};
}

type THttpProgressCallback = (action: THttpAction, currentSize: number, totalSize: number) => void;

export class TRequest {
  private static _instance: any = null;

  public static get instance(): TRequest {
    if (TRequest._instance === null) {
      TRequest._instance = new TRequest();
    }
    return TRequest._instance;
  }

  private static _denyList = new Set([
    'ENOTFOUND',
    'ENETUNREACH',

    // SSL errors from https://github.com/nodejs/node/blob/fc8e3e2cdc521978351de257030db0076d79e0ab/src/crypto/crypto_common.cc#L301-L328
    'UNABLE_TO_GET_ISSUER_CERT',
    'UNABLE_TO_GET_CRL',
    'UNABLE_TO_DECRYPT_CERT_SIGNATURE',
    'UNABLE_TO_DECRYPT_CRL_SIGNATURE',
    'UNABLE_TO_DECODE_ISSUER_PUBLIC_KEY',
    'CERT_SIGNATURE_FAILURE',
    'CRL_SIGNATURE_FAILURE',
    'CERT_NOT_YET_VALID',
    'CERT_HAS_EXPIRED',
    'CRL_NOT_YET_VALID',
    'CRL_HAS_EXPIRED',
    'ERROR_IN_CERT_NOT_BEFORE_FIELD',
    'ERROR_IN_CERT_NOT_AFTER_FIELD',
    'ERROR_IN_CRL_LAST_UPDATE_FIELD',
    'ERROR_IN_CRL_NEXT_UPDATE_FIELD',
    'OUT_OF_MEM',
    'DEPTH_ZERO_SELF_SIGNED_CERT',
    'SELF_SIGNED_CERT_IN_CHAIN',
    'UNABLE_TO_GET_ISSUER_CERT_LOCALLY',
    'UNABLE_TO_VERIFY_LEAF_SIGNATURE',
    'CERT_CHAIN_TOO_LONG',
    'CERT_REVOKED',
    'INVALID_CA',
    'PATH_LENGTH_EXCEEDED',
    'INVALID_PURPOSE',
    'CERT_UNTRUSTED',
    'CERT_REJECTED',
    'HOSTNAME_MISMATCH',
  ]);

  private static _isRetryAllowed(err: any): boolean {
    return !TRequest._denyList.has(err && (err.errMsg || err.code));
  }

  private static _isNetworkError(err: any): boolean {
    return (
      !err.data
      && Boolean(err.errMsg || err.code) // Prevents retrying cancelled requests
      && err.errMsg !== 'ECONNABORTED' // Prevents retrying timed out requests
      && this._isRetryAllowed(err)
    ); // Prevents retrying unsafe errors
  }

  private static _isRetryableError(err: any): boolean {
    if (TSession.instance.isMiniProgram()) {
      return (
        (err.errMsg !== 'ECONNABORTED')
        && (!err.data || (err.statusCode >= 500 && err.statusCode <= 599))
      );
    }
    return (
      err.code !== 'ECONNABORTED'
      && (!err.response || (err.response.status >= 500 && err.response.status <= 599))
    );
  };

  private static _getExponentialDelay(retryNumber = 0) {
    const delay = (retryNumber ** 2) * 100;
    const randomSum = delay * 0.2 * Math.random(); // 0-20% of the delay
    return delay + randomSum;
  }

  private static _isNetworkOrRetryableError(err: any) {
    return this._isNetworkError(err) || TRequest._isRetryableError(err);
  }

  public aegis: any = null;

  private _statistics: Map<string, Map<string, THttpStatistics>> = new Map();
  private _lastNetworkErrorTime = 0;
  // #!if MP_ENV === 'true'
  public httpRequestMp(
    method:  'OPTIONS' | 'GET' | 'HEAD' | 'POST' | 'PUT' | 'DELETE' | 'TRACE' | 'CONNECT', url: string,
    headers: { [key: string]: string } = {},
    content = '',
    progressCallback: THttpProgressCallback = null,
    retryTimes = 3,
  ): Promise<THttpResponse> {
    if (!this.aegis) {
      this.aegis = new Aegis({
        id: 'VQgVWpYwaKkLZUMYXq', // 项目ID，即上报id
        uin: wx.getStorageSync('userid'),
        api: {
          apiDetail: true,
        },
        // reportApiSpeed: true, // 接口测速
      });
    }
    return new Promise((resolve, reject) => {
      let retryNumber = 0;
      const applyRequest = () => {
        const requestTime = Date.now();
        wx.request({
          url,
          method,
          header: headers,
          data: content,
          timeout: 100000,
          success: (resp: any) => {
            if (resp.statusCode) {
              let msg = i18next.t('网络异常，请检查网络后，重新进入{{arg_0}} ({{statusCode}})', { arg_0: TSession.instance.getRoleInfo().roomInfo.name, statusCode: resp.statusCode || '' });
              switch (resp.statusCode) {
                case 200:
                  resolve(new THttpResponse(resp.statusCode, resp.header, typeof resp.data === 'string' ? resp.data : JSON.stringify(resp.data)));
                  return;
                case 400:
                  msg = i18next.t('请求信息异常，请尝试重新进入{{arg_0}}，或者联系客服协助解决', { arg_0: TSession.instance.getRoleInfo().roomInfo.name });
                  // this.aegis.report(new Error(JSON.stringify(resp)));
                  break;
                case 401:
                case 403:
                  msg = `${i18next.t('用户信息验证失败，请重新进入{{arg_0}}', { arg_0: TSession.instance.getRoleInfo().roomInfo.name })} (TCIC：${resp.statusCode})`;
                  // this.aegis.report(new Error(JSON.stringify(resp)));
                  break;
                case 500:
                case 501:
                case 502:
                case 503:
                case 504:
                case 505:
                  msg = i18next.t('后台服务正在维护当中，请稍候重新进入{{arg_0}}，或者联系客服了解进展', { arg_0: TSession.instance.getRoleInfo().roomInfo.name });
                  // this.aegis.report(new Error(JSON.stringify(resp)));
                  break;
                default:
              }
              retryNumber += 1;
              const errorInfo = `TCIC http request failed ${retryNumber}/${retryTimes} -> ${Math.ceil(Date.now() - requestTime)}ms`;
              if (retryNumber <= retryTimes && TRequest._isNetworkOrRetryableError(resp)) {
                const delay = TRequest._getExponentialDelay(retryNumber);  // 计算延迟请求时间
                console.warn(`${errorInfo}, retry after ${Math.ceil(delay)}ms`);
                setTimeout(applyRequest, delay);  // 延迟发起重试请求
                return;
              }
              reject(new THttpResponse(resp.statusCode, resp.header, msg));
            }
          },
          fail: (err: any) => {
            if (err.request) {  // 完成了请求，但未收到响应
              reject(new THttpResponse(-1, {}, i18next.t('网络异常，请检查网络后，重新进入{{arg_0}}', { arg_0: TSession.instance.getRoleInfo().roomInfo.name })));
            } else {  // 请求发送出错
              // this.aegis.report(new Error(JSON.stringify(err)));
              reject(new THttpResponse(-2, {}, i18next.t('网络异常，请检查网络后，重新进入{{arg_0}}', { arg_0: TSession.instance.getRoleInfo().roomInfo.name })));
            }
            console.error('TCIC http request failed', retryNumber, retryTimes, JSON.stringify(err));
          },
        });
      };
      applyRequest();
    });
  }
  // #!endif
  public httpRequest(
    method:  'OPTIONS' | 'GET' | 'HEAD' | 'POST' | 'PUT' | 'DELETE' | 'TRACE' | 'CONNECT', url: string,
    headers: { [key: string]: string } = {},
    content = '',
    progressCallback: THttpProgressCallback = null,
    retryTimes = 3,
    withCredentials: boolean | undefined = undefined,
  ): Promise<THttpResponse> {
    // #!if MP_ENV === 'true'
    if (TSession.instance.isMiniProgram()) {
      return this.httpRequestMp(method, url, headers, content, progressCallback, retryTimes);
    }
    // #!endif
    return new Promise((resolve, reject) => {
      let retryNumber = 0;
      // 创建axios实例
      const axiosInstance = axios.create({
        responseType: 'json',
        onUploadProgress(e) {
          progressCallback && progressCallback(THttpAction.Request, e.loaded, e.total);
        },
        onDownloadProgress(e) {
          progressCallback && progressCallback(THttpAction.Response, e.loaded, e.total);
        },
        validateStatus(status) {
          return status >= 200 && status < 300;  // 只有2XX返回成功，其它响应走catch逻辑打印信息
        },
        withCredentials,
      });

      // 数据统计
      const parsedUrl = new URL(url);
      let hostStatistics = this._statistics.get(parsedUrl.host);
      if (!hostStatistics) {
        hostStatistics = new Map<string, THttpStatistics>();
        this._statistics.set(parsedUrl.host, hostStatistics);
      }
      let pathStatistics = hostStatistics.get(parsedUrl.pathname);
      if (!pathStatistics) {
        pathStatistics = new THttpStatistics();
        hostStatistics.set(parsedUrl.pathname, pathStatistics);
      }
      pathStatistics.requestTimes += 1;
      const increaseResponseTimes = (code: number) => {
        if (!pathStatistics.responseTimes.hasOwnProperty(code)) {
          pathStatistics.responseTimes[code] = 1;
        } else {
          pathStatistics.responseTimes[code] += 1;
        }
      };

      // mock代码用于测试网络请求处理是否正常
      // if (url.indexOf('heartbeat') !== -1) {
      //   // 生成0-9的随机数
      //   const random = Math.floor(Math.random() * 10);
      //   axiosInstance.interceptors.response.use(function (response) {
      //     // 心跳5/10概率随机请求失败
      //     if (random < 5) {
      //       return {
      //         status: 500,
      //         statusText: '',
      //         data: response.data,
      //         headers: response.headers,
      //         config: response.config,
      //         request: response.request,
      //       };
      //     }
      //     return response;
      //   });
      // }

      const applyRequest = () => {
        pathStatistics.requestTimesTotal += 1;
        const requestTime = window.performance.now();

        if (retryNumber > 0) {
          const urlObj = new URL(url, window.location.origin);
          if (urlObj.searchParams.has('random')) {
            urlObj.searchParams.set('random', ((TUtil.getTimestamp() ?? 0) + 4).toString());
            url = urlObj.toString();
          }
        }

        axiosInstance.request({
          method: method as Method,
          url,
          headers,
          data: content,
          timeout: 100000,
        }).then((resp) => {
          // 请求成果重置为0
          this._lastNetworkErrorTime = 0;
          pathStatistics.requestTimesSucceed += 1;
          increaseResponseTimes(resp.status);
          resolve(new THttpResponse(resp.status, resp.headers, typeof resp.data === 'string' ? resp.data : JSON.stringify(resp.data)));
        }, (err) => {
          // 记录断网
          if (TRequest._isNetworkOrRetryableError(err)) {
            const n = +new Date();
            if (this._lastNetworkErrorTime === 0) {
              this._lastNetworkErrorTime = +new Date();
            } else {
              // 断网超过30秒提示错误
              if (n - this._lastNetworkErrorTime > 30000) {
                console.error('断网超过30秒\n');
                // TEvent.instance.notify(TMainEvent.Show_Msg_Box, {
                //   msgBoxId: '888889',
                //   title: i18next.t('提示'),
                //   message: i18next.t('网络不佳，连接已超时。请检查您的网络情况或尝试重新进入。'),
                //   buttons: [i18next.t('重新进入'), i18next.t('检测网络')],
                //   callback: (cb_index: number) => {
                //     if (cb_index === 0) {
                //       const reload = window.customReload || window.location.reload;
                //       reload();
                //     } else {
                //       window.removeunloadListener();
                //       window.location.href = 'https://class.qcloudclass.com/latest/network.html';
                //       // TCIC.SDK.instance.unInitialize();
                //     }
                //   },
                // });
                reject(new THttpResponse(-1, {}, i18next.t('当前无网络，请检查网络设置')));
                return;
              }
            }
          }
          pathStatistics.requestTimesTotalFailed += 1;
          if (err.response) {
            increaseResponseTimes(err.response.status);
          } else if (err.request) {
            increaseResponseTimes(-1);
          } else {
            increaseResponseTimes(-2);
          }
          retryNumber += 1;
          const errorInfo = `TCIC http request failed ${retryNumber}/${retryTimes} ->
      ${Math.ceil(window.performance.now() - requestTime)}ms`;
          if (retryNumber < retryTimes && TRequest._isNetworkOrRetryableError(err)) {
            const delay = TRequest._getExponentialDelay(retryNumber);  // 计算延迟请求时间
            console.warn(`${errorInfo}, retry after ${Math.ceil(delay)}ms`, JSON.stringify(err));
            setTimeout(applyRequest, delay);  // 延迟发起重试请求
            return;
          }
          pathStatistics.requestTimesFailed += 1;
          if (err.response && err.response.status) {  // 有收到回包，只是状态码不符合预期
            let msg = i18next.t('网络异常，请检查网络后，重新进入{{arg_0}} ({{statusCode}})', { arg_0: TSession.instance.getRoleInfo().roomInfo.name, statusCode: err.response.status });
            switch (err.response.status) {
              case 400:
                msg = i18next.t('请求信息异常，请尝试重新进入{{arg_0}}，或者联系客服协助解决', { arg_0: TSession.instance.getRoleInfo().roomInfo.name });
                break;
              case 401:
              case 403:
                msg = `${i18next.t('用户信息验证失败，请重新进入{{arg_0}}', { arg_0: TSession.instance.getRoleInfo().roomInfo.name })} (TCIC：${err.response.status})`;
                break;
              case 500:
              case 501:
              case 502:
              case 503:
              case 504:
              case 505:
                msg = i18next.t('后台服务正在维护当中，请稍候重新进入{{arg_0}}，或者联系客服了解进展', { arg_0: TSession.instance.getRoleInfo().roomInfo.name });
                break;
              default:
            }
            reject(new THttpResponse(err.response.status, err.response.headers, msg));
          } else {
            if (err.request) {  // 完成了请求，但未收到响应
              reject(new THttpResponse(-1, {}, i18next.t('当前无网络，请检查网络设置')));
            } else {  // 请求发送出错
              reject(new THttpResponse(-2, {}, i18next.t('网络异常，请检查网络后，重新进入{{arg_0}}', { arg_0: TSession.instance.getRoleInfo().roomInfo.name })));
            }
          }
          console.error(errorInfo, JSON.stringify(err));
        });
      };
      applyRequest();
    });
  }

  public printStatistics() {
    Array.from(this._statistics.keys()).forEach((host) => {
      const hostStatistics = this._statistics.get(host);
      Array.from(hostStatistics.keys()).forEach((path) => {
        const pathStatistics = hostStatistics.get(path);
        console.log('host:', host, 'path:', path, pathStatistics);
      });
    });
  }

  public clearStatistics() {
    this._statistics = new Map<string, Map<string, THttpStatistics>>();
  }
}
