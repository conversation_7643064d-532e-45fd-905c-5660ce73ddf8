/* eslint-disable */
// @ts-nocheck
import {ArSdk} from 'tencentcloud-webar';
import {TLoggerModule} from "../base/tmodule";
import { TMain } from './tmain';
import { WebarUtil } from './webar-util';

export class TWebAR extends TLoggerModule {
    private config: Object = {};
    private isReady: boolean = false;
    private streamUpdated: boolean = false;
    private sdk: any = null;
    private loopCheckId: number = 0;
    private loopCheckTimes: number = 0;

    constructor(opt) {
        super();
        this.config = opt;
        this.sdk = new ArSdk(this.config);
        this.sdk.on('ready', () => {
            this.isReady = true;
        });
    }

    destroy() {
        this._info('webar-sdk', 'destroy begin');
        this.sdk?.disable();
        this.sdk?.stop();
        this.sdk?.destroy();
        this.sdk = null;
        this.isReady = false;
        this._info('webar-sdk', 'destroy done');
        clearInterval(this.loopCheckId);
    }

    waitIsReady() {
        if(!this.isReady) {
            return new Promise((resolve) => {
                this.loopCheckId = window.setInterval(()=> {
                    this.loopCheckTimes++;
                    this._info('webar-init', 'setInterval check ready');
                    if (this.isReady) {
                        clearInterval(this.loopCheckId);
                        this._info('webar-init', 'setInterval check done');
                        this.loopCheckTimes = 0;
                        resolve(false);
                    }
                    if (this.loopCheckTimes > 50) {
                        clearInterval(this.loopCheckId);
                        this._warn('webar-init', 'loopCheckTimes limit');
                        this.loopCheckTimes = 0;
                        resolve(false);
                    }
                }, 300);
            });
        } else {
            return Promise.resolve(true);
        }
    }

    async setBeautify(cfg) {
        cfg = cfg || {};
        const opt = Object.assign(this.config, cfg);
        console.log('webar-setBeautify', opt, cfg);
       const isReadybefore = await this.waitIsReady();
       this.sdk.setBeautify(cfg);
       if(isReadybefore && this.streamUpdated) {
        return null;
       } else {
        this.streamUpdated = true;
        return this.sdk.getOutput();
       }
    }

   async setBackground(type: string, url: string) {
    const isReadyBefore =  await this.waitIsReady();
    this.sdk.setBackground({
        type: type,
        src: url
    });
    if(!url && type !== 'blur') {
        this.sdk.setBackground(null);
    }
    if(isReadyBefore && this.streamUpdated) {
        return null
    } else {
        this.streamUpdated = true;
        return this.sdk.getOutput();
    }
    }

    async getSdkOutput() {
      const isReadyBefore = await this.waitIsReady();
      if(isReadyBefore && this.streamUpdated) {
        return this.sdk.getOutput();
      } else {
        return null;
      }
    }

    // 设置虚拟形象
    async setAvatar(effectId: string, url: string) {
        // const list = await this.sdk.getAvatarList('VR');
        this._info('webar-set-avatar', 'set avatar begin effectid='+effectId);
        const isReadyBefore =  await this.waitIsReady();
        if (effectId) {
            this._info('webar-set-avatar', 'set begin, bgurl='+url);
            let currentAvatarType = 'VR'; // 目前产品选的就是vr类型的avatar
            let currentBackground = url || './static/assets/avatar-bg1.jpeg';
            await this.sdk.setAvatar(
                {
                    mode: currentAvatarType,
                    effectId: effectId,
                    backgroundUrl: currentBackground,
                });
            this._info('webar-set-avatar', 'set done');
        } else {
            this._info('webar-set-avatar', 'set begin, null');
            await this.sdk.setAvatar(null);
            this._info('webar-set-avatar', 'set begin, null done');
        }
        if(isReadyBefore && this.streamUpdated) {
            return null;
        } else {
            this.streamUpdated = true;
            return this.sdk.getOutput();
        }
    }
}
