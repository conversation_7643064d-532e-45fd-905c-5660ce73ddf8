import Aegis from 'aegis-web-sdk';

const handler = {
  get(target: any, method: string, receiver: any) {
    return function (...args: any[]) {
      console.error(`Called fakeAegis.${String(method)} with arguments:`, args);
    };
  },
};

const fakeAegis = new Proxy({}, handler);

export const getAegis: () => Aegis = () => {
  if ((window as any)._aegis_) {
    return (window as any)._aegis_;
  }
  return fakeAegis;
};

function stringify(arg: any) {
  try {
    return JSON.stringify(arg);
  } catch (err) {
    return String(arg);
  }
}

function wrapConsole() {
  const rawConsoleError = console.error.bind(console);
  const rawConsoleWarn = console.warn.bind(console);

  console.error = (...args: any[]) => {
    const stack = new Error().stack;
    // const msg = args.map(stringify).join();
    getAegis()?.error({
      trace: stack,
      msg: args,
      ext3: 'console.error',
    });
    // 调用原始的console.error
    rawConsoleError(...args);
  };

  // TODO: Error?.captureStackTrace  only v8
  console.warn = (...args: any[]) => {
    const stack = new Error().stack;
    // const msg = args.map(stringify).join();
    getAegis()?.error({
      trace: stack,
      msg: args,
      ext3: 'console.warn',
    });
    // 调用原始的console.error
    rawConsoleWarn(...args);
  };
}

// wrapConsole();

export const rum = {
  get aegis() {
    return getAegis();
  },
};
