export enum TStateType {
  Unknown = 0,
  Camera = 1,
  Mic = 2,
  Screen = 3,
  Visible = 4,
}

export const TMemberStateConfig = {
  [TStateType.Unknown]: {
    typeName: 'unknown',
  },
  [TStateType.Camera]: {
    typeName: 'camera',
    stateNameMap: {
      0: 'unknown',
      1: 'open',
      2: 'closed',
    },
  },
  [TStateType.Mic]: {
    typeName: 'mic',
    stateNameMap: {
      0: 'unknown',
      1: 'open',
      2: 'closed',
    },
  },
  [TStateType.Screen]: {
    typeName: 'screen',
  },
  [TStateType.Visible]: {
    typeName: 'visible',
  },
};
