/**
 * 课堂布局
 */
export enum TClassLayout {
  /**
   * 顶部布局（互动班课）
   */
  Top = 'top',
  /**
   * 双排布局（互动班课）
   */
  Double = 'double',
  /**
   * 右侧布局（互动班课）
   */
  Right = 'right',
  /**
   * 左侧布局（互动班课）
   */
  Left = 'left',
  /**
   * 三分屏（互动班课、1v1课）
   */
  Three = 'three',
  /**
   * 电脑文档+视频，手机画中画（公开课）
   */
  PicInPic = 'picinpic',
  /**
   * 电脑文档+视频， 手机文档+视频（公开课）
   */
  VideoDoc = 'videodoc',
  /**
   * 纯视频模式（公开课、1v1课）
   */
  Video = 'video',
  /**
   * 视频+聊天模式（大班课纯视频，视频+IM）
   */
  VideoIM = 'videoim',
  /**
   * 九宫格（双师课堂）
   */
  CTGrid9 = 'ctgrid9',
  /**
   * 十六宫格（双师课堂）
   */
  CTGrid16 = 'ctgrid16',
  /**
   * 对等布局
   */
  CTPeer = 'ctpeer',
  /**
   * 放大学生画面
   */
  CTStudent = 'ctstudent',
  /**
   * 放大学生画面
   */
  CTTeacher = 'ctteacher',
  /**
   * 只看老师画面
   */
  CTOnlyTeacher = 'ctonlyteacher',
}

interface TClassMainFrame {
  header: boolean,
  videoWall: boolean,
  videoWrap: boolean,
  board: boolean,
  sideIM: boolean,
}

// 只有header，其他 layout 在这个基础上修改
const defaultMainFrame: TClassMainFrame = {
  header: true,
  videoWall: false, // 纯视频课才用，和 board 互斥
  board: false, // 视频+文档课才用，和 videoWall 互斥
  videoWrap: false, // 视频+文档课才用，和 videoWall 互斥
  sideIM: false,
};

export const TClassLayoutMainFrameMap: { [layout: string]: TClassMainFrame } = {
  default: {
    ...defaultMainFrame,
  },
  [TClassLayout.Top]: {
    ...defaultMainFrame,
    videoWrap: true,
    board: true,
  },
  [TClassLayout.Double]: {
    ...defaultMainFrame,
    videoWrap: true,
    board: true,
  },
  [TClassLayout.Right]: {
    ...defaultMainFrame,
    videoWrap: true,
    board: true,
  },
  [TClassLayout.Left]: {
    ...defaultMainFrame,
    videoWrap: true,
    board: true,
  },
  [TClassLayout.Three]: {
    ...defaultMainFrame,
    videoWrap: true,
    board: true,
    sideIM: true,
  },
  [TClassLayout.PicInPic]: {
    ...defaultMainFrame,
    videoWrap: true,
    board: true,
    // TODO 好像没有这个布局了
  },
  [TClassLayout.VideoDoc]: {
    ...defaultMainFrame,
    videoWrap: true,
    board: true,
    // TODO 好像没有这个布局了
  },
  [TClassLayout.Video]: {
    ...defaultMainFrame,
    videoWall: true,
  },
  [TClassLayout.VideoIM]: {
    ...defaultMainFrame,
    videoWall: true,
    sideIM: true,
  },
  // 后面的双师课堂，不再支持
};
