/**
 * 业务层相关状态
 * @enum {string}
  * @enum {string}
 * @property {'tcic@tmain@message-unread-count'} Message_Unread_Count 消息未读计数
 * @property {'tcic@tmain@hand-up-count'} HandUp_Count 举手人数
 * @property {'tcic@tmain@school-info-ready'} School_Info_Ready 获取学校信息完成
 * @property {'tcic@tmain@class-info-ready'} Class_Info_Ready 获取课堂信息完成
 * @property {'tcic@tmain@joined-class'} Joined_Class 已加入课堂
 * @property {'tcic@tmain@joined-trtc'} Joined_TRTC 加入trtc房间
 * @property {'tcic@tmain@stage-status'} Stage_Status 上台状态
 * @property {'tcic@tmain@video-device'} Video_Device 本地摄像头设备状态，这个好像没用到？
 * @property {'tcic@tmain@video-device-status'} Video_Device_Status 本地摄像头设备状态
 * @property {'tcic@tmain@video-capture'} Video_Capture 本地视频采集是否开启
 * @property {'tcic@tmain@video-publish'} Video_Publish 本地视频推流是否开启
 * @property {'tcic@tmain@reporting-camera-state'} Reporting_Camera_State 上报的 cameraState 值
 * @property {'tcic@tmain@audio-device'} Audio_Device 本地麦克风设备状态，这个好像没用到？
 * @property {'tcic@tmain@audio-device-status'} Audio_Device_Status 本地麦克风设备状态
 * @property {'tcic@tmain@audio-capture'} Audio_Capture 本地音频采集是否开启
  * @property {'tcic@tmain@audio-publish'} Audio_Publish 本地音频推流是否开启
 * @property {'tcic@tmain@reporting-mic-state'} Reporting_Mic_State 上报的 micState 值
 * @property {'tcic@tmain@screen-share'} Screen_Share 屏幕分享状态，0：分享中(全屏或小窗应用)，1：暂停中，2：未开始/已结束
 * @property {'tcic@tmain@vod-play'} Vod_Play 视频播放状态，0：开始，2：结束
 * @property {'tcic@tmain@music-play'} Music_Play 音乐播放状态，3：开始，2：结束
 * @property {'tcic@tmain@reporting-screen-state'} Reporting_Screen_State 上报的 screenState 值
 * @property {'tcic@tmain@sub-camera'} Sub_Camera 辅助摄像头播放状态，0：开始，2：结束
 * @property {'tcic@tmain@class-status'} Class_Status 课堂状态，取值参考 TClassStatus，-1表示未加入课堂
 * @property {'tcic@tmain@class-layout'} Class_Layout 课堂布局
 * @property {'tcic@tmain@class-duration'} Class_Duration 课堂持续时间，单位秒；< 0:距离上课时间；== 0:到上课时间未开始上课|课堂已结束|课堂已过期；> 0:上课中
 * @property {'tcic@tmain@member-count'} Member_Count 课堂成员数量
 * @property {'tcic@tmain@like-count'} Like_Count 课堂点赞数量
 * @property {'tcic@tmain@board-permission'} Board_Permission 白板操作权限
 * @property {'tcic@tmain@chat-permission'} Chat_Permission 文字聊天权限
 * @property {'tcic@tmain@screen-share-permission'} Screen_Share_Permission 屏幕分享权限
 * @property {'tcic@tmain@hand-up'} Hand_Up 举手状态
 * @property {'tcic@tmain@mute-all'} Mute_All 全员静音状态
 * @property {'tcic@tmain@mute-all-video'} Mute_Video_All 全员视频状态
 * @property {'tcic@tmain@silence-all'} Silence_All 全员禁言状态
 * @property {'tcic@tmain@silence-mode'} Silence_Mode 全员发言状态
 * @property {'tcic@tmain@enable-stage'} Enable_Stage 直播课开启全员上麦状态
 * @property {'tcic@tmain@ask-stage-list'} Ask_Stage_List 请求上麦人列表
 * @property {'tcic@tmain@ask-stage-status'} Ask_Stage_Status 发起请求上麦的状态
 * @property {'tcic@tmain@invite-stage-status'} Invite_Stage_Status 老师邀请连麦状态
 * @property {'tcic@tmain@wait-stage-userId'} Wait_Stage_UserId 老师测正在等待的上台用户ID
 * @property {'tcic@tmain@im-logined'} IM_Logined IM已登录
 * @property {'tcic@tmain@im-kicked'} IM_Kicked IM已被踢出
 * @property {'tcic@tmain@im-sig-expired'} IM_SIG_EXPIRED IM签名过期
 * @property {'tcic@tmain@im-cmd-ready'} IM_Cmd_Ready IM信令通道可用
 * @property {'tcic@tmain@im-chat-ready'} IM_Chat_Ready IM聊天通道可用
 * @property {'tcic@tmain@im-msg-list'} IM_Msg_List IM消息列表
 * @property {'tcic@tmain@hand-up-member-list'} Hand_Up_Member_List 举手成员列表
 * @property {'tcic@tmain@member-list'} Member_List 成员列表，由成员列表过滤参数控制展示范围
 * @property {'tcic@tmain@member-list-page-count'} Member_List_Page_Count 成员列表总页数，由当前过滤出的成员总数及分页大小决定
 * @property {'tcic@tmain@member-list-total-member-count'} Member_List_Total_Member_Count 成员列表成员总数
 * @property {'tcic@tmain@member-list-offline-member-count'} Member_List_Offline_Member_Count 成员列表离线成员总数
 * @property {'tcic@tmain@join-quit-tips'} Join_Quit_Tips IM消息列表内是否展示成员进出房通知
 * @property {'tcic@tmain@hand-up-tips'} Hand_Up_Tips IM消息列表内是否展示成员举手
 * @property {'tcic@tmain@board-create'} Board_Create 白板已创建
 * @property {'tcic@tmain@board-init'} Board_Init 白板已初始化
 * @property {'tcic@tmain@board-ready'} Board_Ready 白板已可用
 * @property {'tcic@tmain@network-broken'} Network_Broken 网络异常
 * @property {'tcic@tmain@class-visible'} Class_Visible 课堂是否可见（移动端切前后台，桌面端最大最小化）
 * @property {'tcic@tmain@vod-time-update'} Vod_Time_Update 播放时间更新
 * @property {'tcic@tmain@vod-time-Seeked'} Vod_Time_Seeked 播放进度跳转
 * @property {'tcic@tmain@device-orientation'} Device_Orientation 设备方向
 * @property {'tcic@tmain@stage-count'} Stage_Count 连麦人数变更
 * @property {'tcic@tmain@rtc-mode'} RTC_Mode RTC(非快直播)模式
 * @property {'tcic@tmain@auto-play-failed'} Auto_Play_Failed  移动端自动播放失败
 */
export enum TMainState {
  /**
   * 消息未读计数
   */
  Message_Unread_Count = 'tcic@tmain@message-unread-count',

  /**
   * 举手人数
   */
  HandUp_Count = 'tcic@tmain@hand-up-count',
  /**
   * 自动播放失败
   */
  Auto_Play_Failed = 'tcic@tmain@auto-play-failed',

  /**
   * 获取学校信息完成
   */
  School_Info_Ready = 'tcic@tmain@school-info-ready',

  /**
   * 获取课堂信息完成
   */
  Class_Info_Ready = 'tcic@tmain@class-info-ready',

  /**
   * 已加入课堂
   */
  Joined_Class = 'tcic@tmain@joined-class',

  /**
   * 加入trtc房间
   */
  Joined_TRTC = 'tcic@tmain@joined-trtc',

  /**
   * 上台状态
   */
  Stage_Status = 'tcic@tmain@stage-status',

  /**
   * 本地摄像头设备状态，这个好像没用到？
   */
  Video_Device = 'tcic@tmain@video-device',

  /**
   * 本地摄像头设备状态
   */
  Video_Device_Status = 'tcic@tmain@video-device-status',

  /**
   * 本地视频采集是否开启
   */
  Video_Capture = 'tcic@tmain@video-capture',

  /**
   * 本地视频推流是否开启
   */
  Video_Publish = 'tcic@tmain@video-publish',

  /**
   * 上报的 cameraState 值
   */
  Reporting_Camera_State = 'tcic@tmain@reporting-camera-state',

  /**
   * 本地麦克风设备状态，这个好像没用到？
   */
  Audio_Device = 'tcic@tmain@audio-device',

  /**
   * 本地麦克风设备状态
   */
  Audio_Device_Status = 'tcic@tmain@audio-device-status',

  /**
   * 本地音频采集是否开启
   */
  Audio_Capture = 'tcic@tmain@audio-capture',

  /**
   * 本地音频推流是否开启
   */
  Audio_Publish = 'tcic@tmain@audio-publish',

  /**
   * 上报的 micState 值
   */
  Reporting_Mic_State = 'tcic@tmain@reporting-mic-state',

  /**
   * 屏幕分享状态，0：分享中(全屏或小窗应用)，1：暂停中，2：未开始/已结束
   */
  Screen_Share = 'tcic@tmain@screen-share',

  /**
   * 视频播放状态，0：开始，2：结束
   */
  Vod_Play = 'tcic@tmain@vod-play',

  /**
   * 音乐播放状态，3：开始，2：结束
   */
  Music_Play = 'tcic@tmain@music-play',

  /**
   * 上报的 screenState 值
   */
  Reporting_Screen_State = 'tcic@tmain@reporting-screen-state',

  /**
   * 辅助摄像头播放状态，0：开始，2：结束
   */
  Sub_Camera = 'tcic@tmain@sub-camera',

  /**
   * 课堂状态，取值参考 TClassStatus，-1表示未加入课堂
   */
  Class_Status = 'tcic@tmain@class-status',
  /**
   * 课堂布局
   */
  Class_Layout = 'tcic@tmain@class-layout',

  /**
   * 课堂持续时间，单位秒；< 0:距离上课时间；== 0:到上课时间未开始上课|课堂已结束|课堂已过期；> 0:上课中
   */
  Class_Duration = 'tcic@tmain@class-duration',

  /**
   * 课堂成员数量
   */
  Member_Count = 'tcic@tmain@member-count',

  /**
   * 课堂点赞数量
   */
  Like_Count = 'tcic@tmain@like-count',

  /**
   * 白板操作权限
   */
  Board_Permission = 'tcic@tmain@board-permission',

  /**
   * 文字聊天权限
   */
  Chat_Permission = 'tcic@tmain@chat-permission',

  /**
   * 屏幕分享权限
   */
  Screen_Share_Permission = 'tcic@tmain@screen-share-permission',

  /**
   * 举手状态
   */
  Hand_Up = 'tcic@tmain@hand-up',

  /**
   * 全员静音状态
   */
  Mute_All = 'tcic@tmain@mute-all',

  /**
   * 全员视频状态
   */
  Mute_Video_All = 'tcic@tmain@mute-all-video',

  /**
   * 全员禁言状态
   */
  Silence_All = 'tcic@tmain@silence-all',

  /**
   * 聊天设置
   */
  Silence_Mode = 'tcic@tmain@silence-mode',

  /**
   * 直播课开启全员上麦状态
   */
  Enable_Stage = 'tcic@tmain@enable-stage',

  /**
   * 请求上麦人列表
   */
  Ask_Stage_List = 'tcic@tmain@ask-stage-list',

  /**
   * 发起请求上麦的状态
   */
  Ask_Stage_Status = 'tcic@tmain@ask-stage-status',

  /**
   * 老师邀请连麦状态
   */
  Invite_Stage_Status = 'tcic@tmain@invite-stage-status',

  /**
   * 老师测正在等待的上台用户ID
   */
  Wait_Stage_UserId = 'tcic@tmain@wait-stage-userId',

  /**
   * IM已登录
   */
  IM_Logined = 'tcic@tmain@im-logined',

  /**
   * IM已被踢出
   */
  IM_Kicked = 'tcic@tmain@im-kicked',
  /**
   * IM签名过期
   */
  IM_SIG_EXPIRED = 'tcic@tmain@im-sig-expired',

  /**
   * IM信令通道可用
   */
  IM_Cmd_Ready = 'tcic@tmain@im-cmd-ready',

  /**
   * IM聊天通道可用
   */
  IM_Chat_Ready = 'tcic@tmain@im-chat-ready',

  /**
   * IM消息列表
   */
  IM_Msg_List = 'tcic@tmain@im-msg-list',

  /**
   * 举手成员列表
   */
  Hand_Up_Member_List = 'tcic@tmain@hand-up-member-list',

  /**
   * 成员列表，由成员列表过滤参数控制展示范围
   */
  Member_List = 'tcic@tmain@member-list',

  /**
   * 成员列表总页数，由当前过滤出的成员总数及分页大小决定
   */
  Member_List_Page_Count = 'tcic@tmain@member-list-page-count',

  /**
   * 成员列表成员总数
   */
  Member_List_Total_Member_Count = 'tcic@tmain@member-list-total-member-count',
  /**
   * 成员列表离线成员总数
   */
  Member_List_Offline_Member_Count = 'tcic@tmain@member-list-offline-member-count',

  /**
   * IM消息列表内是否展示成员进出房通知
   */
  Join_Quit_Tips = 'tcic@tmain@join-quit-tips',
  /**
   *  IM消息列表内是否展示成员举手
   */
  Hand_Up_Tips = 'tcic@tmain@hand-up-tips',


  /**
   * 白板已创建
   */
  Board_Create = 'tcic@tmain@board-create',

  /**
   * 白板已初始化
   */
  Board_Init = 'tcic@tmain@board-init',

  /**
   * 白板已可用
   */
  Board_Ready = 'tcic@tmain@board-ready',
  /**
   * 网络异常
   */
  Network_Broken = 'tcic@tmain@network-broken',
  /**
   * 课堂是否可见（移动端切前后台，桌面端最大最小化）
   */
  Class_Visible = 'tcic@tmain@class-visible',
  /**
   * 播放时间更新
   */
  Vod_Time_Update = 'tcic@tmain@vod-time-update',
  /**
   * 播放进度跳转
   */
  Vod_Time_Seeked = 'tcic@tmain@vod-time-Seeked',
  /**
   * 设备方向
   */
  Device_Orientation = 'tcic@tmain@device-orientation',
  /**
   * 连麦人数变更
   */
  Stage_Count = 'tcic@tmain@stage-count',
  /**
   * RTC(非快直播)模式
   */
  RTC_Mode = 'tcic@tmain@rtc-mode',
  /**
   * 网络状态
   */
  Network_Quality_Status = 'tcic@tmain@network-quality-status',

  /**
   * 项目核心流程
   */
  Project_Core_Process = 'tcic@tmain@project-core-process',

}
