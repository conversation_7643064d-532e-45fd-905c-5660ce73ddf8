/**
 * 业务层相关事件 EventBusNames
 * @enum {string}
 * @property {'tcic@tmain@after-enter'} After_Enter 进入课堂后
 * @property {'tcic@tmain@class-modify'} Modify_Class 修改课堂信息
 * @property {'tcic@tmain@class-leave'} Leave_Class 离开课堂
 * @property {'tcic@tmain@before-leave'} Before_Leave 离开课堂前
 * @property {'tcic@tmain@after-leave'} After_Leave 离开课堂后
 * @property {'tcic@tmain@kick-out-by-teacher'} Kick_Out_By_Teacher 被老师踢出课堂
 * @property {'tcic@tmain@kick-out-by-another'} Kick_Out_By_Another 账号在其它地方登录导致被踢出课堂
 * @property {'tcic@tmain@kick-out-by-expire'} Kick_Out_By_Expire 账号签名过期导致被踢出课堂
 * @property {'tcic@tmain@permission-update'} Permission_Update 成员权限变更
 * @property {'tcic@tmain@stream-update'} Stream_Update 直播流变更
 * @property {'tcic@tmain@av-add'} AV_Add 成员音视频权限变更-新增
 * @property {'tcic@tmain@av-update'} AV_Update 成员音视频权限变更-变更
 * @property {'tcic@tmain@av-remove'} AV_Remove 成员音视频权限变更-移除
 * @property {'tcic@tmain@member-join'} Member_Join 成员加入
 * @property {'tcic@tmain@member-exit'} Member_Exit 成员退出
 * @property {'tcic@tmain@Member_Info_Update'} Member_Info_Update 成员信息更新
 * @property {'tcic@tmain@member-hand-up'} Member_Hand_Up 成员举手
 * @property {'tcic@tmain@member-hand-up-cancel'} Member_Hand_Up_Cancel 取消举手
 * @property {'tcic@tmain@member-hand-up-update'} Member_Hand_Up_Update 举手状态更新
 * @property {'tcic@tmain@question-valid'} Question_Valid 问题有效
 * @property {'tcic@tmain@question-new'} Question_Begin 开始提问
 * @property {'tcic@tmain@question-end'} Question_End 结束提问
 * @property {'tcic@tmain@question-abandon'} Question_Abandon 中止答题
 * @property {'tcic@tmain@question-close'} Question_Close 关闭答题
 * @property {'tcic@tmain@question-been-answered'} Question_Been_Answered 有学生回答问题
 * @property {'tcic@tmain@mobile-soft-board'} Soft_Keyboard 软键盘弹出
 * @property {'tcic@tmain@recv_custom_msg'} Recv_Custom_Msg 收到自定义消息
 * @property {'tcic@tmain@recv_member_action'} Recv_Member_Action 老师操作了学生权限
 * @property {'tcic@tmain@show-msg-box'} Show_Msg_Box 显示对话框
 * @property {'tcic@tmain@close-msg-box'} Close_Msg_Box 关闭对话框
 * @property {'tcic@tmain@task-updated'} Task_Updated 任务更新
 * @property {'tcic@tmain@app-resized'} App_Resized 应用大小变化
 * @property {'tcic@tmain@try-reload-resource'} Try_Reload_Resource 尝试重新加载资源
 * @property {'tcic@tmain@load-resource-error'} Load_Resource_Error 加载资源失败
 * @property {'tcic@tmain@error'} Error 发生错误(影响主线流程)
 * @property {'tcic@tmain@tim-error'} Tim_Error 发生Tim错误
 * @property {'tcic@tmain@tim-error-timeout'} Tim_Error_Timeout 发生Tim错误超时
 * @property {'tcic@tmain@warn'} Warn 发生失败(不影响主线流程)
 * @property {'tcic@tmain@back-pressed'} Back_Pressed 用户点击返回按钮
 * @property {'tcic@tmain@version-update'} Version_Update 版本更新
 * @property {'tcic@tmain@watermark-update'} WaterMark_Update 水印更新
 * @property {'tcic@tmain@stage-loop-update'} Stage_Loop 循环上台状态变更
 * @property {'tcic@tmain@recv-im-msgs'} Recv_IM_Msgs 收到IM消息
 * @property {'tcic@tmain@recv-im-msgs-only-message-list'} Recv_IM_Msgs_Only_Message_List 收到IM消息仅进入消息列表
 * @property {'tcic@tmain@delete-im-msgs'} Delete_IM_Msgs 超过IM最大存储数量，删除最老的IM消息
 * @property {'tcic@tmain@update-im-msgs'} Update_IM_Msgs 更新IM消息
 * @property {'tcic@tmain@recv-custom-im-msg'} Recv_Custom_IM_Msg 收到自定义IM消息
 * @property {'tcic@tmain@translate-msg-notify'} Translate_Msg_Notify 翻译消息通知
 * @property {'tcic@tmain@electron-pause-screen-share'} Electron_Screen_Share_Pause electron主进程发来的屏幕共享被动暂停消息
 * @property {'tcic@tmain@electron-resume-screen-share'} Electron_Screen_Share_Resume electron主进程发来的屏幕共享被动恢复消息
 * @property {'tcic@tmain@language-update'} Language_Update 语言更新
 * @property {'tcic@tmain@name-config-update'} Name_Config_Update 名称配置更新
 * @property {'tcic@tmain@log_output'} Log_Output 所有的日志
 */
export enum TMainEvent {
  After_Enter = 'tcic@tmain@after-enter',
  Modify_Class = 'tcic@tmain@class-modify',
  Leave_Class = 'tcic@tmain@class-leave',
  Before_Leave = 'tcic@tmain@before-leave',
  After_Leave = 'tcic@tmain@after-leave',
  Kick_Out_By_Teacher = 'tcic@tmain@kick-out-by-teacher',
  Kick_Out_By_Another = 'tcic@tmain@kick-out-by-another',
  Kick_Out_By_Expire = 'tcic@tmain@kick-out-by-expire',
  Kick_Out_By_Self = 'tcic@tmain@kick-out-by-Self',
  Self_Offline = 'tcic@tmain@self-offline',
  Self_Online = 'tcic@tmain@self-online',
  Permission_Update = 'tcic@tmain@permission-update',
  Stream_Update = 'tcic@tmain@stream-update',
  AV_Add = 'tcic@tmain@av-add',
  AV_Update = 'tcic@tmain@av-update',
  AV_Remove = 'tcic@tmain@av-remove',
  Member_Join = 'tcic@tmain@member-join',
  Member_Exit = 'tcic@tmain@member-exit',
  Member_Info_Update = 'tcic@tmain@Member_Info_Update',
  Member_Hand_Up = 'tcic@tmain@member-hand-up',
  Member_Stage_Up = 'tcic@tmain@member-stage-up',
  Member_Stage_Down = 'tcic@tmain@member-stage-down',
  Member_Hand_Up_Cancel = 'tcic@tmain@member-hand-up-cancel',
  Member_Hand_Up_Update = 'tcic@tmain@member-hand-up-update',
  Question_Valid = 'tcic@tmain@question-valid',
  Question_Begin = 'tcic@tmain@question-new',
  Question_End = 'tcic@tmain@question-end',
  Question_Abandon = 'tcic@tmain@question-abandon',
  Question_Close = 'tcic@tmain@question-close',
  Question_Been_Answered = 'tcic@tmain@question-been-answered',
  Soft_Keyboard = 'tcic@tmain@mobile-soft-board',
  Recv_Custom_Msg = 'tcic@tmain@recv_custom_msg',
  Recv_Member_Action = 'tcic@tmain@recv_member_action',
  Show_Msg_Box = 'tcic@tmain@show-msg-box',
  Close_Msg_Box = 'tcic@tmain@close-msg-box',
  Show_Loading_Box = 'tcic@tmain@show-loading-box',
  Close_Loading_Box = 'tcic@tmain@close-loading-box',
  Task_Updated = 'tcic@tmain@task-updated',
  App_Resized = 'tcic@tmain@app-resized',
  Try_Reload_Resource = 'tcic@tmain@try-reload-resource',
  Load_Resource_Error = 'tcic@tmain@load-resource-error',
  Error = 'tcic@tmain@error',
  Tim_Error = 'tcic@tmain@tim-error',
  Tim_Error_Timeout = 'tcic@tmain@tim-error-timeout',
  Warn = 'tcic@tmain@warn',
  Back_Pressed = 'tcic@tmain@back-pressed',
  Version_Update = 'tcic@tmain@version-update',
  WaterMark_Update = 'tcic@tmain@watermark-update',
  Marquee_Update = 'tcic@tmain@marquee-update',
  Stage_Loop = 'tcic@tmain@stage-loop-update',
  Recv_IM_Msgs = 'tcic@tmain@recv-im-msgs',
  Recv_IM_Msgs_Only_Message_List = 'tcic@tmain@recv-im-msgs-only-message-list',
  Delete_IM_Msgs = 'tcic@tmain@delete-im-msgs',
  Update_IM_Msgs = 'tcic@tmain@update-im-msgs',
  Recv_Custom_IM_Msg = 'tcic@tmain@recv-custom-im-msg',
  Translate_Msg_Notify = 'tcic@tmain@translate-msg-notify',
  Electron_Screen_Share_Pause = 'tcic@tmain@electron-pause-screen-share',
  Electron_Screen_Share_Resume = 'tcic@tmain@electron-resume-screen-share',
  Language_Update = 'tcic@tmain@language-update',
  Name_Config_Update = 'tcic@tmain@name-config-update',
  Error_No_Camera_Permission = 'tcic@tmain@error-no-camera-permission',
  Error_No_Mic_Permission = 'tcic@tmain@error-no-mic-permission',
  Warn_Camera_Closed = 'tcic@tmain@warn-camera-closed',
  Warn_Mic_Closed = 'tcic@tmain@warn-mic-closed',
  Error_Camera_Closed = 'tcic@tmain@error-camera-closed',
  Error_Mic_Closed = 'tcic@tmain@error-mic-closed',
  Error_No_Frame_Content = 'tcic@tmain@no-frame-content',
  Error_No_Speaker_Volume = 'tcic@tmain@no_speaker_volume',
  Error_No_Mic_Volume = 'tcic@tmain@no_mic_volume',
  Core_process_Start_Login_IM = 'tcic@tmain@core_process_start_login_im',
  Core_process_Start_Login_IM_Success = 'tcic@tmain@core_process_login_im_success',
  Core_process_Start_Login_IM_Error = 'tcic@tmain@core_process_login_im_error',
  Core_process_Start_Join_IM_Group = 'tcic@tmain@core_process_join_im_group',
  Core_process_Join_IM_Group_Success = 'tcic@tmain@core_process_join_im_group_success',
  Core_process_Join_IM_Group_Error = 'tcic@tmain@core_process_join_im_group_error',
  Core_process_Start_Init_Board = 'tcic@tmain@core_process_start_init_board',
  Core_process_Start_Init_Board_Success = 'tcic@tmain@core_process_init_board_success',
  Core_process_Start_Init_Board_Error = 'tcic@tmain@core_process_init_board_error',
  Core_process_Start_Join_TRTC = 'tcic@tmain@core_process_start_join_trtc',
  Core_process_Join_TRTC_Success = 'tcic@tmain@core_process_join_trtc_success',
  Core_process_Join_TRTC_Error = 'tcic@tmain@core_process_join_trtc_error',
  AI_CLASS_READY = 'tcic@tmain@ai_class_ready',
  Broken_Network = 'tcic@tmain@broken_network',
  Weak_Network = 'tcic@tmain@weak_network',
  Electron_Window_Maximize_Status = 'tcic@tmain@window_maximize_status',
  Log_Output = 'tcic@tmain@log_output'
}
