/**
 * @fileoverview 不同场景下的配置信息
 * @desc URL 追加 wordscene 参数就能指向对应的场景配置，如：&wordscene=meeting
 */

import { TUtil } from '../module/tutil_inner';
import wordsJson from './words.json';
import wordsJsonMeeting from './wordScenes/meeting.json';
import wordsJsonMedical from './wordScenes/medical.json';

type WordConfig = { [lng: string]: { [key: string]: string } };
const defaultConfig = wordsJson as WordConfig;

// 预置几个，新的用自定义js实现
const wordSceneMap: { [wordScene: string]: WordConfig } = {
  meeting: wordsJsonMeeting, // 会议
  medical: wordsJsonMedical, // 医疗
};

const searchParams = TUtil.parseQuery(window.location.search.substring(1));
const wordScene = searchParams.wordscene || searchParams.boilerplate || ''; // 兼容旧参数 boilerplate
const wordSceneConfig = wordScene && wordSceneMap[wordScene];
console.log('wordScene', wordScene, wordSceneConfig);

const resultConfig: WordConfig = {};
if (wordSceneConfig) {
  Object.entries(defaultConfig).forEach(([lng, lngWords]) => {
    const overrideLngWords = wordSceneConfig[lng]
      || wordSceneConfig[/zh(-\w+)?/g.test(lng) ? 'zh' : 'en']; // 如果没有指定语言，降级到中/英文
    resultConfig[lng] = {
      ...lngWords,
      ...overrideLngWords,
    };
  });
} else {
  Object.assign(resultConfig, defaultConfig);
}

export default resultConfig;
