/**
 * - 接口变更
    - 接口统一入口为 `TCIC.SDK`
    - `finish` 变更为 `unInitialize`
    - `initSubWindow` 变更为 `initSubWindowSelf`
    - 废弃接口 `isClassHandUp`，可通过 `getParams` 获取
    - 废弃接口 `getCustomData`，可通过 `getSchoolInfo` 获取
- 事件变更
    - 所有事件名变更，统一增加前缀，具体参考`TTrtcEvent`、`TIMEvent`、`TMainEvent`
- 模型变更
    - IM 消息返回类型变更为 `TIMMsg`
 * @packageDocumentation
 * @module index
 */

export { ComponentEventListener } from './module/tmain';
export { RoleNameConfig, RoomNameConfig } from './constants/nameConfig';
export { TSimpleDocmentInfo } from './module/business/tbusiness_document';
export { THandUpMemberInfo } from './module/business/tbusiness_member';
export { TSchoolWaterMarkPosition } from './module/business/tbusiness_school';
export { TBoardFitMode, TBoardToolType } from './module/tboard';

export { TResourcePath } from './module/business/tbusiness_rbac';
export {
  TClassMember,
  TGetMemberListResult,
  TMemberActionType,
  TMemberInfo,
  TMemberStatus,
  TMemberType,
  TStreamType,
  TScreenState,
  TJoinType,
  TStreamInfo,
  TMemberRole,
  TGetHandUpListResult,
  TMemberJoinResult,
  TMemberListFilter,
  THandUpListFilter,
  TMemberActionParam,
} from './module/business/tbusiness_member';
export { TErrorInfo } from './module/business/tbusiness';
export {
  TAnswerQuestionParam,
  TAnswerQuestionResult,
  TCreateQuestionParam,
  TCreateQuestionResult,
  TGetQuestionInfoResult,
  TGetQuestionResultResult,
  TGetQuestionStatsResult,
  TGetQuestionsResult,
  TMyAnswersResult,
  TQuestionAnswer,
  TQuestionStatus,
  TQuestionType,
} from './module/business/tbusiness_exam';
export {
  TCommandID,
  TCommandStatus,
  TCommandStatusItem,
  TBaseCommandItem,
  TCommandList,
  TCommandReq,
  TCommandReqResult,
  TGetUserCommandReqResult,
  TGetAllCommandReqResult,
  TStageCommandParam,
  TStageCommandItem,
} from './module/business/tbusiness_command';
export {
  TIMConvType,
  TIMEvent,
  TIMMsgType,
  TMainEvent,
  TMainState,
  TPermissionFlag,
  TPackageType,
  TPermissionUpdateReason,
  TResourceType,
  TPlatform,
  TDevice,
  TClassLayout,
  TDeviceStatus,
  TIMMsg,
  TComponentLayout,
  TPlatformCategory,
} from './constants';
export {
  TMain as SDK,
  TDeviceOrientation,
  TRect,
} from './module/tmain';
export {
  TScreenCaptureSourceType,
  TTrtcDeviceState,
  TTrtcDeviceType,
  TTrtcEvent,
  TTrtcVideoFillMode,
  TTrtcVideoMirrorType,
  TTrtcVideoRotation,
  TTrtcVideoStreamType,
  TTrtcVideoResolution,
  TTrtcDeviceInfo,
  TTrtcSnapshot,
  TTrtcLocalVideoParams,
} from './module/trtc/ttrtc_base';
export {
  TStatisticsEvent,
  TNetworkStatistics,
  TRemoteNetworkStatistics,
  TNetworkQuality,
} from './module/statistics/tstatistics_base';
export { TLiveEvent } from './module/live/live_base';
export { getAegis } from './utils/aegis';
export { TGetUserListResult, TPermissionInfo } from './module/business/tbusiness_user';
export { TSchoolCustomContent, TSchoolWaterMark } from './module/business/tbusiness_school';
export { TIMMsgList, TSendProgressCallback } from './module/tim';
export { NameConfig } from './constants/nameConfig';
export { TRTCScreenCaptureSourceInfo } from 'trtc-electron-sdk';
export { TLevel, TLoggerParam } from './module/tlogger';
export { ComponentEvent } from 'typedoc/dist/lib/utils/component';
export { TEventListener, TEventOptions } from './base/tevent';
export { TStateOptions, TStateUpdateListener } from './module/tstate';
export {
  TBatchDocumentInfo,
  TCreateDocumentResult,
  TDocumentInfo,
  TGetDocumentListParam,
  TGetDocumentListResult,
  TUploadDocumentResult,
  TUploadProgressCallback,
  TUploadReadyCallback,
  TUploadResponseCallback,
} from './module/business/tbusiness_document';
export {
  TClassListInfo,
  TClassStatus,
  TClassSubType,
  TClassInfo,
  TAudienceMixType,
  TAudienceType,
  TCameraResolution,
  TInteractionMode,
  TRoomType,
  TTaskInfo,
  VideoOrientation,
  TClassHistroyMessage,
  TGetTaskListResult,
} from './module/business/tbusiness_class';
export { TBoardInitParams } from './module/tboard';
export { THandupInfo } from './module/business/tbusiness_user';

export { TSchoolInfo } from './module/business/tbusiness_school';
export { TUserInfo } from './module/business/tbusiness_user';

