{
  "include": [
    "src",
  ],
  "exclude": [
    "node_modules",
  ],
  "compilerOptions": {
    "esModuleInterop": true,
    "lib": [
      "ES2016",
      "dom"
    ],
    "tsBuildInfoFile": ".buildCache",
    "incremental": true,
    "skipLibCheck": true,
    "moduleResolution": "node",
    "emitDecoratorMetadata": true,
    "experimentalDecorators": true,
    "noImplicitAny": true,
    "target": "es5",
    "allowJs": false,
    "resolveJsonModule": true,
    "sourceMap": true,
    "downlevelIteration": true,
    "outDir": "dist",
//    "typeRoots": [
//      "./src/typings",
//      "./node_modules/@types"
//    ],
    // "baseUrl": "./",
    // "paths": {
    //   "@root":["../../"]
    // }
  }
}
