## TCIC SDK 接口文档

# 重构变更

- 接口变更
    - 接口统一入口为 `TCIC.SDK`
    - `finish` 变更为 `unInitialize`
    - `initSubWindow` 变更为 `initSubWindowSelf`
    - 废弃接口 `isClassHandUp`，可通过 `getParams` 获取
    - 废弃接口 `getCustomData`，可通过 `getSchoolInfo` 获取
- 事件变更
    - 所有事件名变更，统一增加前缀，具体参考`TTrtcEvent`、`TIMEvent`、`TMainEvent`
- 模型变更
    - IM 消息返回类型变更为 `TIMMsg`


# 主接口类

```js
class SDK {

  /**
   * 获取单例
   * @return {TCIC}  TCIC 单例
   */
  static get instance() {
    return this.getInstance();
  }

  /**
   * 初始化
   * @return {Promise<void>} 初始化结果
   */
  initialize() {
    return TMain.instance.initialize();
  }

  /**
   * 反初始化，一般在退出课堂使用
   * @return {Promise<void>} 反初始化结果
   */
  unInitialize() {
    return TMain.instance.unInitialize();
  }
  ......
}
```

# 接口调用

```js
// 1. 初始化 SDK
TCIC.SDK.instance.initialize();
......
// 2. 其中接口调用
......
// 3. 反初始化 SDK
TCIC.SDK.instance.unInitialize();
```
